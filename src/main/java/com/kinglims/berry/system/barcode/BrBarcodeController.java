package com.kinglims.berry.system.barcode;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.OutputStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.krysalis.barcode4j.HumanReadablePlacement;
import org.krysalis.barcode4j.impl.code128.Code128Bean;
import org.krysalis.barcode4j.output.bitmap.BitmapCanvasProvider;
import org.krysalis.barcode4j.tools.UnitConv;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Controller
@RequestMapping("/berry/system/barcode")
public class BrBarcodeController {
    
    @RequestMapping("/barcode4j/out")
    @Transactional(rollbackFor = Exception.class)
    public void datadictIsValid(HttpServletRequest req, HttpServletResponse res, @RequestParam String barcode) throws IOException {
    	barcode = barcode == null ? "" : barcode.trim();
    	if (barcode.length() < 1) {
    		return;
    	}
    	generateBarCode(barcode, false, res.getOutputStream());
    }
	
	/**
     * 已生成code128条形码为例
     * @param msg           要生成的文本
     * @param hideText      隐藏可读文本
     * @param ous
     */
    private void generateBarCode(String msg, boolean hideText, OutputStream ous) {
        try {
            // 如果想要其他类型的条码(CODE 39, EAN-8...)直接获取相关对象Code39Bean...等等
            Code128Bean bean = new Code128Bean();
            // 分辨率：值越大条码越长，分辨率越高。
            int dpi = 150;
            // 设置两侧是否加空白
            bean.doQuietZone(true);
            // 设置条码每一条的宽度
            // UnitConv 是barcode4j 提供的单位转换的实体类，用于毫米mm,像素px,英寸in,点pt之间的转换
            bean.setModuleWidth(UnitConv.in2mm(3.0f / dpi));

            // 设置文本位置（包括是否显示）
            if (hideText) {
                bean.setMsgPosition(HumanReadablePlacement.HRP_NONE);
            }
            // 设置图片类型
            String format = "image/png";

            BitmapCanvasProvider canvas = new BitmapCanvasProvider(ous, format, dpi, BufferedImage.TYPE_BYTE_BINARY, false, 0);

            // 生产条形码
            bean.generateBarcode(canvas, msg);

            // 结束
            canvas.finish();
            ous.close();
        } catch (IOException ie) {
            ie.getStackTrace();
        }
    }
}
