package com.kinglims.berry.system.basic;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/system/basic")
@Slf4j
@Transactional
public class BrDataDictController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    @RequestMapping("/datadict/isValid")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject datadictIsValid(@RequestBody JSONObject data) {
    	
    	log.info("/berry/system/basic/datadict/isValid ------------ 进入");
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sql = "update BR_DATA_DICT set IS_VALID='Y' where ID=?";
    		
    		for (String id : ids) {
    			int i=updateJdbcTemplate.update(sql, id);
    			log.info(i+"");
    		}
    		
        	log.info("/berry/system/basic/datadict/isValid ------------ 完成");
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    	
    	} catch (Exception e) {

        	log.info("/berry/system/basic/datadict/isValid ------------ 异常");
    		log.info("/berry/system/basic/datadict/isValid", e);
    		log.error("/berry/system/basic/datadict/isValid", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    @RequestMapping("/datadict/unValid")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject datadictUnvalid(@RequestBody JSONObject data) {
    	
    	log.info("/berry/system/basic/datadict/unValid ------------ 进入");
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sql = "update BR_DATA_DICT set IS_VALID='N' where ID=?";
    		
    		for (String id : ids) {
      			int i=updateJdbcTemplate.update(sql, id);
    			log.info(i+"");
    		}
    		
        	log.info("/berry/system/basic/datadict/unValid ------------ 完成");
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    	
    	} catch (Exception e) {

        	log.info("/berry/system/basic/datadict/unValid ------------ 异常");
    		log.info("/berry/system/basic/datadict/unValid", e);
    		log.error("/berry/system/basic/datadict/unValid", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    @RequestMapping("/datadict/repeatCheck")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject datadictRepeatCheck(@RequestBody JSONObject data) {
    	
    	log.info("/berry/system/basic/datadict/repeatCheck ------------ 进入");
    	
    	try {
    		String ID = data.getString("ID");
    		String CODE = data.getString("CODE");
    		String ITEM_CODE = data.getString("ITEM_CODE");
    		
    		ID = ID == null ? "" : ID.trim();
    		CODE = CODE == null ? "" : CODE.trim();
    		ITEM_CODE = ITEM_CODE == null ? "" : ITEM_CODE.trim();
    		
    		String sql = "select count(1) from BR_DATA_DICT where CODE=? and ITEM_CODE=?";
    		int count = 1;
    		if (CODE.length() == 0 || ITEM_CODE.length() == 0) {
    			// 未输入验证不通过
    		} else if (ID.length() > 0) {
    			sql += " and ID<>?";
    			count = queryJdbcTemplate.queryForObject(sql, Integer.class, CODE, ITEM_CODE, ID);
    		} else {
    			count = queryJdbcTemplate.queryForObject(sql, Integer.class, CODE, ITEM_CODE);
    		}
        	log.info("/berry/system/basic/datadict/repeatCheck ------------ 完成");
    		
	    	return new CurrResponseResolve(1).put("count", count).put(responseMessageParameter).getData();
    	
    	} catch (Exception e) {

        	log.info("/berry/system/basic/datadict/repeatCheck ------------ 异常");
    		log.info("/berry/system/basic/datadict/repeatCheck", e);
    		log.error("/berry/system/basic/datadict/repeatCheck", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    @RequestMapping("/emailTemplate/isValid")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject emailTemplateIsValid(@RequestBody JSONObject data) {
    	
    	log.info("/berry/system/basic/emailTemplate/isValid ------------ 进入");
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sql = "update BR_EMAIL_TEMPLATE set IS_VALID='Y' where ID=?";
    		
    		for (String id : ids) {
    			int i=updateJdbcTemplate.update(sql, id);
    			log.info(i+"");
    		}
    		
        	log.info("/berry/system/basic/emailTemplate/isValid ------------ 完成");
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    	
    	} catch (Exception e) {

        	log.info("/berry/system/basic/emailTemplate/isValid ------------ 异常");
    		log.info("/berry/system/basic/emailTemplate/isValid", e);
    		log.error("/berry/system/basic/emailTemplate/isValid", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    @RequestMapping("/emailTemplate/unValid")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject emailTemplateUnValid(@RequestBody JSONObject data) {
    	
    	log.info("/berry/system/basic/emailTemplate/unValid ------------ 进入");
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sql = "update BR_EMAIL_TEMPLATE set IS_VALID='N' where ID=?";
    		
    		for (String id : ids) {
      			int i=updateJdbcTemplate.update(sql, id);
    			log.info(i+"");
    		}
    		
        	log.info("/berry/system/basic/emailTemplate/unValid ------------ 完成");
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    	
    	} catch (Exception e) {

        	log.info("/berry/system/basic/emailTemplate/unValid ------------ 异常");
    		log.info("/berry/system/basic/emailTemplate/unValid", e);
    		log.error("/berry/system/basic/emailTemplate/unValid", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    @RequestMapping("/emailTemplate/repeatCheck")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject emailTemplateRepeatCheck(@RequestBody JSONObject data) {
    	
    	log.info("/berry/system/basic/emailTemplate/repeatCheck ------------ 进入");
    	
    	try {
    		String ID = data.getString("ID");
    		String TEMP_CODE = data.getString("TEMP_CODE");
    		
    		ID = ID == null ? "" : ID.trim();
    		TEMP_CODE = TEMP_CODE == null ? "" : TEMP_CODE.trim();
    		
    		String sql = "select count(1) from BR_EMAIL_TEMPLATE where TEMP_CODE=?";
    		int count = 1;
    		if (TEMP_CODE.length() == 0) {
    			// 未输入验证不通过
    		} else if (ID.length() > 0) {
    			sql += " and ID<>?";
    			count = queryJdbcTemplate.queryForObject(sql, Integer.class, TEMP_CODE, ID);
    		} else {
    			count = queryJdbcTemplate.queryForObject(sql, Integer.class, TEMP_CODE);
    		}
        	log.info("/berry/system/basic/emailTemplate/repeatCheck ------------ 完成");
    		
	    	return new CurrResponseResolve(1).put("count", count).put(responseMessageParameter).getData();
    	
    	} catch (Exception e) {

        	log.info("/berry/system/basic/emailTemplate/repeatCheck ------------ 异常");
    		log.info("/berry/system/basic/emailTemplate/repeatCheck", e);
    		log.error("/berry/system/basic/emailTemplate/repeatCheck", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    @RequestMapping("/jkLibType/isValid")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject jkLibTypeIsValid(@RequestBody JSONObject data) {
    	
    	log.info("/berry/system/basic/jkLibType/isValid ------------ 进入");
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sql = "update BR_DICT_JK set IS_VALID='Y' where ID=?";
    		
    		for (String id : ids) {
    			int i=updateJdbcTemplate.update(sql, id);
    			log.info(i+"");
    		}
    		
        	log.info("/berry/system/basic/jkLibType/isValid ------------ 完成");
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    	
    	} catch (Exception e) {

        	log.info("/berry/system/basic/jkLibType/isValid ------------ 异常");
    		log.info("/berry/system/basic/jkLibType/isValid", e);
    		log.error("/berry/system/basic/jkLibType/isValid", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    @RequestMapping("/jkLibType/unValid")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject jkLibTypeUnValid(@RequestBody JSONObject data) {
    	
    	log.info("/berry/system/basic/jkLibType/unValid ------------ 进入");
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sql = "update BR_DICT_JK set IS_VALID='N' where ID=?";
    		
    		for (String id : ids) {
      			int i=updateJdbcTemplate.update(sql, id);
    			log.info(i+"");
    		}
    		
        	log.info("/berry/system/basic/jkLibType/unValid ------------ 完成");
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    	
    	} catch (Exception e) {

        	log.info("/berry/system/basic/jkLibType/unValid ------------ 异常");
    		log.info("/berry/system/basic/jkLibType/unValid", e);
    		log.error("/berry/system/basic/jkLibType/unValid", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    @RequestMapping("/jkLibType/repeatCheck")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject jkLibTypeRepeatCheck(@RequestBody JSONObject data) {
    	
    	log.info("/berry/system/basic/jkLibType/repeatCheck ------------ 进入");
    	
    	try {
    		String ID = data.getString("ID");
    		String SEQUENCE_CODE = data.getString("SEQUENCE_CODE");
    		
    		ID = ID == null ? "" : ID.trim();
    		SEQUENCE_CODE = SEQUENCE_CODE == null ? "" : SEQUENCE_CODE.trim();
    		
    		String sql = "select count(1) from BR_DICT_JK where SEQUENCE_CODE=?";
    		int count = 1;
    		if (SEQUENCE_CODE.length() == 0) {
    			// 未输入验证不通过
    		} else if (ID.length() > 0) {
    			sql += " and ID<>?";
    			count = queryJdbcTemplate.queryForObject(sql, Integer.class, SEQUENCE_CODE, ID);
    		} else {
    			count = queryJdbcTemplate.queryForObject(sql, Integer.class, SEQUENCE_CODE);
    		}
        	log.info("/berry/system/basic/jkLibType/repeatCheck ------------ 完成");
    		
	    	return new CurrResponseResolve(1).put("count", count).put(responseMessageParameter).getData();
    	
    	} catch (Exception e) {

        	log.info("/berry/system/basic/jkLibType/repeatCheck ------------ 异常");
    		log.info("/berry/system/basic/jkLibType/repeatCheck", e);
    		log.error("/berry/system/basic/jkLibType/repeatCheck", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
