package com.kinglims.berry.sxAnalyze;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/sxAnalyze/sxAnalyze/sxTaskOrder")
@Slf4j
public class BrSXTaskOrderController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 添加明细
    @RequestMapping("/addDetails")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject addDetails(@RequestBody JSONObject data, @RequestHeader(name="Authorization")String token) {
    	
    	try {
//    		User user = SysBasic.getUserByTokenRedis(token);
    		
    		String SX_TASK_ID = data.getString("SX_TASK_ID");
    		String SX_TASK_NO = data.getString("SX_TASK_NO");
    		List<String> RK_IDS = (List<String>) data.get("RK_IDS");//入口表IDS
//    		String CREATOR = user.getID();
//    		Date CREATTIME = new Date();
//    		String LASTUPDATOR = CREATOR;
//    		Date LASTUPDATETIME = CREATTIME;
//    		String LOGINCOMPANY = "";
    		
    		String sqlQueryM = "SELECT COUNT(1) FROM BR_SX_TASK WHERE ID=? AND STATUS IN ('草稿', '审核不通过')";
			int countRS = queryJdbcTemplate.queryForObject(sqlQueryM, Integer.class, SX_TASK_ID);
    		if (countRS == 0) {
    			return new CurrResponseResolve(-1).put("msg", "主单不是草稿状态").put(responseMessageParameter).getData();
    		}
    		
    		//更新入口表SX_TASK_ID SX_TASK_MX_ID
    		String updateSQL_RK = "UPDATE BR_MODUAL_SX SET SX_TASK_ID=?, SX_TASK_MX_ID=? WHERE ID=? AND SX_TASK_ID IS NULL";
    		//添加选中数据
    		String insertSQL_SXMX = "INSERT INTO BR_SX_TASK_DETAIL ("
			    				+ " ID"
			    				+ ",SX_TASK_ID"
			    				+ ",LINK_ID"
			    				+ ",FLOWCELL"
			    				+ ",FLOWCELL_ID"
			    				+ ",FLOWCELL_MX_NO"
			    				+ ",SAMPLE_ID"
			    				+ ",SAMPLE_CODE"
			    				+ ",SAMPLE_NAME"
			    				+ ",SAMPLE_TYPE"
			    				+ ",SAMPLE_NUM"
			    				+ ",DATA_NUM"
			    				+ ",LIB_ID"
			    				+ ",LIB_CODE"
			    				+ ")"
			    				+ " SELECT "
			    				+ " ? AS ID"
			    				+ ",? AS SX_TASK_ID"
			    				+ ",ID AS LINK_ID"
			    				+ ",NGS_FLOWCELL AS FLOWCELL"
			    				+ ",NGS_MX_ID AS FLOWCELL_ID"
			    				+ ",LANE_NO AS FLOWCELL_MX_NO"
			    				+ ",SAMPLE_ID"
			    				+ ",SAMPLE_CODE"
			    				+ ",SAMPLE_NAME"
			    				+ ",SAMPLE_TYPE"
			    				+ ",SAMPLE_NUM"
			    				+ ",DATA_NUM"
			    				+ ",LIB_ID"
			    				+ ",LIB_CODE"
			    				+ " FROM BR_MODUAL_SX WHERE ID=?";
    		//更新主单样本数量，数据量 FLAG
    		boolean updateMainFlag = false;
    		for (String RK_ID : RK_IDS) {
    			String SX_TASK_MX_ID = SysBasic.getUUID();
    			int i = updateJdbcTemplate.update(updateSQL_RK, SX_TASK_ID, SX_TASK_MX_ID, RK_ID);
    			if (i > 0) {
    				updateMainFlag = true;
        			updateJdbcTemplate.update(insertSQL_SXMX, SX_TASK_MX_ID, SX_TASK_ID, RK_ID);
    			}
    		}
    		//更新主单样本数量，数据量
    		if (updateMainFlag) {
    			String updateSQL_MAIN = "UPDATE BR_SX_TASK SET"
    					+ " SAMPLE_NUM=(SELECT SUM(SAMPLE_NUM) FROM BR_SX_TASK_DETAIL WHERE SX_TASK_ID=?)"
    					+ ",DATA_NUM=(SELECT SUM(DATA_NUM) FROM BR_SX_TASK_DETAIL WHERE SX_TASK_ID=?)"
    					+ " WHERE ID=?";
    			updateJdbcTemplate.update(updateSQL_MAIN, SX_TASK_ID, SX_TASK_ID, SX_TASK_ID);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/addDetails", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/addDetails", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除明细
    @RequestMapping("/delDetails")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delDetails(@RequestBody JSONObject data) {
    	
    	try {
    		String SX_TASK_ID = data.getString("SX_TASK_ID");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sqlQueryM = "SELECT COUNT(1) FROM BR_SX_TASK WHERE ID=? AND STATUS IN ('草稿', '审核不通过')";
			int countRS = queryJdbcTemplate.queryForObject(sqlQueryM, Integer.class, SX_TASK_ID);
    		if (countRS == 0) {
    			return new CurrResponseResolve(-1).put("msg", "主单不是草稿状态").put(responseMessageParameter).getData();
    		}
    		//删除明细
    		String deleteSQL_MX = "DELETE BR_SX_TASK_DETAIL WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		updateJdbcTemplate.update(deleteSQL_MX, ids.toArray());
    		//更新入口表SX_TASK_ID SX_TASK_MX_ID
    		String updateSQL_RK = "UPDATE BR_MODUAL_SX SET SX_TASK_ID=NULL, SX_TASK_MX_ID=NULL WHERE SX_TASK_MX_ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		updateJdbcTemplate.update(updateSQL_RK, ids.toArray());
    		//更新主单样本数量，数据量
    		String updateSQL_MAIN = "UPDATE BR_SX_TASK SET"
					+ " SAMPLE_NUM=(SELECT SUM(SAMPLE_NUM) FROM BR_SX_TASK_DETAIL WHERE SX_TASK_ID=?)"
					+ ",DATA_NUM=(SELECT SUM(DATA_NUM) FROM BR_SX_TASK_DETAIL WHERE SX_TASK_ID=?)"
					+ " WHERE ID=?";
			updateJdbcTemplate.update(updateSQL_MAIN, SX_TASK_ID, SX_TASK_ID, SX_TASK_ID);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/delDetails", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/delDetails", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除主单
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		//删除主单SQL
    		String deleteSQL_MAIN = "DELETE BR_SX_TASK WHERE ID=? AND STATUS IN ('草稿', '审核不通过')";
    		//删除明细SQL
    		String deleteSQL_MX = "DELETE BR_SX_TASK_DETAIL WHERE SX_TASK_ID=?";
    		//更新入口表SX_TASK_ID SX_TASK_MX_ID
    		String updateSQL_RK = "UPDATE BR_MODUAL_SX SET SX_TASK_ID=NULL, SX_TASK_MX_ID=NULL WHERE SX_TASK_ID=?";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(deleteSQL_MAIN, id);
    			if (i > 0) {
    				// 删除明细表
    				updateJdbcTemplate.update(deleteSQL_MX, id);
    				// 更新入口表SX_TASK_ID SX_TASK_MX_ID
    				updateJdbcTemplate.update(updateSQL_RK, id);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/del", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提交到审核
    @RequestMapping("/confirmToSX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject confirmToSX(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_DZP = "UPDATE BR_SX_TASK"
    						+ " SET STATUS='待指派', APPROVE_BY=NULL, APPROVE_RS=NULL, APPROVE_REMARK=NULL, APPROVE_TIME=NULL"
    						+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")"
    						+ " AND STATUS IN ('草稿', '审核不通过')"
    						+ " AND ANALYSTS_ID IS NULL";
    		
    		String updateSQL_DSH = "UPDATE BR_SX_TASK"
					+ " SET STATUS='待审核', APPROVE_BY=NULL, APPROVE_RS=NULL, APPROVE_REMARK=NULL, APPROVE_TIME=NULL"
					+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")"
					+ " AND STATUS IN ('草稿', '审核不通过')"
					+ " AND ANALYSTS_ID IS NOT NULL";
    		
    		updateJdbcTemplate.update(updateSQL_DZP, ids.toArray());
    		updateJdbcTemplate.update(updateSQL_DSH, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/confirmToSX", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/confirmToSX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 暂停
    @RequestMapping("/pause")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject taskPause(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_SX_TASK SET STATUS='已暂停'"
    				+ " WHERE STATUS='分析中' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/pause", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/pause", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 继续
    @RequestMapping("/continue")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject taskContinue(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_SX_TASK SET STATUS='分析中'"
    						+ " WHERE STATUS='已暂停' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/continue", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/continue", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 待DoubleCheck
    @RequestMapping("/awaitDoubleCheck")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject taskAwaitDoubleCheck(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_SX_TASK SET STATUS='待DoubleCheck'"
    				+ " WHERE STATUS='分析中' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/awaitSend", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/awaitSend", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 退回分析
    @RequestMapping("/gobackToAnalyze")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject taskGobackToAnalyze(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_SX_TASK SET STATUS='分析中'"
    				+ " WHERE STATUS='待DoubleCheck' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/gobackToAnalyze", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/gobackToAnalyze", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 待发送
    @RequestMapping("/awaitSend")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject taskAwaitSend(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_SX_TASK SET STATUS='待发送', DATA_LIBERATE_FLAG='待释放'"
    				+ " WHERE STATUS='待DoubleCheck' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/awaitSend", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/awaitSend", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 已发送: 待反馈
    @RequestMapping("/awaitFeedback")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject taskAwaitFeedback(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_SX_TASK SET STATUS='待反馈'"
    				+ " WHERE STATUS='待发送' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/awaitFeedback", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/awaitFeedback", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 已发送: 已反馈
    @RequestMapping("/alreadyFeedback")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject taskAlreadyFeedback(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_SX_TASK SET STATUS='已反馈'"
    				+ " WHERE STATUS='待反馈' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/alreadyFeedback", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/alreadyFeedback", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 已发送: 已反馈 -> 提交方案
    
    // 批量分派
    @RequestMapping("/assignBatch")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject assignBatch(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String ANALYSTS_ID = data.getString("ANALYSTS_ID");
    		String ANALYSTS_NAME = data.getString("ANALYSTS_NAME");
    		
    		String updateSQL_gaipai_1 = "UPDATE BR_SX_TASK SET ANALYSTS_ID=?, ANALYSTS_NAME=?"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS IN ('待审核', '分析中')";
    		
    		String updateSQL_gaipai_2 = "UPDATE BR_SX_TASK SET ANALYSTS_ID=?, ANALYSTS_NAME=?, STATUS='分析中'"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS='已暂停'";
    		
    		String updateSQL_toDSH = "UPDATE BR_SX_TASK SET ANALYSTS_ID=?, ANALYSTS_NAME=?, STATUS='待审核'"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS='待分派'";
    		
    		List<String> args = new ArrayList<String>();
    		args.add(ANALYSTS_ID);
    		args.add(ANALYSTS_NAME);
    		args.addAll(ids);

    		updateJdbcTemplate.update(updateSQL_gaipai_1, args.toArray());//改派 1: 状态不变
    		updateJdbcTemplate.update(updateSQL_gaipai_2, args.toArray());//改派 2: 状态=分析中
    		updateJdbcTemplate.update(updateSQL_toDSH, args.toArray());//分派: 状态=待审核
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/assignBatch", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/assignBatch", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 数据释放完成
    @RequestMapping("/dataLiberateFinish")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject taskDataLiberateFinish(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_SX_TASK SET DATA_LIBERATE_FLAG='已释放'"
    				+ " WHERE DATA_LIBERATE_FLAG='待释放' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/dataLiberateFinish", e);
			log.error("/berry/sxAnalyze/sxAnalyze/sxTaskOrder/dataLiberateFinish", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
