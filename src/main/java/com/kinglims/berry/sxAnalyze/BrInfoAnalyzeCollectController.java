package com.kinglims.berry.sxAnalyze;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.security.entity.User;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect")
@Slf4j
public class BrInfoAnalyzeCollectController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 已发送
    @RequestMapping("/sendToCSS")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject sendToCSS(@RequestBody JSONObject data, @RequestHeader(name="Authorization")String token) {
    	
    	try {
//    		User user = SysBasic.getUserByTokenRedis(token);
    		
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_INFO_ANALYZE_COLLECT SET MIS_FLAG='已发送', CSS_FLAG='待填写'"
    				+ " WHERE MIS_FLAG='待发送' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/sendToCSS", e);
			log.error("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/sendToCSS", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 已发送 : 撤回
    @RequestMapping("/revokeSendToCSS")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeSendToCSS(@RequestBody JSONObject data, @RequestHeader(name="Authorization")String token) {
    	
    	try {
//    		User user = SysBasic.getUserByTokenRedis(token);
    		
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_INFO_ANALYZE_COLLECT SET MIS_FLAG='待发送', CSS_FLAG=NULL"
    				+ " WHERE MIS_FLAG='已发送' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/revokeSendToCSS", e);
			log.error("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/revokeSendToCSS", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 已反馈: 重填
    @RequestMapping("/resendToCSS")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject resendToCSS(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_INFO_ANALYZE_COLLECT SET MIS_FLAG='已发送',CSS_FLAG='待填写'"
    				+ " WHERE MIS_FLAG='已反馈' AND CSS_FLAG='已提交' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/resendToCSS", e);
			log.error("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/resendToCSS", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 已确认
    @RequestMapping("/confirmToYQR")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject confirmToYQR(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL_STATUS = "UPDATE BR_INFO_ANALYZE_COLLECT SET MIS_FLAG='已确认'"
    				+ " WHERE MIS_FLAG='已反馈' AND ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新状态
    		updateJdbcTemplate.update(updateSQL_STATUS, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/confirmToYQR", e);
			log.error("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/confirmToYQR", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 10x单细胞表面蛋白-信息采集单: 保存蛋白名称
    @RequestMapping("/saveSPR_MX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject saveSPR_MX(@RequestBody JSONObject data) {
    	
    	try {
    		String IDS = data.getString("IDS");
    		String[] idArray = IDS.split(",");
    		String PROTEIN_NAME=data.getString("PROTEIN_NAME");//蛋白名称
    		
    		
    		String updateSQL_tqTaskResult = "UPDATE BR_INFO_ANALYZE_COLLECT_MX"
    				+ " SET PROTEIN_NAME=?"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(idArray.length)+")";
    		
    		int updateFieldCount = 1;//更新字段数
    		Object[] params = new Object[ updateFieldCount + idArray.length ];
    		params[0] = PROTEIN_NAME;//蛋白名
    		for (int i = 0; i < idArray.length; i++) {
    			params[ updateFieldCount + i ] = idArray[i];
    		}
    		
    		updateJdbcTemplate.update(updateSQL_tqTaskResult, params);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/saveSPR_MX", e);
			log.error("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/saveSPR_MX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // XXX信息采集单: 保存组合
    @RequestMapping("/addSPR_GROUP")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject saveSPR(@RequestBody JSONObject data, @RequestHeader(name="Authorization")String token) {
    	
    	try {
    		JSONArray gridData = data.getJSONArray("gridData");
    		String M_ID = data.getString("M_ID");
    		
    		String SAMPLE_ID="";
    		String SAMPLE_CODE="";
    		String SAMPLE_NAME="";
    		int GROUP_NO = 99999;
    		int GROUP_CONTENT_SORT = 99999;
    		
    		Date nowDate = new Date();
    		User user = SysBasic.getUserByTokenRedis(token);
    		
    		String CREATOR = user.getID();//创建人
    		Date CREATTIME = nowDate;//创建时间
    		String LASTUPDATOR = user.getID();//最近修改人
    		Date LASTUPDATETIME = nowDate;//最近修改时间
    		String LOGINCOMPANY = "";//账套
    		
    		String ID = SysBasic.getUUID();
    		
    		// 拼接组合样本信息
    		for (int i=0; i<gridData.size(); i++) {
    			JSONObject gridRow = gridData.getJSONObject(i);
    			SAMPLE_ID += SAMPLE_ID.length()>0 ? ";"+gridRow.getString("SAMPLE_ID") : gridRow.getString("SAMPLE_ID");
    			SAMPLE_CODE += SAMPLE_CODE.length()>0 ? ";"+gridRow.getString("SAMPLE_CODE") : gridRow.getString("SAMPLE_CODE");
    			SAMPLE_NAME += SAMPLE_NAME.length()>0 ? ";"+gridRow.getString("SAMPLE_NAME") : gridRow.getString("SAMPLE_NAME");
    		}
    		
    		// 插入新行
    		String tableName = "BR_INFO_ANALYZE_COLLECT_GROUP";
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(queryJdbcTemplate);
    		Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName(tableName);
    		SysBasic.filterMap(data, metaMap.keySet());//过滤表不存在的字段
    		data.put("ID", ID);
    		data.put("SAMPLE_ID", SAMPLE_ID);
    		data.put("SAMPLE_NAME", SAMPLE_NAME);
    		data.put("SAMPLE_CODE", SAMPLE_CODE);
    		data.put("GROUP_NO", GROUP_NO);
    		data.put("GROUP_CONTENT_SORT", GROUP_CONTENT_SORT);
    		data.put("CREATOR", CREATOR);
    		data.put("CREATTIME", CREATTIME);
    		data.put("LASTUPDATOR", LASTUPDATOR);
    		data.put("LOGINCOMPANY", LOGINCOMPANY);
    		data.put("LASTUPDATETIME", LASTUPDATETIME);
    		data.put("M_ID", M_ID);
    		SysBasic.insertDataByTableMap(updateJdbcTemplate, tableName, data);
    		
    		
    		// 更新序号: 排序查询后，按顺序重新排序号， 1...99999
    		String selectSQL_group = "SELECT ID FROM BR_INFO_ANALYZE_COLLECT_GROUP WHERE M_ID=? ORDER BY GROUP_NO";
    		String updateSQL_group = "UPDATE BR_INFO_ANALYZE_COLLECT_GROUP SET GROUP_NO=?, GROUP_CONTENT_SORT=? WHERE ID=?";
    		List<String> groupIdList = updateJdbcTemplate.queryForList(selectSQL_group, String.class, M_ID);
    		for (int i=0; i<groupIdList.size(); i++) {
    			String groupId = groupIdList.get(i);
    			updateJdbcTemplate.update(updateSQL_group, (i+1), (i+1), groupId);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/addSPR_GROUP", e);
			log.error("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/addSPR_GROUP", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    //保存信息分析单主单
    @RequestMapping("/saveInfosxAnalyze_Comment")
    @Transactional
    public JSONObject saveInfosxAnalyze_Comment(@RequestBody JSONObject data, @RequestHeader(name="Authorization")String token) {
    	log.info("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/saveInfosxAnalyze_Comment 进入");
    	try {
    		String COLLECT_TYPE=data.getString("COLLECT_TYPE");//分析单类型
    		String ID = data.getString("ID");
    		String SPECIES_NAME=data.getString("SPECIES_NAME");//物种中文名
    		String SPECIES_LATIN_NAME=data.getString("SPECIES_LATIN_NAME");//物种拉丁名
    		String REFER_GENE_GROUP_CHAIN=data.getString("REFER_GENE_GROUP_CHAIN");//参考基因组链
    		String NOTE_FILE_URL=data.getString("NOTE_FILE_URL");//注释文件链接
    		String updateSQL_tqTaskResult = "UPDATE BR_INFO_ANALYZE_COLLECT"
    				+ " SET SPECIES_NAME=?"
    				+ ",SPECIES_LATIN_NAME=?"
    				+ ",REFER_GENE_GROUP_CHAIN=?"
    				+ ",NOTE_FILE_URL=?"
    				+ " WHERE ID=?";
    		
    		updateJdbcTemplate.update(updateSQL_tqTaskResult,SPECIES_NAME,SPECIES_LATIN_NAME,REFER_GENE_GROUP_CHAIN,NOTE_FILE_URL,ID);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/saveSPR_MX", e);
			log.error("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/saveSPR_MX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    //删除样本组合
    @RequestMapping("/delSampleGroup")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delSampleGroup(@RequestBody JSONObject data) {
    	log.info("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/delSampleGroup 进入");
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		// 删除样本明细
    		String sqlDEL_DETAIL = "DELETE FROM BR_INFO_ANALYZE_COLLECT_GROUP MX"
    				+ " WHERE MX.ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    			
    		updateJdbcTemplate.update(sqlDEL_DETAIL, ids.toArray());
    		
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/delSampleGroup", e);
			log.error("/berry/sxAnalyze/sxAnalyze/infoAnalyzeCollect/delSampleGroup", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
}
