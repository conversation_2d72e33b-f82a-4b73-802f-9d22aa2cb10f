package com.kinglims.berry.sxAnalyze;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

@Service
@RestController
@RequestMapping("/berry/sxAnalyze/sxAnalyze")
@Slf4j
public class BrProAnalystController {

	@Autowired
    private JdbcTemplate updateJdbcTemplate;
	
	@Autowired
    private JdbcTemplate queryJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    //项目与分析人员维护保存
    @RequestMapping("/proAnalystSave")
    @Transactional
    public JSONObject submitProAnalyst(@RequestBody JSONObject data) {
    	log.info("/berry/sxAnalyze/sxAnalyze/proAnalystSave 进入");
    	
    	String ANALYST_ID = data.getString("ANALYST_ID");
		JSONArray PRO_WEIGHT_IDS = data.getJSONArray("PRO_WEIGHT_IDS");
		
    	try {
    		//保存前先删除项目与分析人员关系，再重新新增关系
    		String delSQL = "DELETE FROM BR_PRO_ANALYST WHERE ANALYST_ID=?";
    		updateJdbcTemplate.update(delSQL, ANALYST_ID);
    		
    		String insertSQL = "INSERT INTO BR_PRO_ANALYST (ID, ANALYST_ID, PRO_WEIGHT_ID, CREATOR, CREATTIME, LASTUPDATOR, LASTUPDATETIME, LOGINCOMPANY)" + 
    				" VALUES (?, ?, ?, NULL, NULL, NULL, NULL, NULL)";
    		
    		for (int i = 0; i < PRO_WEIGHT_IDS.size(); i++) {
    			JSONObject obj = PRO_WEIGHT_IDS.getJSONObject(i);
    			String PRO_WEIGHT_ID = obj.getString("PRO_WEIGHT_ID");
    			
    			// 插入项目与分析人员维护信息表
    			Object[] args1 = { SysBasic.getUUID(), ANALYST_ID, PRO_WEIGHT_ID};
        		updateJdbcTemplate.update(insertSQL, args1);
    		}
        	log.info("/berry/sxAnalyze/sxAnalyze/proAnalystSave  完成");
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/sxAnalyze/sxAnalyze/proAnalystSave", e);
			log.error("/berry/sxAnalyze/sxAnalyze/proAnalystSave", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    //获取项目与分析人员信息
    @RequestMapping("/getProAnalyst")
    @Transactional
    public JSONObject getProAnalyst(@RequestBody JSONObject data) {
    	log.info("/berry/sxAnalyze/sxAnalyze/getProAnalyst 进入");
    	
    	String ANALYST_ID = data.getString("ANALYST_ID");
    	
    	try {
    		
    		String querySQL = "SELECT ID, ANALYST_ID, PRO_WEIGHT_ID FROM BR_PRO_ANALYST WHERE ANALYST_ID=?";
    		List<Map<String, Object>> result = queryJdbcTemplate.queryForList(querySQL, ANALYST_ID);
    		
    		log.info("/berry/sxAnalyze/sxAnalyze/getProAnalyst  完成");
    		
    		return new CurrResponseResolve(1).put(responseMessageParameter).put("data", result).getData();
    		
    	} catch (Exception e) {
    		
    		log.info("/berry/sxAnalyze/sxAnalyze/getProAnalyst", e);
    		log.error("/berry/sxAnalyze/sxAnalyze/getProAnalyst", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
    	}
    }
    
    //删除项目与分析人员信息
    @RequestMapping("/deleteProAnalyst")
    @Transactional
    public JSONObject deleteProAnalyst(@RequestBody JSONObject data) {
    	log.info("/berry/sxAnalyze/sxAnalyze/deleteProAnalyst 进入");
    	
    	List<String> ids = (List<String>) data.get("ANALYST_IDS");
    	
    	try {
    		
    		String deleteSQL = "DELETE FROM BR_PRO_ANALYST WHERE ANALYST_ID=?";
    		for(String id : ids) {
    			updateJdbcTemplate.update(deleteSQL, id);
    		}
    		log.info("/berry/sxAnalyze/sxAnalyze/deleteProAnalyst  完成");
    		
    		return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    		
    	} catch (Exception e) {
    		
    		log.info("/berry/sxAnalyze/sxAnalyze/deleteProAnalyst", e);
    		log.error("/berry/sxAnalyze/sxAnalyze/deleteProAnalyst", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
    	}
    }
}
