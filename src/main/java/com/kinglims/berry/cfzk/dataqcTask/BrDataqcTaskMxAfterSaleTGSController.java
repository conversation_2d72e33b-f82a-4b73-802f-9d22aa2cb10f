package com.kinglims.berry.cfzk.dataqcTask;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/cfzk/dataqcTask/dataqcAfterSaleTGS")
@Slf4j
public class BrDataqcTaskMxAfterSaleTGSController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 提交完成
    @RequestMapping("/submitFinish")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitFinish(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
//    		// 获取状态为“草稿”的ID
//    		String selectSQL_ID = "SELECT ID FROM BR_DATAQC_TASK_LIST"
//    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie( ids.size() )+") AND STATUS='草稿'";
//    		List<String> IDList = queryJdbcTemplate.queryForList(selectSQL_ID, String.class, ids.toArray());
//    		
//    		if (IDList != null && IDList.size() > 0) {
//	    		// 判断明细 -- 明细填写验证
//	    		String selectSQL_checkData = "SELECT COUNT(1) FROM BR_DATAQC_TASK_MX_A_S_TGS"
//	    						+ " WHERE PROJECT_ID IN ("+SysBasic.getQuestionMarkBySzie( IDList.size() )+")"
//	    						+ " AND (LIB_TYPE IS NULL OR DATA_NUM IS NULL OR DATA_NUM<=0)";
//    			int checkData_COUNT = queryJdbcTemplate.queryForObject(selectSQL_checkData, Integer.class, IDList.toArray()); 
//	    		if (checkData_COUNT > 0) {
//	    			return new CurrResponseResolve(1).put("msg", "文库类型不能为空, 数据量必须大于零").put(responseMessageParameter).getData();
//	    		} else {
		    		String sqlUpdate = "UPDATE BR_DATAQC_TASK_LIST SET TASK_FLAG='已完成'"
		    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie( ids.size() )+") AND TASK_FLAG='进行中'";
		    		updateJdbcTemplate.update(sqlUpdate, ids.toArray());
//	    		}
//			}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/cfzk/dataqcTask/dataqcAfterSaleTGS/submitFinish", e);
			log.error("/berry/cfzk/dataqcTask/dataqcAfterSaleTGS/submitFinish", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提交审核
    @RequestMapping("/submitToApprove")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitToApprove(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
//    		// 获取状态为“草稿”的ID
//    		String selectSQL_ID = "SELECT ID FROM BR_DATAQC_TASK_LIST"
//    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie( ids.size() )+") AND STATUS='草稿'";
//    		List<String> IDList = queryJdbcTemplate.queryForList(selectSQL_ID, String.class, ids.toArray());
//    		
//    		if (IDList != null && IDList.size() > 0) {
//	    		// 判断明细 -- 明细填写验证
//	    		String selectSQL_checkData = "SELECT COUNT(1) FROM BR_DATAQC_TASK_MX_A_S_TGS"
//	    						+ " WHERE PROJECT_ID IN ("+SysBasic.getQuestionMarkBySzie( IDList.size() )+")"
//	    						+ " AND (LIB_TYPE IS NULL OR DATA_NUM IS NULL OR DATA_NUM <=0)";
//    			int checkData_COUNT = queryJdbcTemplate.queryForObject(selectSQL_checkData, Integer.class, IDList.toArray()); 
//	    		if (checkData_COUNT > 0) {
//	    			return new CurrResponseResolve(1).put("msg", "文库类型不能为空, 数据量必须大于零").put(responseMessageParameter).getData();
//	    		} else {
		    		String updateSQL_toDSH = "UPDATE BR_DATAQC_TASK_LIST SET TASK_FLAG='待审核'"
		    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie( ids.size() )+")"
		    				+ " AND TASK_FLAG IN ('草稿', '审核不通过')"
		    				+ " AND TASK_HANDLER_ID IS NULL";
		    		String updateSQL_toJXZ = "UPDATE BR_DATAQC_TASK_LIST SET TASK_FLAG='进行中'"
		    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie( ids.size() )+")"
		    				+ " AND TASK_FLAG IN ('草稿', '审核不通过')"
		    				+ " AND TASK_HANDLER_ID IS NOT NULL";
		    		
		    		updateJdbcTemplate.update(updateSQL_toDSH, ids.toArray());
		    		updateJdbcTemplate.update(updateSQL_toJXZ, ids.toArray());
//	    		}
//			}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/cfzk/dataqcTask/dataqcAfterSaleTGS/submitToApprove", e);
			log.error("/berry/cfzk/dataqcTask/dataqcAfterSaleTGS/submitToApprove", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除
    @RequestMapping("/delM")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delM(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String deleteSQL_M = "DELETE FROM BR_DATAQC_TASK_LIST WHERE ID=? AND TASK_FLAG='草稿'";
    		String deleteSQL_MX = "DELETE FROM BR_DATAQC_TASK_MX_A_S_TGS WHERE TASK_ID=?";
    		for (String id : ids) {
        		int i = queryJdbcTemplate.update(deleteSQL_M, id);
        		if (i > 0) {
		    		updateJdbcTemplate.update(deleteSQL_MX, id);
        		}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/cfzk/dataqcTask/dataqcAfterSaleTGS/delM", e);
			log.error("/berry/cfzk/dataqcTask/dataqcAfterSaleTGS/delM", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 明细删除
    @RequestMapping("/delMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delMX(@RequestBody JSONObject data) {
    	
    	try {
    		String TASK_ID = data.getString("TASK_ID");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL = "SELECT COUNT(1) FROM BR_DATAQC_TASK_LIST WHERE ID=? AND TASK_FLAG='草稿'";
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, TASK_ID);
    		if (count == 0) {
    			return new CurrResponseResolve(-1).put("errMsg", "只能删除“草稿”的任务明细").put(responseMessageParameter).getData();
    		}
    		
    		String deleteSQL = "DELETE FROM BR_DATAQC_TASK_MX_A_S_TGS WHERE ID=?";
    		
    		for (String id : ids) {
    			updateJdbcTemplate.update(deleteSQL, id);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/cfzk/dataqcTask/dataqcAfterSaleTGS/delMX", e);
			log.error("/berry/cfzk/dataqcTask/dataqcAfterSaleTGS/delMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 批量修改
    @RequestMapping("/editMxBatch")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject editMxBatch(@RequestBody JSONObject data) {
    	
    	try {
    		String TASK_ID = data.getString("TASK_ID");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL = "SELECT COUNT(1) FROM BR_DATAQC_TASK_LIST WHERE ID=? AND TASK_FLAG IN ('草稿','进行中')";
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, TASK_ID);
    		if (count == 0) {
    			return new CurrResponseResolve(-1).put("errMsg", "只能修改“草稿”的任务明细").put(responseMessageParameter).getData();
    		}
    		
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(updateJdbcTemplate);
    		Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName("BR_DATAQC_TASK_MX_A_S_TGS");
    		
    		SysBasic.filterMap(data, metaMap.keySet());//过滤表不存在的字段
    		
    		for (String id : ids) {
    			data.put("ID", id);
    			SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_DATAQC_TASK_MX_A_S_TGS", data);
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/cfzk/dataqcTask/dataqcAfterSaleTGS/editMxBatch", e);
			log.error("/berry/cfzk/dataqcTask/dataqcAfterSaleTGS/editMxBatch", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}