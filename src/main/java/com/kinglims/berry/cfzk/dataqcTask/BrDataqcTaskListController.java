package com.kinglims.berry.cfzk.dataqcTask;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/cfzk/dataqcTask/taskList")
@Slf4j
public class BrDataqcTaskListController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 批量修改
    @RequestMapping("/editMxBatch")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject editMxBatch(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL = "SELECT COUNT(1) FROM BR_DATAQC_TASK_LIST"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND TASK_FLAG IN ('草稿','进行中')";
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, ids.toArray());
    		if (count == 0) {
    			return new CurrResponseResolve(-1).put("errMsg", "只能修改“草稿, 进行中”的任务明细").put(responseMessageParameter).getData();
    		}
    		
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(updateJdbcTemplate);
    		Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName("BR_DATAQC_TASK_LIST");
    		
    		SysBasic.filterMap(data, metaMap.keySet());//过滤表不存在的字段
    		
    		for (String id : ids) {
    			data.put("ID", id);
    			SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_DATAQC_TASK_LIST", data);
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/cfzk/dataqcTask/taskList/editMxBatch", e);
			log.error("/berry/cfzk/dataqcTask/taskList/editMxBatch", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}