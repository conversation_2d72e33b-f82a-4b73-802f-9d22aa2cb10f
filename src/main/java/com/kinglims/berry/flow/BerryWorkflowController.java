package com.kinglims.berry.flow;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.system.workflow.handler.logic.Modulars;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowMessage;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowUtils;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

@Service
@RestController
@RequestMapping("/berry/workflow")
@Slf4j
public class BerryWorkflowController {

    @Autowired
    private ResponseMessageParameter responseMessageParameter;

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;

    @Autowired
    @Qualifier("write")
    private JdbcTemplate systemUpdateJdbcTemplate;

    @Autowired
    private JdbcTemplate systemQueryJdbcTemplate;
    
    
    @Autowired
    private WorkflowUtils workflowUtils;

    /**
     * 流程提交-项目最终审核提交-项目立项
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/project", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitProject(@RequestBody Map<String,Object> map) {
        int code=1;
        Object[] arrIds = SysBasic.toTranObjectsByList((ArrayList)map.get("arrIds"));
        
        // 重新获取数据库中，状态为“已审核”的数据的ID
        String sql1 = "SELECT ID FROM BR_PROJECT_INFO WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(arrIds.length)+") AND STATUS='审核通过'";
        List<String> ids = queryJdbcTemplate.queryForList(sql1, String.class, arrIds);
        Object[] objects = ids.toArray();
        
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        
        String sqlcode = "SELECT * FROM BR_PROJECT_INFO WHERE ID in ("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listProject=jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcode, objects);
	    
	    List<String> dataMapIdList = new ArrayList<String>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_YY");

	    String message="成功";
	    for (int i = 0; i<listProject.size() && code==1; i++) {
	    	Map<String,Object> projectMap = listProject.get(i);
	    	String PROJECT_ID = projectMap.get("ID").toString();
	    	projectMap.put("PROJECT_ID", PROJECT_ID);
	    	
	    	sqlcode=" SELECT * FROM BR_PROJECT_MX_INFO WHERE PROJECT_ID=?";
	    	
	    	List<Map<String,Object>> listProjectMx=jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcode, PROJECT_ID);
	    	
		    sqlcode="select 'K_WORKFLOW_OBJ_ID' AS KEY,K_WORKFLOW_OBJ_ID from BR_MODUAL_YY where ID=?";
		    for(int j = 0; j<listProjectMx.size() && code==1; j++) {
		    	 Map<String,Object> projectMxMap = listProjectMx.get(j);
		    	String PROJECT_IN_ID = SysBasic.toTranStringByObject(projectMxMap.get("PROJECT_IN_ID"));
		    	if(!"".equals(PROJECT_IN_ID) ) {//上一步流程的模块入口表ID
		    		
		    		// 更新原流程入口表信息
			    	Map<String,Object> resultMap = queryJdbcTemplate.queryForMap("SELECT * FROM BR_MODUAL_YY WHERE ID=?", PROJECT_IN_ID);
			    	resultMap.putAll(projectMap);//项目主单信息
			    	resultMap.putAll(projectMxMap);//项目明细信息
			    	resultMap.put("ID", PROJECT_IN_ID);
			    	
			    	// 判断是否变更新方案
			    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//文库类型:新方案ID
			    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
			    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
			    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
			    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//文库类型:新方案ID -> 模块表:方案ID
			    	}
			    	
			    	SysBasic.filterMap(resultMap, metaMap.keySet());
			    	SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_YY", resultMap);//更新
		    		
			    	dataMapIdList.add(PROJECT_IN_ID);
		    	}
		    }
	    }
        if(code<0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submit(Modulars.YY,dataMapIdList);
            if(wflowMsg.isB()){
                sqlcode="update BR_PROJECT_INFO set STATUS='已提交方案',PROJECT_FLAG='在线' where ID in("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode, objects);
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).put("message",message).getData();
    }
    // 撤回方案: 立项
    @RequestMapping("/revoke/project")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeTQFlow(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> arrIds = (List<String>) data.get("arrIds");
    		String sqlUpdate = "UPDATE BR_PROJECT_INFO SET STATUS='待审核',PROJECT_FLAG=NULL WHERE ID=? and STATUS='已提交方案'";
    		String sqlSELECT_LINKID = "SELECT YY.ID FROM BR_MODUAL_YY YY"
    				+ " WHERE YY.PROJECT_ID=?"
    				+ " AND YY.PROJECT_MX_ID IN (SELECT MX.ID FROM BR_PROJECT_MX_INFO MX WHERE MX.PROJECT_ID=?)"
    				+ " AND YY.ID NOT IN (SELECT MX.PROJECT_IN_ID FROM BR_PROJECT_MX_INFO MX WHERE MX.PROJECT_ID=?)";
    		
    		for (String id : arrIds) {
    			int i = updateJdbcTemplate.update(sqlUpdate, id);
    			if (i > 0) {
    				// 查询提取明细 LINK_ID
    				List<String> linkIdList = updateJdbcTemplate.queryForList(sqlSELECT_LINKID, String.class, id, id, id);
    				WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.YY, linkIdList);
    				if (!wflowMsg.isB()) {
    					SysBasic.rollBack();
    					break;
    				}
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/workflow/revoke/project", e);
			log.error("/berry/workflow/revoke/project", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    
    /**
     * 流程提交-生产任务
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/productTask", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitProductTask(@RequestBody Map<String,Object> map) {
        int code=0;
        Object[] objects=SysBasic.toTranObjectsByList((ArrayList)map.get("objects"));
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        String sqlcode=" SELECT * FROM BR_PRODU_TASK WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
        String sqlcodeMX=" SELECT * FROM BR_PRODU_TASK_MX WHERE TASK_ID=?";
        
        
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listTaskM=jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcode, objects);
	    
	    List<Map<String,Object>> dataMapList=new ArrayList<Map<String,Object>>();
	    List<Object> IDS = new ArrayList<Object>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_PD");
	    
	    for (Map<String,Object> taskM : listTaskM) {
	    	
	    	Object TASK_ID = taskM.get("ID");
	    	List<Map<String,Object>> listTaskMx = jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcodeMX, TASK_ID);
	    	
		    for(Map<String,Object> taskMx:listTaskMx) {
		    	Object LINK_ID=taskMx.get("LINK_ID");
		    	if("".equals(SysBasic.toTranStringByObject(LINK_ID))) {
		    		continue;
		    	}
		    	Map<String,Object> resultMap=jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_MODUAL_PD where ID=?", LINK_ID);
		    	resultMap.putAll(taskM);
		    	resultMap.putAll(taskMx);
		    	
		    	// 判断是否变更新方案
		    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
		    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
		    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
		    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
		    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
		    	}
		    	
		    	SysBasic.filterMap(resultMap, metaMap.keySet());//过滤表不存在的字段
		    	resultMap.put("ID", LINK_ID);//避免ID覆盖
		    	int u=SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_PD", resultMap);//更新
		    	if(u>0) {
		    		code=1;
		    	}else {
		    		code=-1;
		    		break;
		    	}
		    	dataMapList.add(resultMap);
		    }
		    IDS.add(TASK_ID);
	    }
        if(code<=0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submitMap(Modulars.PD,dataMapList);
            if(wflowMsg.isB()){
                sqlcode="UPDATE BR_PRODU_TASK SET TASK_FLAG='已提交方案' WHERE ID in("+SysBasic.getQuestionMarkBySzie(IDS.size())+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode, IDS.toArray());
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).getData();
    }
    // 撤回方案: 生产任务单
    @RequestMapping("/revoke/productTask")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeProductTaskFlow(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String sqlUpdate = "UPDATE BR_PRODU_TASK SET TASK_FLAG='待审核' WHERE ID=? and TASK_FLAG='已提交方案'";
    		String sqlSELECT_LINKID = "SELECT MX.LINK_ID FROM BR_PRODU_TASK_MX MX WHERE MX.TASK_ID=?";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(sqlUpdate, id);
    			if (i > 0) {
    				// 查询提取明细 LINK_ID
    				List<String> linkIdList = updateJdbcTemplate.queryForList(sqlSELECT_LINKID, String.class, id);
    				WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.PD, linkIdList);
    				if (!wflowMsg.isB()) {
    					SysBasic.rollBack();
    					break;
    				}
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/workflow/revoke/productTask", e);
			log.error("/berry/workflow/revoke/productTask", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    /**
     * 流程提交-预处理
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/ready", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitReady(@RequestBody Map<String,Object> map) {
        int code=0;
        Object[] objects=SysBasic.toTranObjectsByList((ArrayList)map.get("objects"));
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        String sqlcode=" SELECT bsr.*"+
        " FROM BR_SAMPLE_READY bsr"+
        " WHERE bsr.ID in ("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listTaskMx=jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcode, objects);
	    
	    List<String> dataMapIdList=new ArrayList<String>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_READY");

	    for(Map<String,Object> taskMxMap:listTaskMx) {
	    	String LINK_ID = SysBasic.toTranStringByObject( taskMxMap.get("LINK_ID") );
	    	if("".equals(LINK_ID)) {
	    		continue;
	    	}
	    	Map<String,Object> resultMap=jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_MODUAL_READY where ID=?", LINK_ID);
	    	resultMap.putAll(taskMxMap);
	    	
	    	// 判断是否变更新方案
	    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
	    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
	    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
	    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
	    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
	    	}
	    	
	    	SysBasic.filterMap(resultMap, metaMap.keySet());//过滤表不存在的字段
	    	resultMap.put("ID", LINK_ID);//避免ID覆盖
	    	int u=SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_READY", resultMap);//更新
	    	if(u>0) {
	    		code=1;
	    	}else {
	    		code=-1;
	    		break;
	    	}
	    	dataMapIdList.add(LINK_ID);
	    }
        if(code<=0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submit(Modulars.READY, dataMapIdList);
            if(wflowMsg.isB()){
                sqlcode="update BR_SAMPLE_READY set Y_FLAG='已提交' where ID in("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode,objects);
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).getData();
    }
    // 撤回方案: 样品预处理
    @RequestMapping("/revoke/ready")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeQCFlow(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String sqlUpdate = "UPDATE BR_SAMPLE_READY SET Y_FLAG='进行中' WHERE ID=? and Y_FLAG='已提交'";
    		String sqlSELECT_LINKID = "SELECT LINK_ID FROM BR_SAMPLE_READY WHERE ID=?";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(sqlUpdate, id);
    			if (i > 0) {
    				// 查询提取明细 LINK_ID
    				List<String> linkIdList = updateJdbcTemplate.queryForList(sqlSELECT_LINKID, String.class, id);
    				WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.READY, linkIdList);
    				if (!wflowMsg.isB()) {
    					SysBasic.rollBack();
    					break;
    				}
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/workflow/revoke/ready", e);
			log.error("/berry/workflow/revoke/ready", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    /**
     * 流程提交-预文库
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/libReady", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitLibReady(@RequestBody Map<String,Object> map) {
        int code=0;
        Object[] objects=SysBasic.toTranObjectsByList((ArrayList)map.get("objects"));
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        String sqlcode=" SELECT bli.*"+
        " FROM BR_LIB_INFO bli"+
        " WHERE bli.ID in ("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listTaskMx=jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcode, objects);
	    
	    List<Map<String,Object>> dataMapList=new ArrayList<Map<String,Object>>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_LIB_Y");

	    for(Map<String,Object> taskMxMap:listTaskMx) {
	    	Object LINK_ID=taskMxMap.get("LINK_ID");
	    	if("".equals(SysBasic.toTranStringByObject(LINK_ID))) {
	    		continue;
	    	}
	    	Map<String,Object> resultMap=jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_MODUAL_LIB_Y where ID=?", LINK_ID);
	    	resultMap.putAll(taskMxMap);
	    	
	    	// 判断是否变更新方案
	    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
	    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
	    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
	    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
	    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
	    	}
	    	
	    	SysBasic.filterMap(resultMap, metaMap.keySet());//过滤表不存在的字段
	    	resultMap.put("ID", LINK_ID);//避免ID覆盖
	    	int u=SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_LIB_Y", resultMap);//更新
	    	if(u>0) {
	    		code=1;
	    	}else {
	    		code=-1;
	    		break;
	    	}
	    	resultMap.remove("LIB_ID");//移除LIB_ID
	    	resultMap.put("LIB_ID", resultMap.get("LIB_Y_ID"));//重置LIB_ID
	    	resultMap.remove("LIB_Y_ID");//移除LIB_Y_ID
	    	dataMapList.add(resultMap);
	    }
        if(code<=0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submitMap(Modulars.LIB_Y,dataMapList);
            if(wflowMsg.isB()){
                sqlcode="update BR_LIB_INFO set LIB_FLAG='已提交' where ID in("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode,objects);
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).getData();
    }
    

    /**
     * 流程提交-终文库
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/libZ", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitLibZ(@RequestBody Map<String,Object> map) {
        int code=0;
        Object[] objects=SysBasic.toTranObjectsByList((ArrayList)map.get("objects"));
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        String sqlcode=" SELECT bli.*"+
        " FROM BR_LIB_INFO bli"+
        " WHERE bli.ID in ("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listTaskMx=jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcode, objects);
	    
	    List<Map<String,Object>> dataMapList=new ArrayList<Map<String,Object>>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_LIB_Z");

	    for(Map<String,Object> taskMxMap:listTaskMx) {
	    	Object LINK_ID=taskMxMap.get("LINK_ID");
	    	if("".equals(SysBasic.toTranStringByObject(LINK_ID))) {
	    		continue;
	    	}
	    	Map<String,Object> resultMap=jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_MODUAL_LIB_Z where ID=?", LINK_ID);
	    	resultMap.putAll(taskMxMap);
	    	
	    	// 判断是否变更新方案
	    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
	    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
	    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
	    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
	    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
	    	}
	    	
	    	SysBasic.filterMap(resultMap, metaMap.keySet());//过滤表不存在的字段
	    	resultMap.put("ID", LINK_ID);//避免ID覆盖
	    	int u=SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_LIB_Z", resultMap);//更新
	    	if(u>0) {
	    		code=1;
	    	}else {
	    		code=-1;
	    		break;
	    	}
	    	resultMap.remove("LIB_ID");//移除LIB_ID
	    	resultMap.put("LIB_ID", resultMap.get("LIB_Z_ID"));//重置LIB_ID
	    	resultMap.remove("LIB_Z_ID");//移除LIB_Z_ID
	    	dataMapList.add(resultMap);
	    }
        if(code<=0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submitMap(Modulars.LIB_Z, dataMapList);
            if(wflowMsg.isB()){
                sqlcode="update BR_LIB_INFO set LIB_FLAG='已提交' where ID in("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode,objects);
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).getData();
    }
    /**
     * 流程提交-捕获预文库
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/libPY", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitLibPY(@RequestBody Map<String,Object> map) {
        int code=0;
        Object[] objects=SysBasic.toTranObjectsByList((ArrayList)map.get("objects"));
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        String sqlcode=" SELECT bli.*"+
        " FROM BR_LIB_INFO bli"+
        " WHERE bli.ID in ("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listTaskMx=jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcode, objects);
	    
	    List<Map<String,Object>> dataMapList=new ArrayList<Map<String,Object>>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_LIB_PY");

	    for(Map<String,Object> taskMxMap:listTaskMx) {
	    	Object LINK_ID=taskMxMap.get("LINK_ID");
	    	if("".equals(SysBasic.toTranStringByObject(LINK_ID))) {
	    		continue;
	    	}
	    	Map<String,Object> resultMap=jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_MODUAL_LIB_PY where ID=?", LINK_ID);
	    	resultMap.putAll(taskMxMap);
	    	
	    	// 判断是否变更新方案
	    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
	    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
	    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
	    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
	    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
	    	}
	    	
	    	SysBasic.filterMap(resultMap, metaMap.keySet());//过滤表不存在的字段
	    	resultMap.put("ID", LINK_ID);//避免ID覆盖
	    	int u=SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_LIB_PY", resultMap);//更新
	    	if(u>0) {
	    		code=1;
	    	}else {
	    		code=-1;
	    		break;
	    	}
	    	resultMap.remove("LIB_ID");//移除LIB_ID
	    	resultMap.put("LIB_ID", resultMap.get("LIB_PY_ID"));//重置LIB_ID
	    	resultMap.remove("LIB_PY_ID");//移除LIB_PY_ID
	    	dataMapList.add(resultMap);
	    }
        if(code<=0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submitMap(Modulars.LIB_PY,dataMapList);
            if(wflowMsg.isB()){
                sqlcode="update BR_LIB_INFO set LIB_FLAG='已提交' where ID in("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode,objects);
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).getData();
    }
    /**
     * 流程提交-NGS文库
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/libNGS", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitLibNGS(@RequestBody Map<String,Object> map) {
        int code=0;
        Object[] objects=SysBasic.toTranObjectsByList((ArrayList)map.get("objects"));
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        String sqlcode=" SELECT bli.*"+
        " FROM BR_LIB_INFO bli"+
        " WHERE bli.ID in ("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listTaskMx=jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcode, objects);
	    
	    List<Map<String,Object>> dataMapList=new ArrayList<Map<String,Object>>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_LIB_N");

	    for(Map<String,Object> taskMxMap:listTaskMx) {
	    	Object LINK_ID=taskMxMap.get("LINK_ID");
	    	if("".equals(SysBasic.toTranStringByObject(LINK_ID))) {
	    		continue;
	    	}
	    	Map<String,Object> resultMap=jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_MODUAL_LIB_N where ID=?", LINK_ID);
	    	resultMap.putAll(taskMxMap);
	    	
	    	// 判断是否变更新方案
	    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
	    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
	    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
	    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
	    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
	    	}
	    	
	    	SysBasic.filterMap(resultMap, metaMap.keySet());//过滤表不存在的字段
	    	resultMap.put("ID", LINK_ID);//避免ID覆盖
	    	int u=SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_LIB_N", resultMap);//更新
	    	if(u>0) {
	    		code=1;
	    	}else {
	    		code=-1;
	    		break;
	    	}
	    	resultMap.remove("LIB_ID");//移除LIB_ID
	    	resultMap.put("LIB_ID", resultMap.get("LIB_N_ID"));//重置LIB_ID
	    	resultMap.remove("LIB_N_ID");//移除LIB_N_ID
	    	dataMapList.add(resultMap);
	    }
        if(code<=0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submitMap(Modulars.LIB_N,dataMapList);
            if(wflowMsg.isB()){
                sqlcode="update BR_LIB_INFO set LIB_FLAG='已提交' where ID in("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode,objects);
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).getData();
    }
    /**
     * 流程提交-TGS文库
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/libTGS", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitLibTGS(@RequestBody Map<String,Object> map) {
        int code=0;
        Object[] objects=SysBasic.toTranObjectsByList((ArrayList)map.get("objects"));
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        String sqlcode=" SELECT bli.*"+
        " FROM BR_LIB_INFO bli"+
        " WHERE bli.ID in ("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listTaskMx=jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcode, objects);
	    
	    List<Map<String,Object>> dataMapList=new ArrayList<Map<String,Object>>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_LIB_T");

	    for(Map<String,Object> taskMxMap:listTaskMx) {
	    	Object LINK_ID=taskMxMap.get("LINK_ID");
	    	if("".equals(SysBasic.toTranStringByObject(LINK_ID))) {
	    		continue;
	    	}
	    	Map<String,Object> resultMap=jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_MODUAL_LIB_T where ID=?", LINK_ID);
	    	resultMap.putAll(taskMxMap);
	    	
	    	// 判断是否变更新方案
	    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
	    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
	    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
	    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
	    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
	    	}
	    	
	    	SysBasic.filterMap(resultMap, metaMap.keySet());//过滤表不存在的字段
	    	resultMap.put("ID", LINK_ID);//避免ID覆盖
	    	int u=SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_LIB_T", resultMap);//更新
	    	if(u>0) {
	    		code=1;
	    	}else {
	    		code=-1;
	    		break;
	    	}
	    	resultMap.remove("LIB_ID");//移除LIB_ID
	    	resultMap.put("LIB_ID", resultMap.get("LIB_T_ID"));//重置LIB_ID
	    	resultMap.remove("LIB_T_ID");//移除LIB_T_ID
	    	dataMapList.add(resultMap);
	    }
        if(code<=0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submitMap(Modulars.LIB_T,dataMapList);
            if(wflowMsg.isB()){
                sqlcode="update BR_LIB_INFO set LIB_FLAG='已提交' where ID in("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode,objects);
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).getData();
    }
    /**
     * 流程提交-Bionano文库
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/libBionano", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitLibBionano(@RequestBody Map<String,Object> map) {
        int code=0;
        Object[] objects=SysBasic.toTranObjectsByList((ArrayList)map.get("objects"));
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        String sqlcode=" SELECT bli.*"+
        " FROM BR_LIB_INFO bli"+
        " WHERE bli.ID in ("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listTaskMx=jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcode, objects);
	    
	    List<Map<String,Object>> dataMapList=new ArrayList<Map<String,Object>>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_LIB_B");

	    for(Map<String,Object> taskMxMap:listTaskMx) {
	    	Object LINK_ID=taskMxMap.get("LINK_ID");
	    	if("".equals(SysBasic.toTranStringByObject(LINK_ID))) {
	    		continue;
	    	}
	    	Map<String,Object> resultMap=jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_MODUAL_LIB_B where ID=?", LINK_ID);
	    	resultMap.putAll(taskMxMap);
	    	
	    	// 判断是否变更新方案
	    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
	    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
	    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
	    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
	    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
	    	}
	    	
	    	SysBasic.filterMap(resultMap, metaMap.keySet());//过滤表不存在的字段
	    	resultMap.put("ID", LINK_ID);//避免ID覆盖
	    	int u=SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_LIB_B", resultMap);//更新
	    	if(u>0) {
	    		code=1;
	    	}else {
	    		code=-1;
	    		break;
	    	}
	    	resultMap.remove("LIB_ID");//移除LIB_ID
	    	resultMap.put("LIB_ID", resultMap.get("LIB_B_ID"));//重置LIB_ID
	    	resultMap.remove("LIB_B_ID");//移除LIB_B_ID
	    	dataMapList.add(resultMap);
	    }
        if(code<=0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submitMap(Modulars.LIB_B,dataMapList);
            if(wflowMsg.isB()){
                sqlcode="update BR_LIB_INFO set LIB_FLAG='已提交' where ID in("+SysBasic.getQuestionMarkBySzie(objects.length)+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode,objects);
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).getData();
    }
    // 撤回方案: 文库
    @RequestMapping("/revoke/lib")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeLibFlow(@RequestBody JSONObject data) {
    	
    	try {
    		String type = data.getString("type");
    		List<String> ids = (List<String>) data.get("ids");
    		String sqlUpdate = "UPDATE BR_LIB_INFO SET LIB_FLAG='构建中' WHERE ID=? and LIB_FLAG='已提交'";
    		String sqlSELECT_LINKID = "SELECT LINK_ID FROM BR_LIB_INFO WHERE ID=?";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(sqlUpdate, id);
    			if (i > 0) {
    				// 查询提取明细 LINK_ID
    				List<String> linkIdList = updateJdbcTemplate.queryForList(sqlSELECT_LINKID, String.class, id);
    				boolean b = false;
    				if ("Y".equalsIgnoreCase(type)) {
    					workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.LIB_Y, linkIdList);
    				} else if ("Z".equalsIgnoreCase(type)) {
    					workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.LIB_Z, linkIdList);
    				} else if ("PY".equalsIgnoreCase(type)) {
    					workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.LIB_PY, linkIdList);
    				} else if ("N".equalsIgnoreCase(type)) {
    					workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.LIB_N, linkIdList);
    				} else if ("T".equalsIgnoreCase(type)) {
    					workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.LIB_T, linkIdList);
    				} else if ("B".equalsIgnoreCase(type)) {
    					workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.LIB_B, linkIdList);
    				}
    				if (!b) {
    					SysBasic.rollBack();
    					break;
    				}
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/workflow/revoke/lib", e);
			log.error("/berry/workflow/revoke/lib", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    /**
     * 流程提交-CapturePooling
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/capturePooling", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitCapturePooling(@RequestBody Map<String,Object> map) {
        int code=0;
        Object[] objects=SysBasic.toTranObjectsByList((ArrayList)map.get("objects"));
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        
        String sqlcodeM = "SELECT * FROM BR_CAPTURE_POOLING_INFO WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(objects.length)+") AND CAP_POOL_LIB_FLAG='待审核'";
        String sqlcodeMx = "SELECT * FROM BR_CAPTURE_POOLING_MX_INFO WHERE CAP_LIB_ID=?";
        
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listTaskM=jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcodeM, objects);
	    
	    List<String> linkIdList = new ArrayList<String>();
	    List<Object> mIdList = new ArrayList<Object>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_CP");
	    
	    for (Map<String,Object> taskM : listTaskM) {
	    	Object CAP_LIB_ID = taskM.get("ID");
	    	List<Map<String,Object>> listTaskMx = jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcodeMx, CAP_LIB_ID);
	    	
		    for(Map<String,Object> taskMx:listTaskMx) {
		    	Object LINK_ID = taskMx.get("LINK_ID");
		    	if("".equals(SysBasic.toTranStringByObject(LINK_ID))) {
		    		continue;
		    	}
		    	Map<String,Object> resultMap=jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_MODUAL_CP where ID=?", LINK_ID);
		    	resultMap.putAll(taskM);
		    	resultMap.putAll(taskMx);
		    	
		    	// 判断是否变更新方案
		    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
		    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
		    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
		    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
		    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
		    	}
		    	
		    	SysBasic.filterMap(resultMap, metaMap.keySet());//过滤表不存在的字段
		    	resultMap.put("ID", LINK_ID);//避免ID覆盖
		    	int u = SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_CP", resultMap);//更新
		    	if(u>0) {
		    		code=1;
		    	}else {
		    		code=-1;
		    		break;
		    	}
		    	linkIdList.add( LINK_ID+"" );
		    }
		    mIdList.add( CAP_LIB_ID );
	    }
        if(code<=0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submit(Modulars.CP, linkIdList);
            if(wflowMsg.isB()){
                String sqlcode="update BR_CAPTURE_POOLING_INFO set CAP_POOL_LIB_FLAG='已审核' where ID in("+SysBasic.getQuestionMarkBySzie(mIdList.size())+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode,mIdList.toArray());
                
                // 插入LIB_INFO
                sqlcode = "INSERT INTO BR_LIB_INFO (ID, LIB_POOL_ID, LIB_TYPE_SYS, LIB_CODE, LIB_TYPE, LIB_FLAG) SELECT ?, ID, 'CapturePooing文库', CAP_POOL_LIB_CODE, '混合文库', '已提交' FROM BR_CAPTURE_POOLING_INFO WHERE ID=?";
                for (Object mId : mIdList) {
                	u=systemUpdateJdbcTemplate.update(sqlcode, SysBasic.getUUID(), mId);
                }
                
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).getData();
    }
    // 撤回方案: pooling CP
    @RequestMapping("/revoke/poolingCP")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokePoolingCPFlow(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String sqlUpdate = "UPDATE BR_CAPTURE_POOLING_INFO SET CAP_POOL_LIB_FLAG='待审核' WHERE ID=? and CAP_POOL_LIB_FLAG='已审核'";
    		String sqlSELECT_LINKID = "SELECT MX.LINK_ID FROM BR_CAPTURE_POOLING_MX_INFO MX WHERE MX.CAP_LIB_ID=?";
    		String sqlDELETE_LIB = "DELETE FROM BR_LIB_INFO WHERE LIB_POOL_ID=?";
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(sqlUpdate, id);
    			if (i > 0) {
    				updateJdbcTemplate.update(sqlDELETE_LIB, id);
    				// 查询提取明细 LINK_ID
    				List<String> linkIdList = updateJdbcTemplate.queryForList(sqlSELECT_LINKID, String.class, id);
    				WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.CP, linkIdList);
    				if (!wflowMsg.isB()) {
    					SysBasic.rollBack();
    					break;
    				}
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/workflow/revoke/poolingCP", e);
			log.error("/berry/workflow/revoke/poolingCP", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    /**
     * 流程提交-NgsPooling
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/ngsPooling", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitNgsPooling(@RequestBody Map<String,Object> map) {
        int code=0;
        Object[] objects=SysBasic.toTranObjectsByList((ArrayList)map.get("objects"));
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        
        String sqlcodeM = "SELECT * FROM BR_POOLING_INFO WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(objects.length)+") AND POOL_LIB_FLAG='待审核'";
        String sqlcodeMx = "SELECT * FROM BR_POOLING_MX_INFO WHERE POOL_LIB_ID=?";
        
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listTaskM = jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcodeM, objects);

	    List<String> linkIdList = new ArrayList<String>();
	    List<Object> mIdList = new ArrayList<Object>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_BH");

	    
	    for (Map<String,Object> taskM : listTaskM) {
	    	Object POOL_LIB_ID = taskM.get("ID");
	    	List<Map<String,Object>> listTaskMx = jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcodeMx, POOL_LIB_ID);
	    	
		    for(Map<String,Object> taskMx : listTaskMx) {
		    	Object LINK_ID = taskMx.get("LINK_ID");
		    	if("".equals(SysBasic.toTranStringByObject(LINK_ID))) {
		    		continue;
		    	}
		    	Map<String,Object> resultMap=jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_MODUAL_BH where ID=?", LINK_ID);
		    	resultMap.putAll(taskM);
		    	resultMap.putAll(taskMx);
		    	
		    	// 判断是否变更新方案
		    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
		    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
		    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
		    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
		    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
		    	}
		    	
		    	SysBasic.filterMap(resultMap, metaMap.keySet());//过滤表不存在的字段
		    	resultMap.put("ID", LINK_ID);//避免ID覆盖
		    	int u=SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_BH", resultMap);//更新
		    	if(u>0) {
		    		code=1;
		    	}else {
		    		code=-1;
		    		break;
		    	}
		    	linkIdList.add(LINK_ID+"");
		    }
		    mIdList.add( POOL_LIB_ID );
	    }
        if(code<=0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submit(Modulars.BH, linkIdList);
            if(wflowMsg.isB()){
                String sqlcode="update BR_POOLING_INFO set POOL_LIB_FLAG='已审核' where ID in("+SysBasic.getQuestionMarkBySzie(mIdList.size())+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode,mIdList.toArray());
                
                // 插入LIB_INFO
                sqlcode = "INSERT INTO BR_LIB_INFO (ID, LIB_POOL_ID, LIB_TYPE_SYS, LIB_CODE, LIB_TYPE, LIB_FLAG) SELECT ?, ID, 'NGSPooling文库', POOL_LIB_CODE, '混合文库', '已提交' FROM BR_POOLING_INFO WHERE ID=?";
                for (Object mId : mIdList) {
                	u=systemUpdateJdbcTemplate.update(sqlcode, SysBasic.getUUID(), mId);
                }
                
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).getData();
    }
    // 撤回方案: pooling NGS
    @RequestMapping("/revoke/poolingNGS")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokePoolingNGSFlow(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String sqlUpdate = "UPDATE BR_POOLING_INFO SET POOL_LIB_FLAG='待审核' WHERE ID=? and POOL_LIB_FLAG='已审核'";
    		String sqlSELECT_LINKID = "SELECT MX.LINK_ID FROM BR_POOLING_MX_INFO MX WHERE MX.POOL_LIB_ID=?";
    		String sqlDELETE_LIB = "DELETE FROM BR_LIB_INFO WHERE LIB_POOL_ID=?";
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(sqlUpdate, id);
    			if (i > 0) {
    				updateJdbcTemplate.update(sqlDELETE_LIB, id);
    				// 查询提取明细 LINK_ID
    				List<String> linkIdList = updateJdbcTemplate.queryForList(sqlSELECT_LINKID, String.class, id);
    				WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.BH, linkIdList);
    				if (!wflowMsg.isB()) {
    					SysBasic.rollBack();
    					break;
    				}
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/workflow/revoke/poolingNGS", e);
			log.error("/berry/workflow/revoke/poolingNGS", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    /**
     * 流程提交-TgsPooling
     * @param map
     * @return
     */
    @RequestMapping(value = "/submit/tgsPooling", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject submitTgsPooling(@RequestBody Map<String,Object> map) {
        int code=0;
        Object[] objects=SysBasic.toTranObjectsByList((ArrayList)map.get("objects"));
        JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(systemQueryJdbcTemplate);
        
        String sqlcodeM = "SELECT * FROM BR_TPOOLING_INFO WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(objects.length)+") AND TPOOL_LIB_FLAG='待审核'";
        String sqlcodeMx = "SELECT * FROM BR_TPOOLING_MX_INFO WHERE TPOOL_LIB_ID=?";
        
        jdbcTemplateUtils.toggle(queryJdbcTemplate);
	    List<Map<String,Object>> listTaskM = jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcodeM, objects);

	    List<String> linkIdList = new ArrayList<String>();
	    List<Object> mIdList = new ArrayList<Object>();

	    jdbcTemplateUtils.toggle(systemQueryJdbcTemplate);
	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_BH_T");
	    
	    for (Map<String,Object> taskM : listTaskM) {
	    	Object TPOOL_LIB_ID = taskM.get("ID");
	    	List<Map<String,Object>> listTaskMx = jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcodeMx, TPOOL_LIB_ID);
	    	
		    for(Map<String,Object> taskMx:listTaskMx) {
		    	Object LINK_ID = taskMx.get("LINK_ID");
		    	if("".equals(SysBasic.toTranStringByObject(LINK_ID))) {
		    		continue;
		    	}
		    	Map<String,Object> resultMap=jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_MODUAL_BH_T where ID=?", LINK_ID);
		    	resultMap.putAll(taskM);
		    	resultMap.putAll(taskMx);
		    	
		    	// 判断是否变更新方案
		    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
		    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
		    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
		    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
		    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
		    	}
		    	
		    	SysBasic.filterMap(resultMap, metaMap.keySet());//过滤表不存在的字段
		    	resultMap.put("ID", LINK_ID);//避免ID覆盖
		    	int u=SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_BH_T", resultMap);//更新
		    	if(u>0) {
		    		code=1;
		    	}else {
		    		code=-1;
		    		break;
		    	}
		    	linkIdList.add( LINK_ID+"" );
		    }
		    mIdList.add( TPOOL_LIB_ID );
	    }
        if(code<=0){
            SysBasic.rollBack();
        }else{
        	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submit(Modulars.PH_T, linkIdList);
            if(wflowMsg.isB()){
                String sqlcode="update BR_TPOOLING_INFO set TPOOL_LIB_FLAG='已审核' where ID in("+SysBasic.getQuestionMarkBySzie(mIdList.size())+")";
                int u=systemUpdateJdbcTemplate.update(sqlcode,mIdList.toArray());
                
                // 插入LIB_INFO
                sqlcode = "INSERT INTO BR_LIB_INFO (ID, LIB_POOL_ID, LIB_TYPE_SYS, LIB_CODE, LIB_TYPE, LIB_FLAG) SELECT ?, ID, 'TGSPooling文库', TPOOL_LIB_CODE, '混合文库', '已提交' FROM BR_TPOOLING_INFO WHERE ID=?";
                for (Object mId : mIdList) {
                	u=systemUpdateJdbcTemplate.update(sqlcode, SysBasic.getUUID(), mId);
                }
                
                if(u>0){
                    code=1;
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
            }else{
                code=-1;
                SysBasic.rollBack();
            }
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).getData();
    }
    // 撤回方案: pooling TGS
    @RequestMapping("/revoke/poolingTGS")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokePoolingTGSFlow(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String sqlUpdate = "UPDATE BR_TPOOLING_INFO SET TPOOL_LIB_FLAG='待审核' WHERE ID=? and TPOOL_LIB_FLAG='已审核'";
    		String sqlSELECT_LINKID = "SELECT LINK_ID FROM BR_TPOOLING_MX_INFO WHERE TPOOL_LIB_ID=?";
    		String sqlDELETE_LIB = "DELETE FROM BR_LIB_INFO WHERE LIB_POOL_ID=?";
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(sqlUpdate, id);
    			if (i > 0) {
    				updateJdbcTemplate.update(sqlDELETE_LIB, id);
    				// 查询提取明细 LINK_ID
    				List<String> linkIdList = updateJdbcTemplate.queryForList(sqlSELECT_LINKID, String.class, id);
    				WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.PH_T, linkIdList);
    				if (!wflowMsg.isB()) {
    					SysBasic.rollBack();
    					break;
    				}
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/workflow/revoke/poolingTGS", e);
			log.error("/berry/workflow/revoke/poolingTGS", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
