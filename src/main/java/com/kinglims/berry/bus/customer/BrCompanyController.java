package com.kinglims.berry.bus.customer;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/bus/customer/company")
@Slf4j
public class BrCompanyController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 作废
    @RequestMapping("/obsolete")
    @Transactional
    public JSONObject obsolete(@RequestBody JSONObject data) {
    	log.info("/berry/bus/customer/company/obsolete 进入");
    	try {
    		List ids = (List) data.get("ids");
    		
        	String sql = "update BR_CUSTOMER_COMPANY"
        			+ " set STATUS='作废',OBSOLETE_FLAG='Y'"
        			+ " where id in ("+SysBasic.getQuestionMarkBySzie(ids.size())+") and STATUS='正常'";
        	
        	updateJdbcTemplate.update(sql, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/bus/customer/company/obsolete", e);
			log.error("/berry/bus/customer/company/obsolete", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 恢复
    @RequestMapping("/recoverNormal")
    @Transactional
    public JSONObject recoverNormal(@RequestBody JSONObject data) {
    	log.info("/berry/bus/customer/company/recoverNormal 进入");
    	try {
    		List ids = (List) data.get("ids");
    		
        	String sql = "update BR_CUSTOMER_COMPANY"
        			+ " set STATUS='正常',OBSOLETE_FLAG='N'"
        			+ " where id in ("+SysBasic.getQuestionMarkBySzie(ids.size())+") and STATUS='作废'";
        	
        	updateJdbcTemplate.update(sql, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/bus/customer/company/recoverNormal", e);
			log.error("/berry/bus/customer/company/recoverNormal", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提交
    @RequestMapping("/submitNormal")
    @Transactional
    public JSONObject submitNormal(@RequestBody JSONObject data) {
    	log.info("/berry/bus/customer/company/submitNormal 进入");
    	try {
    		List ids = (List) data.get("ids");
    		
        	String sql = "update BR_CUSTOMER_COMPANY"
        			+ " set STATUS='正常',OBSOLETE_FLAG='N'"
        			+ " where id in ("+SysBasic.getQuestionMarkBySzie(ids.size())+") and STATUS='草稿'";
        	
        	updateJdbcTemplate.update(sql, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/bus/customer/company/submitNormal", e);
			log.error("/berry/bus/customer/company/submitNormal", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除
    @RequestMapping("/del")
    @Transactional
    public JSONObject del(@RequestBody JSONObject data) {
    	log.info("/berry/bus/customer/company/del 进入");
    	try {
    		List ids = (List) data.get("ids");
    		
        	String sql = "delete from BR_CUSTOMER_COMPANY"
        			+ " where id in ("+SysBasic.getQuestionMarkBySzie(ids.size())+") and STATUS='草稿'";
        	
        	updateJdbcTemplate.update(sql, ids.toArray());
        	
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/bus/customer/company/del", e);
			log.error("/berry/bus/customer/company/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
