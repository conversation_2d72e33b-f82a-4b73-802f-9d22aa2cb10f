package com.kinglims.berry.bus.saleManage;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/bus/saleManage/targetCustomer")
@Slf4j
public class BrTargetCustomerController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 客户档案表更新分析信息
    @RequestMapping("/archives/updateAnalyze")
    @Transactional
    public JSONObject archivesUpdateAnalyze(@RequestBody JSONObject data) {
    	log.info("/berry/bus/saleManage/targetCustomer/archives/updateAnalyze 进入");
    	
		String ARCHIVES_ID = data.getString("ARCHIVES_ID");
		String ANALYZE_ID = data.getString("ANALYZE_ID");
		
    	try {
    		
    		String updateSQL = " MERGE INTO BR_TARGET_CUSTOMER_ARCHIVES T1"
    				         + " USING (SELECT (SELECT COUNT(1) FROM BR_TARGET_CUSTOMER_ANALYZE WHERE ARCHIVES_ID=?) AS COUNTS, ARCHIVES_ID, ANALYZE_INFO, CREATTIME FROM BR_TARGET_CUSTOMER_ANALYZE WHERE ID=?)T2"
    				         + " ON (T1.ID=T2.ARCHIVES_ID)"
    				         + " WHEN MATCHED THEN"
    				         + "      UPDATE  SET T1.ANALYZE_COUNT=T2.COUNTS"
    				         + "                , T1.ANALYZE_INFO=T2.ANALYZE_INFO"
    				         + "                , T1.ANALYZE_DATETIME=T2.CREATTIME";
    		// 联表更新合同表
        	updateJdbcTemplate.update(updateSQL, ARCHIVES_ID, ANALYZE_ID);
        	log.info("/berry/bus/saleManage/targetCustomer/archives/updateAnalyze  完成");
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/bus/saleManage/targetCustomer/archives/updateAnalyze", e);
			log.error("/berry/bus/saleManage/targetCustomer/archives/updateAnalyze", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
