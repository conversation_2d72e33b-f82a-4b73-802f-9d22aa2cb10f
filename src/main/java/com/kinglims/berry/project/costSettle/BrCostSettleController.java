package com.kinglims.berry.project.costSettle;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/project/costSettle")
@Slf4j
public class BrCostSettleController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 保存费用明细
    @RequestMapping("/saveCostDetails")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject saveCostDetails(@RequestBody JSONObject data) {
    	
    	try {
    		String COST_SETTLE_ID = data.getString("COST_SETTLE_ID");
    		JSONArray DETAIL_ARRAY = data.getJSONArray("DETAIL_ARRAY");
    		
    		// 全部数据先删除
    		String sqlDEL = "DELETE FROM BR_COST_SETTLE_DETAIL WHERE COST_SETTLE_ID=?";
    		updateJdbcTemplate.update(sqlDEL, COST_SETTLE_ID);
    		
    		String sqlINSERT = "INSERT INTO BR_COST_SETTLE_DETAIL"
    		                 + " (ID,COST_SETTLE_ID,COST_NAME,PRICE,QUANTITY,AMOUNT,REMARK"
    				         + ",CREATOR,CREATTIME,LASTUPDATOR,LASTUPDATETIME,LOGINCOMPANY,SORT_NO) VALUES "
    				         + "(?,?,?,?,?,?,?,?,?,?,?,?,?)";
    		
    		// 全部数据重新插入
    		for (int i=0; i<DETAIL_ARRAY.size(); i++) {
    			JSONObject detail = DETAIL_ARRAY.getJSONObject(i);
    			String ID = detail.getString("ID");//VARCHAR2 唯一标识
    			String COST_NAME = detail.getString("COST_NAME");//VARCHAR2 费用名称
    			Double PRICE = detail.getDouble("PRICE");//NUMBER 单价
    			Integer QUANTITY = detail.getInteger("QUANTITY");//NUMBER 数量
    			Double AMOUNT = detail.getDouble("AMOUNT");//NUMBER 金额
    			String REMARK = detail.getString("REMARK");//VARCHAR2 备注
    			String CREATOR = detail.getString("CREATOR");//VARCHAR2 创建人
    			Date CREATTIME = detail.getDate("CREATTIME");//DATE 创建时间
    			String LASTUPDATOR = detail.getString("LASTUPDATOR");//VARCHAR2 最近修改人
    			Date LASTUPDATETIME = detail.getDate("LASTUPDATETIME");//DATE 最近修改时间
    			String LOGINCOMPANY = detail.getString("LOGINCOMPANY");//VARCHAR2 账套
    			
    			if (ID==null || ID.length()==0) {// 新增
    				ID = SysBasic.getUUID();
    				CREATOR = "";
    				CREATTIME = new Date();
    				LOGINCOMPANY = "";// 帐套信息，等于当前用户登录帐套
    			}
    			
    			// 刷新: 最后修改信息
    			LASTUPDATOR = "";
    			LASTUPDATETIME = new Date();
    			
    			Object[] params = { ID,COST_SETTLE_ID,COST_NAME,PRICE,QUANTITY,AMOUNT,REMARK
    								,CREATOR,CREATTIME,LASTUPDATOR,LASTUPDATETIME,LOGINCOMPANY, i
			    				};
    			updateJdbcTemplate.update(sqlINSERT, params);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/costSettle/saveCostDetails", e);
			log.error("/berry/project/costSettle/saveCostDetails", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除费用结算申请单
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sqlDEL_M = "DELETE FROM BR_COST_SETTLE WHERE ID=? AND STATUS='草稿'";
    		String sqlDEL_D = "DELETE FROM BR_COST_SETTLE_DETAIL WHERE COST_SETTLE_ID=?";
    		for (String id : ids) {
	    		int i = updateJdbcTemplate.update(sqlDEL_M, id);
	    		if (i > 0) {
	    			updateJdbcTemplate.update(sqlDEL_D, id);
	    		}
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/costSettle/del", e);
			log.error("/berry/project/costSettle/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提交到待确认
    @RequestMapping("/submitToDQR")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitToDQR(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_COST_SETTLE SET STATUS='待确认'"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS='草稿'";
    		
	    	updateJdbcTemplate.update(updateSQL, ids.toArray());
	    	
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/costSettle/submitToDQR", e);
			log.error("/berry/project/costSettle/submitToDQR", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 提交到待提报
    @RequestMapping("/submitToDTB")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitToDTB(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_COST_SETTLE SET STATUS='待提报'"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS='待确认'";
    		
	    	updateJdbcTemplate.update(updateSQL, ids.toArray());
	    	
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/costSettle/submitToDTB", e);
			log.error("/berry/project/costSettle/submitToDTB", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 提交到待结算
    @RequestMapping("/submitToDJS")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitToDJS(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_COST_SETTLE SET STATUS='待结算'"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS='待提报'";
    		
	    	updateJdbcTemplate.update(updateSQL, ids.toArray());
	    	
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/costSettle/submitToDJS", e);
			log.error("/berry/project/costSettle/submitToDJS", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 提交已结算
    @RequestMapping("/submitYJS")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitYJS(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_COST_SETTLE SET STATUS='已结算'"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS='待结算'";
    		
	    	updateJdbcTemplate.update(updateSQL, ids.toArray());
	    	
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/costSettle/submitYJS", e);
			log.error("/berry/project/costSettle/submitYJS", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 提交到销售确认
    @RequestMapping("/submitToSale")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitToSale(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_COST_SETTLE"
    				+ " SET STATUS_SALE='待确认',APPROVE_RS_SALE=NULL,APPROVE_TIME_SALE=NULL,APPROVE_REMARK_SALE=NULL"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS_SALE IS NULL";
    		
	    	updateJdbcTemplate.update(updateSQL, ids.toArray());
	    	
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/costSettle/submitToSale", e);
			log.error("/berry/project/costSettle/submitToSale", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 提交到客户确认
    @RequestMapping("/submitToCSS")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitToCSS(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_COST_SETTLE"
    				+ " SET STATUS_CSS='待确认',APPROVE_RS_CSS=NULL,APPROVE_TIME_CSS=NULL,APPROVE_REMARK_CSS=NULL"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS_CSS IS NULL";
    		
	    	updateJdbcTemplate.update(updateSQL, ids.toArray());
	    	
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/costSettle/submitToCSS", e);
			log.error("/berry/project/costSettle/submitToCSS", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
