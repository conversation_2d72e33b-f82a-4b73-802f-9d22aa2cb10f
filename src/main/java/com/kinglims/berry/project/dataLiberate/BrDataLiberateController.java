package com.kinglims.berry.project.dataLiberate;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/project/dataLiberate")
@Slf4j
public class BrDataLiberateController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 删除数据释放申请单
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sqlDEL_M = "DELETE FROM BR_DATA_LIBERATE WHERE ID=? AND STATUS='草稿'";
    		String sqlDEL_D = "DELETE FROM BR_DATA_LIBERATE_DETAIL WHERE M_ID=?";
    		for (String id : ids) {
	    		int i = updateJdbcTemplate.update(sqlDEL_M, id);
	    		if (i > 0) {
	    			updateJdbcTemplate.update(sqlDEL_D, id);
	    		}
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/dataLiberate/del", e);
			log.error("/berry/project/dataLiberate/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除数据释放申请单
    @RequestMapping("/addDetails")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject addDetails(@RequestBody JSONObject data) {
    	
    	try {
    		String M_ID = data.getString("M_ID");
    		String D_TYPE = data.getString("D_TYPE");
    		List<String> ids = (List<String>) data.get("ids");
    		if ("XJ".equalsIgnoreCase(D_TYPE)) {//下机数据
	    		String sql = "INSERT INTO BR_DATA_LIBERATE_DETAIL (ID, M_ID, D_TYPE, LINK_ID, CREATOR, CREATTIME, LASTUPDATOR, LASTUPDATETIME, LOGINCOMPANY) VALUES (?,?,?,?,?,?,?,?,?)";
	    		for (String LINK_ID : ids) {
	    			Object[] params = { SysBasic.getUUID(), M_ID, D_TYPE, LINK_ID, null, null, null, null, null };
		    		updateJdbcTemplate.update(sql, params);
	    		}
    		}
    		if ("FX".equalsIgnoreCase(D_TYPE)) {//分析数据
    			String sql = "INSERT INTO BR_DATA_LIBERATE_DETAIL (ID, M_ID, D_TYPE, LINK_ID, CREATOR, CREATTIME, LASTUPDATOR, LASTUPDATETIME, LOGINCOMPANY) VALUES (?,?,?,?,?,?,?,?,?)";
	    		for (String LINK_ID : ids) {
	    			Object[] params = { SysBasic.getUUID(), M_ID, D_TYPE, LINK_ID, null, null, null, null, null };
		    		updateJdbcTemplate.update(sql, params);
	    		}
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/dataLiberate/del", e);
			log.error("/berry/project/dataLiberate/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 超期删除申请
    @RequestMapping("/applyTimeoutDel")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject applyTimeoutDel(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sql = "UPDATE BR_DATA_LIBERATE SET STATUS='超期删除申请' WHERE ID=? AND STATUS IN ('草稿', '待释放')";
    		for (String id : ids) {
	    		updateJdbcTemplate.update(sql, id);
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/dataLiberate/applyTimeoutDel", e);
			log.error("/berry/project/dataLiberate/applyTimeoutDel", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 超期删除确认
    @RequestMapping("/commitTimeoutDel")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commitTimeoutDel(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sql = "UPDATE BR_DATA_LIBERATE SET STATUS='已确认超期删除' WHERE ID=? AND STATUS IN ('超期删除申请')";
    		for (String id : ids) {
	    		updateJdbcTemplate.update(sql, id);
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/dataLiberate/commitTimeoutDel", e);
			log.error("/berry/project/dataLiberate/commitTimeoutDel", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提交申请
    @RequestMapping("/submitApply")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitApply(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sql = "UPDATE BR_DATA_LIBERATE SET STATUS='待释放' WHERE ID=? AND STATUS IN ('草稿')";
    		for (String id : ids) {
    			updateJdbcTemplate.update(sql, id);
    		}
    		return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    		
    	} catch (Exception e) {
    		
    		log.info("/berry/project/dataLiberate/submitApply", e);
    		log.error("/berry/project/dataLiberate/submitApply", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
    	}
    }
}
