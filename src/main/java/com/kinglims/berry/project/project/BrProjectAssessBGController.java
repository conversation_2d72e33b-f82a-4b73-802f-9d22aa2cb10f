package com.kinglims.berry.project.project;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/project/project/assessBG")
@Slf4j
public class BrProjectAssessBGController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 添加明细
    @RequestMapping("/addDetails")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject addDetails(@RequestBody JSONObject data) {
    	
    	try {
    		String BG_ID = data.getString("BG_ID");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL_M_check = "SELECT COUNT(1) FROM BR_PROJECT_ASSESS_BG WHERE ID=? AND STATUS='草稿'";
			int countM_check = queryJdbcTemplate.queryForObject(selectSQL_M_check, Integer.class, BG_ID);
    		if (countM_check == 0) {
    			return new CurrResponseResolve(-1).put("msg", "主单不是草稿状态").put(responseMessageParameter).getData();
    		}
    		
    		String updateSQL_RK = "UPDATE BR_MODUAL_BG SET BG_MX_ID=?, BG_ID=? WHERE ID=? AND BG_ID IS NULL";
    		String insertSQL_MX = "INSERT INTO BR_PROJECT_ASSESS_BG_MX ("
		    				+ " ID"//唯一标识
		    				+ ",BG_ID"//报告ID
		    				+ ",LINK_ID"//关联入口表ID
		    				+ ",CONTRACT_ID"//合同ID
		    				+ ",CONTRACT_NO"//合同编号
		    				+ ",CONTRACT_NAME"//合同名称
		    				+ ",PROJECT_ID"//项目ID
		    				+ ",PROJECT_NO"//项目编号
		    				+ ",PROJECT_NAME"//项目名称
		    				+ ",NGS_FLOWCELL"//FLOWCELL号
		    				+ ",NGS_START_DATETIME"//上机时间
		    				+ ",TGS_DOWN_TIME"//下机时间
		    				+ ",TASK_ID"//生产生产任务单ID
		    				+ ",TASK_NO"//生产任务单号
		    				+ ",TASK_NAME"//任务单名称
		    				+ ",SAMPLE_ID"//样本ID
		    				+ ",SAMPLE_CODE"//样品编号
		    				+ ",SAMPLE_NAME"//样品名称
		    				+ ",LIB_ID"//文库ID
		    				+ ",LIB_CODE"//文库编号
		    				+ ",LIB_NAME"//文库名称
		    				+ ",LIB_TYPE"//文库类型
//		    				+ ",CREATOR"//创建人
//		    				+ ",CREATTIME"//创建时间
//		    				+ ",LASTUPDATOR"//最近修改人
//		    				+ ",LASTUPDATETIME"//最近修改时间
//		    				+ ",LOGINCOMPANY"//账套
		    				+ ")"
    				        + " SELECT"
 		    				+ " ? AS ID"//唯一标识
 		    				+ ",? AS BG_ID"//报告ID
 		    				+ ",ID AS LINK_ID"//关联入口表ID
 		    				+ ",CONTRACT_ID"//合同ID
 		    				+ ",CONTRACT_NO"//合同编号
 		    				+ ",CONTRACT_NAME"//合同名称
 		    				+ ",PROJECT_ID"//项目ID
 		    				+ ",PROJECT_NO"//项目编号
 		    				+ ",PROJECT_NAME"//项目名称
 		    				+ ",NGS_FLOWCELL"//FLOWCELL号
 		    				+ ",NGS_START_DATETIME"//上机时间
 		    				+ ",TGS_DOWN_TIME"//下机时间
 		    				+ ",TASK_ID"//生产生产任务单ID
 		    				+ ",TASK_NO"//生产任务单号
 		    				+ ",TASK_NAME"//任务单名称
 		    				+ ",SAMPLE_ID"//样本ID
 		    				+ ",SAMPLE_CODE"//样品编号
 		    				+ ",SAMPLE_NAME"//样品名称
 		    				+ ",LIB_ID"//文库ID
 		    				+ ",LIB_CODE"//文库编号
 		    				+ ",LIB_NAME"//文库名称
 		    				+ ",LIB_TYPE"//文库类型
    				        + " FROM BR_MODUAL_BG WHERE ID=?";
    		// 添加选中数据
    		for (String id : ids) {
    			String BG_MX_ID = SysBasic.getUUID();
    			Object[] params = { BG_MX_ID, BG_ID, id };
    			int i = updateJdbcTemplate.update(updateSQL_RK, params);
    			if (i > 0) {
    				updateJdbcTemplate.update(insertSQL_MX, params);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/assessBG/addDetails", e);
			log.error("/berry/project/project/assessBG/addDetails", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除明细
    @RequestMapping("/delDetails")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delDetails(@RequestBody JSONObject data) {
    	
    	try {
    		String BG_ID = data.getString("BG_ID");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL_M_check = "SELECT COUNT(1) FROM BR_PROJECT_ASSESS_BG WHERE ID=? AND STATUS='草稿'";
			int countM = queryJdbcTemplate.queryForObject(selectSQL_M_check, Integer.class, BG_ID);
    		if (countM == 0) {
    			return new CurrResponseResolve(-1).put("msg", "主单不是草稿状态").put(responseMessageParameter).getData();
    		}
    		
    		String deleteSQL_MX = "DELETE FROM BR_PROJECT_ASSESS_BG_MX WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		String updateSQL_RK = "UPDATE BR_MODUAL_BG SET BG_ID=NULL, BG_MX_ID=NULL WHERE BG_MX_ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
			
			// 删除明细表
    		updateJdbcTemplate.update(deleteSQL_MX, ids.toArray());
			// 删除 LINK_ID 关联信息 -- 查询所有明细表关联数据作为条件
    		updateJdbcTemplate.update(updateSQL_RK, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/assessBG/delDetails", e);
			log.error("/berry/project/project/assessBG/delDetails", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除主单
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String deleteSQL_M = "DELETE BR_PROJECT_ASSESS_BG WHERE ID=? AND STATUS='草稿'";
    		String deleteSQL_MX = "DELETE BR_PROJECT_ASSESS_BG_MX WHERE BG_ID=?";
    		String updateSQL_RK = "UPDATE BR_MODUAL_BG SET BG_ID=NULL, BG_MX_ID=NULL WHERE BG_ID=?";
			
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(deleteSQL_M, id);
    			if (i > 0) {
    				// 删除明细表
    				updateJdbcTemplate.update(deleteSQL_MX, id);
    				// 删除 LINK_ID 关联信息 -- 查询所有明细表关联数据作为条件
    				updateJdbcTemplate.update(updateSQL_RK, id);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/assessBG/del", e);
			log.error("/berry/project/project/assessBG/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提交
    @RequestMapping("/submit")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submit(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_PROJECT_ASSESS_BG SET STATUS='已提交'"
    		                 + " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS='草稿'";
    		int i = updateJdbcTemplate.update(updateSQL, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/assessBG/submit", e);
			log.error("/berry/project/project/assessBG/submit", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 撤销: 提交 -> 草稿
    @RequestMapping("/revokeSubmit")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeSubmit(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_PROJECT_ASSESS_BG SET STATUS='草稿'"
    		                 + " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS='已提交'";
    		int i = updateJdbcTemplate.update(updateSQL, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/assessBG/revokeSubmit", e);
			log.error("/berry/project/project/assessBG/revokeSubmit", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提交客户
    @RequestMapping("/commitCustomer")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commitCustomer(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_PROJECT_ASSESS_BG SET CUSTOMER_COMMIT_STATUS='待确认'"
    		                 + " WHERE CUSTOMER_COMMIT_STATUS IS NULL AND ID=?";
    		// 更新: 提交生信状态
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(updateSQL, id);
    			if (i > 0) {
    				// 逻辑处理，插入客户
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/assessBG/commitCustomer", e);
			log.error("/berry/project/project/assessBG/commitCustomer", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 提交生信
    @RequestMapping("/commitSX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commitSX(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_PROJECT_ASSESS_BG SET SX_COMMIT_STATUS='待确认'"
    		                 + " WHERE SX_COMMIT_STATUS IS NULL AND ID=?";
    		// 更新: 提交生信状态
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(updateSQL, id);
    			if (i > 0) {
    				// 逻辑处理，插入生信分析任务单表
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/assessBG/commitSX", e);
			log.error("/berry/project/project/assessBG/commitSX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 提交生产
    @RequestMapping("/commitProd")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commitProd(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_PROJECT_ASSESS_BG SET PROD_COMMIT_STATUS='待确认'"
    		                 + " WHERE PROD_COMMIT_STATUS IS NULL AND ID=?";
    		// 更新: 提交生信状态
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(updateSQL, id);
    			if (i > 0) {
    				// 逻辑处理，插入生产分析任务单表
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/assessBG/commitProd", e);
			log.error("/berry/project/project/assessBG/commitProd", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
