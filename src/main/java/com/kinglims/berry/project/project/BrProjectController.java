package com.kinglims.berry.project.project;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.security.entity.User;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/project/project")
@Slf4j
public class BrProjectController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 保存立项费用明细
    @RequestMapping("/projectCreate/saveProjectCostInfo")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject projectCreateSaveProjectCostInfo(@RequestBody JSONObject data) {
    	
    	try {
    		String PROJECT_ID = data.getString("PROJECT_ID");
    		JSONArray COST_ARRAY = data.getJSONArray("COST_ARRAY");
    		
    		// 释放“提取任务单”费用标识
			String updateSQL_tqTaskCostFlag_1 = "UPDATE BR_TQ_INFO SET COST_FLAG='N'"
					+ " WHERE ID IN (SELECT LINK_ID FROM BR_PROJECT_COST_INFO WHERE COST_SOURCE='提取任务单' AND PROJECT_ID=?)";
			updateJdbcTemplate.update(updateSQL_tqTaskCostFlag_1, PROJECT_ID);
    		
    		// 全部数据先删除
    		String deleteSQL = "DELETE FROM BR_PROJECT_COST_INFO WHERE PROJECT_ID=?";
    		updateJdbcTemplate.update(deleteSQL, PROJECT_ID);
    		
    		String insertSQL = "INSERT INTO BR_PROJECT_COST_INFO"
    		                 + " (ID,PROJECT_ID,COST_NAME,UNIT_PRICE,COST_NUM,COST_TOTAL_SUM,COST_REMARKS,SORT_NUM,COST_SOURCE,LINK_ID)"
    				         + " VALUES (?,?,?,?,?,?,?,?,?,?)";
    		
    		List<String> tqTaskCostFlag_ids = new ArrayList<String>();
    		// 全部数据重新插入
    		for (int i=0; i<COST_ARRAY.size(); i++) {
    			JSONObject detail = COST_ARRAY.getJSONObject(i);
    			String ID = detail.getString("ID");//VARCHAR2 唯一标识
    			String COST_NAME = detail.getString("COST_NAME");//费用名称
    			double UNIT_PRICE = detail.getDouble("UNIT_PRICE");//单价(元)
    			double COST_NUM = detail.getDouble("COST_NUM");//数量
    			double COST_TOTAL_SUM = detail.getDouble("COST_TOTAL_SUM");//金额(元)
        		String COST_REMARKS = detail.getString("COST_REMARKS");//备注
        		String COST_SOURCE = detail.getString("COST_SOURCE");//费用来源
        		String LINK_ID = detail.getString("LINK_ID");//关联来源ID
    			
    			if (ID==null || ID.length()==0) {// 新增
    				ID = SysBasic.getUUID();
    			}
    			
    			Object[] params = { ID,PROJECT_ID,COST_NAME,UNIT_PRICE,COST_NUM,COST_TOTAL_SUM,COST_REMARKS,i,COST_SOURCE,LINK_ID };
    			updateJdbcTemplate.update(insertSQL, params);
    			
    			if ("提取任务单".equals(COST_SOURCE)) {//用于打标提取任务单费用标识
    				tqTaskCostFlag_ids.add(LINK_ID);
    			}
    		}
    		// 提取任务单费用标识
    		if (tqTaskCostFlag_ids!=null && tqTaskCostFlag_ids.size()>0) {
    			String updateSQL_tqTaskCostFlag_2 = "UPDATE BR_TQ_INFO SET COST_FLAG='Y'"
    											+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(tqTaskCostFlag_ids.size())+")";
    			updateJdbcTemplate.update(updateSQL_tqTaskCostFlag_2, tqTaskCostFlag_ids.toArray());
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/projectCreate/saveProjectCostInfo", e);
			log.error("/berry/project/project/projectCreate/saveProjectCostInfo", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 立项提交审核
    @RequestMapping("/projectCreate/submitApprove")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject projectCreateSubmitApprove(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		// 获取项目状态为“新立项”的ID
    		String selectSQL_xlxID = "SELECT ID FROM BR_PROJECT_INFO"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie( ids.size() )+") AND STATUS='新立项'";
    		List<String> xlxlDList = queryJdbcTemplate.queryForList(selectSQL_xlxID, String.class, ids.toArray());
    		
    		if (xlxlDList != null && xlxlDList.size() > 0) {
	    		
//	    		// 判断明细 - 文库类型不能为空  -  数据量不能为空且大于零
//	    		String selectSQL_checkData = "SELECT COUNT(1) FROM BR_PROJECT_MX_INFO"
//	    						+ " WHERE PROJECT_ID IN ("+SysBasic.getQuestionMarkBySzie( xlxlDList.size() )+")"
//	    						+ " AND (LIB_TYPE IS NULL OR DATA_NUM IS NULL OR DATA_NUM<=0)";
//    			int checkData_COUNT = queryJdbcTemplate.queryForObject(selectSQL_checkData, Integer.class, xlxlDList.toArray()); 
//	    		if (checkData_COUNT > 0) {
//	    			return new CurrResponseResolve(1).put("msg", "文库类型不能为空, 数据量必须大于零").put(responseMessageParameter).getData();
//	    		} else {
		    		String sqlUpdate = "UPDATE BR_PROJECT_INFO SET STATUS='待审核'"
		    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie( xlxlDList.size() )+") AND STATUS='新立项'";
		    		updateJdbcTemplate.update(sqlUpdate, xlxlDList.toArray());
//	    		}
			}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/projectCreate/submitApprove", e);
			log.error("/berry/project/project/projectCreate/submitApprove", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 立项明细添加
    @RequestMapping("/projectCreate/addMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject projectCreateAddMX(@RequestBody JSONObject data) {
    	
    	try {
    		String PROJECT_ID = data.getString("PROJECT_ID");
    		String PROJECT_NO = data.getString("PROJECT_NO");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL = "SELECT COUNT(1) FROM BR_PROJECT_INFO WHERE ID=? AND STATUS='新立项'";
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, PROJECT_ID);
    		if (count == 0) {
    			return new CurrResponseResolve(-1).put("errMsg", "只能给“新立项”的项目添加明细").put(responseMessageParameter).getData();
    		}
    		
    		String insertSQL = "INSERT INTO BR_PROJECT_MX_INFO ("
    				+ "ID" // 唯一标识
    				+ ",PROJECT_ID" // 主表ID
    				+ ",PROJECT_IN_ID" // 关联项目模块入口参数
    				+ ",DATA_NUM" // 数据量（M）
    				+ ",PROJECT_NO" // 项目编号
    				+ ",SAMPLE_ID" // 关联样本ID
    				+ ",SAMPLE_CODE" // 样本编号
    				+ ",SAMPLE_NAME" // 样品名称
    				+ ",SAMPLE_TYPE" // 样本类型
    				+ ",LIB_INDEX_CODE" // index编号
    				+ ",LIB_INDEX_SEQ" // index序列
    				+ ",LIB_TYPE" // 文库类型
    				+ ",SEQ_TYPE" // 测序类型
//    				+ ",TASK_RECEIVE_MAN" // 任务接收人
//    				+ ",TASK_RECEIVE_DATETIME" // 任务接收日期
//    				+ ",PROJECT_MX_REMARKS" // 备注
//    				+ ",CREATOR" // 创建人
//    				+ ",CREATTIME" // 创建时间
//    				+ ",LASTUPDATOR" // 最近修改人
//    				+ ",LASTUPDATETIME" // 最近修改时间
//    				+ ",LOGINCOMPANY" // 账套
    				+ ",SORT_NUM" // 排序号
    				+ ") "
    				+ "SELECT"
    				+ " ? AS ID" // 唯一标识
    				+ ",? AS PROJECT_ID" // 关联提取主单
    				+ ",ID AS PROJECT_IN_ID" // 关联项目模块入口参数: 入口表ID
					+ ",DATA_NUM" // 目标数据量(M)
					+ ",? AS PROJECT_NO" // 项目编号
					+ ",SAMPLE_ID" // 关联样本ID
					+ ",SAMPLE_CODE" // 样本编号
					+ ",SAMPLE_NAME" // 样品名称
					+ ",SAMPLE_TYPE" // 样本类型
					+ ",LIB_INDEX_CODE" // index编号
					+ ",LIB_INDEX_SEQ" // index序列
					+ ",LIB_TYPE" // 文库类型
					+ ",SEQ_TYPE" // 测序类型
//					+ ",PROJECT_ID" // 项目ID
//					+ ",CONTRACT_ID" // 关联合同ID
//					+ ",CONTRACT_NO" // 合同编号
//					+ ",CONTRACT_NAME" // 合同名称
//					+ ",PROJECT_NAME" // 项目名称
//					+ ",PLAT_CATEGORY" // 平台类别
//					+ ",SEQ_PLATFORM" // 测序平台
//					+ ",PROJECT_MAN" // 项目负责人
//					+ ",PROJECT_TYPE" // 项目类型
//					+ ",START_DATE" // 生产开始日期
//					+ ",CLOSING_DATE" // 生产截止日期
//					+ ",EXPER_MAN" // 实验室负责人
//					+ ",SALE_MAN" // 销售负责人
//					+ ",SPECIES_NAME" // 物种名称
//					+ ",DATA_DELIVERY" // 数据交付%
//					+ ",PROJECT_DESCRIBE" // 项目描述
//					+ ",PROJECT_REMARKS" // 项目立项备注
//					+ ",PROJECT_APPROVE_BY" // 项目立审核人
//					+ ",PROJECT_APPROVE_RS" // 项目立审核意见
//					+ ",PROJECT_APPROVE_TIME" // 项目立审核时间
//					+ ",PROJECT_APPROVE_REMARK" // 项目立审核备注
//					+ ",SAMPLE_RECEIVE_DATE" // 收样日期
//					+ ",SAMPLE_RECEIVE_SURVEYOR" // 收样检验人
//					+ ",PROJECT_MX_REMARKS" // 项目立项明细备注
//					+ ",FLOW_PLAN_ID" // 实验流程方案ID
    				+ ",(SELECT (CASE WHEN T.SORT_NUM IS NULL THEN 1 ELSE T.SORT_NUM+1 END) FROM (SELECT MAX(SORT_NUM) AS SORT_NUM,COUNT(1) AS C FROM BR_PROJECT_MX_INFO WHERE PROJECT_ID=?)T) AS SORT_NUM" // ",SORT_NUM" // 排序号
    				+ " FROM BR_MODUAL_YY WHERE ID=?";
    		
    		String updateSQL = "UPDATE BR_MODUAL_YY SET PROJECT_ID=?,PROJECT_NO=?,PROJECT_MX_ID=?,OBJ_FLAG='归结' WHERE ID=? AND PROJECT_ID IS NULL";
    		
    		for (String MODUAL_YY_ID : ids) {
    			String PROJECT_MX_ID = SysBasic.getUUID();

    			Object[] objs = new Object[]{ PROJECT_ID, PROJECT_NO, PROJECT_MX_ID, MODUAL_YY_ID };
    			int i = updateJdbcTemplate.update(updateSQL, objs);
    			if (i > 0) {
	    			objs = new Object[]{ PROJECT_MX_ID, PROJECT_ID, PROJECT_NO, PROJECT_ID, MODUAL_YY_ID };
	    			updateJdbcTemplate.update(insertSQL, objs);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/projectCreate/addMX", e);
			log.error("/berry/project/project/projectCreate/addMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 移至暂未立
    @RequestMapping("/projectCreate/removeToZWL")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject projectCreateRemoveToZWL(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_MODUAL_YY SET PAUSE_FLAG='Y'"
    				+ " WHERE PROJECT_ID IS NULL AND PAUSE_FLAG='N' AND ID IN ("+SysBasic.getQuestionMarkBySzie( ids.size() )+")";
    		
    		updateJdbcTemplate.update(updateSQL, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/projectCreate/removeToZWL", e);
			log.error("/berry/project/project/projectCreate/removeToZWL", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 移至待立项
    @RequestMapping("/projectCreate/removeToDLX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject projectCreateRemoveToDLX(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_MODUAL_YY SET PAUSE_FLAG='N'"
    				+ " WHERE PROJECT_ID IS NULL AND PAUSE_FLAG='Y' AND ID IN ("+SysBasic.getQuestionMarkBySzie( ids.size() )+")";
    		
    		updateJdbcTemplate.update(updateSQL, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/projectCreate/removeToDLX", e);
			log.error("/berry/project/project/projectCreate/removeToDLX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 立项删除
    @RequestMapping("/projectCreate/delM")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject projectCreateDelM(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		//删除项目
    		String deleteSQL_M = "DELETE FROM BR_PROJECT_INFO WHERE ID=? AND STATUS='新立项'";
    		//释放“提取任务单”费用标识
			String updateSQL_tqTaskCostFlag = "UPDATE BR_TQ_INFO SET COST_FLAG='N'"
					+ " WHERE ID IN (SELECT LINK_ID FROM BR_PROJECT_COST_INFO WHERE COST_SOURCE='提取任务单' AND PROJECT_ID=?)";
			//删除项目费用
    		String deleteSQL_COST = "DELETE FROM BR_PROJECT_COST_INFO WHERE PROJECT_ID=?";
    		//删除明细
    		String deleteSQL_MX = "DELETE FROM BR_PROJECT_MX_INFO WHERE PROJECT_ID=?";
    		//释放入口表引用关系
    		String updateSQL_RK = "UPDATE BR_MODUAL_YY SET PROJECT_ID=NULL,PROJECT_NO=NULL,PROJECT_MX_ID=NULL,OBJ_FLAG=NULL WHERE PROJECT_ID=?";
    		
    		for (String PROJECT_ID : ids) {
    			//删除项目
        		int i = queryJdbcTemplate.update(deleteSQL_M, PROJECT_ID);
        		if (i > 0) {
        			//释放“提取任务单”费用标识
        			updateJdbcTemplate.update(updateSQL_tqTaskCostFlag, PROJECT_ID);
        			//删除项目费用
	    			updateJdbcTemplate.update(deleteSQL_COST, PROJECT_ID);
	    			//删除明细
		    		updateJdbcTemplate.update(deleteSQL_MX, PROJECT_ID);
		    		//释放入口表引用关系
		    		updateJdbcTemplate.update(updateSQL_RK, PROJECT_ID);
        		}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/projectCreate/delM", e);
			log.error("/berry/project/project/projectCreate/delM", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 立项明细删除
    @RequestMapping("/projectCreate/delMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject projectCreateDelMX(@RequestBody JSONObject data) {
    	
    	try {
    		String PROJECT_ID = data.getString("PROJECT_ID");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL = "SELECT COUNT(1) FROM BR_PROJECT_INFO WHERE ID=? AND STATUS='新立项'";
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, PROJECT_ID);
    		if (count == 0) {
    			return new CurrResponseResolve(-1).put("errMsg", "只能删除“新立项”的项目的明细").put(responseMessageParameter).getData();
    		}
    		
    		String deleteSQL = "DELETE FROM BR_PROJECT_MX_INFO WHERE ID=?";
    		String updateSQL = "UPDATE BR_MODUAL_YY SET PROJECT_ID=NULL,PROJECT_NO=NULL,PROJECT_MX_ID=NULL,OBJ_FLAG=NULL WHERE PROJECT_MX_ID=?";
    		
    		for (String PROJECT_MX_ID : ids) {
    			updateJdbcTemplate.update(deleteSQL, PROJECT_MX_ID);
	    		updateJdbcTemplate.update(updateSQL, PROJECT_MX_ID);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/projectCreate/delMX", e);
			log.error("/berry/project/project/projectCreate/delMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 检测任务 RNA DNA 批量修改
    @RequestMapping("/projectCreate/editMxBatch")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject projectCreateEditMxBatch(@RequestBody JSONObject data) {
    	
    	try {
    		String IDS = data.getString("IDS");
    		String[] idArray = IDS.split(",");
    		
    		// 处理日期格式
    		if (data.containsKey("QC_DATE")) {//
    		}
    		if (data.containsKey("QC_RECEIVE_DATE")) {//
    		}
    		
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(updateJdbcTemplate);
    		Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName("BR_PROJECT_MX_INFO");
    		
    		SysBasic.filterMap(data, metaMap.keySet());//过滤表不存在的字段
    		
    		for (String id : idArray) {
    			data.put("ID", id);
    			SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_PROJECT_MX_INFO", data);
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/projectCreate/editMxBatch", e);
			log.error("/berry/project/project/projectCreate/editMxBatch", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 关闭项目
    @RequestMapping("/projectOnline/close")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject projectOnlineClose(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_PROJECT_INFO SET PROJECT_FLAG='关闭' WHERE PROJECT_FLAG='在线' AND ID IN ("+SysBasic.getQuestionMarkBySzie( ids.size() )+")";
    		
    		updateJdbcTemplate.update(updateSQL, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/projectOnline/close", e);
			log.error("/berry/project/project/projectOnline/close", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 重新打开项目: 在线
    @RequestMapping("/projectOffline/reopen")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject projectOfflineReopen(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_PROJECT_INFO SET PROJECT_FLAG='在线' WHERE PROJECT_FLAG='关闭' AND ID IN ("+SysBasic.getQuestionMarkBySzie( ids.size() )+")";
    		
    		updateJdbcTemplate.update(updateSQL, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/projectOffline/reopen", e);
			log.error("/berry/project/project/projectOffline/reopen", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 批量分派: 项目管理员
    @RequestMapping("/batchUpdateContractManager")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject batchUpdateContractManager(@RequestBody JSONObject data, @RequestHeader(name="Authorization")String token) {
    	
    	try {
    		User user = SysBasic.getUserByTokenRedis(token);
    		
    		String CONTRACT_IDS = data.getString("CONTRACT_IDS");
    		String[] contractIds = CONTRACT_IDS.split(",");
    		
    		String CONTRACT_MANAGER_ID = data.getString("CONTRACT_MANAGER_ID");
    		String CONTRACT_MANAGER_NAME = data.getString("CONTRACT_MANAGER_NAME");
    		
    		String CREATOR = user.getNAME();
    		Date CREATTIME = new Date();
    		
    		String updateSQL_CONTRACT = "UPDATE BR_CONTRACT SET CONTRACT_MANAGER_ID=?, CONTRACT_MANAGER_NAME=? WHERE ID=?";
    		String insertSQL_MANAGER = "INSERT INTO BR_CONTRACT_MANAGER"
    				+ " (ID, CREATOR, CREATTIME, CONTRACT_ID, CONTRACT_MANAGER_ID, CONTRACT_MANAGER_NAME) VALUES (?,?,?,?,?,?)";
    		
    		for (String CONTRACT_ID : contractIds) {
    			// 更新合同表
    			updateJdbcTemplate.update(updateSQL_CONTRACT, CONTRACT_MANAGER_ID, CONTRACT_MANAGER_NAME, CONTRACT_ID);
    			// 插入分派记录表
    			Object[] params = { SysBasic.getUUID(), CREATOR, CREATTIME, CONTRACT_ID, CONTRACT_MANAGER_ID, CONTRACT_MANAGER_NAME };
    			updateJdbcTemplate.update(insertSQL_MANAGER, params);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/batchUpdateContractManager", e);
			log.error("/berry/project/project/batchUpdateContractManager", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 获取项目项目类型 - 字典
    @RequestMapping("/getPROJECT_TYPE_ZD")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject getPROJECT_TYPE_ZD(@RequestBody JSONObject data) {
    	
    	try {
    		String PROJECT_TYPE = data.getString("PROJECT_TYPE");
    		
    		String selectSQL = "SELECT * FROM BR_DATA_DICT WHERE CODE='PROJECT_TYPE' AND ITEM_TEXT=?";
    		List<Map<String, Object>> infoMapList = queryJdbcTemplate.queryForList(selectSQL, PROJECT_TYPE);
    		
    		Map<String, Object> infoMap = null;
    		if (infoMapList!=null && infoMapList.size()>0) {
    			infoMap = infoMapList.get(0);
    		} else {
    			infoMap = new HashMap<String, Object>();
    		}
	    	return new CurrResponseResolve(1).put("infoMap", infoMap).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/getPROJECT_TYPE_ZD", e);
			log.error("/berry/project/project/getPROJECT_TYPE_ZD", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 获取项目 - 合同信息
    @RequestMapping("/getContractInfo")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject getContractInfo(@RequestBody JSONObject data) {
    	
    	try {
    		String CONTRACT_ID = data.getString("CONTRACT_ID");
    		String CONTRACT_NO = data.getString("CONTRACT_NO");
    		
    		String selectSQL = "SELECT"
    				+ " CONTRACT_NO"//合同编号
    				+ ",CONTRACT_NAME"//合同名称
    				+ ",CONTRACT_STATUS"//合同状态
    				+ ",CONTRACT_AMOUNT"//合同金额
    				+ ",CONTRACT_ATT_FILEPATH"//合同附件
    				+ ",SIGN_BUS_MAN_ID"//业务(销售)员ID
    				+ ",SIGN_BUS_MAN"//业务(销售)员姓名
    				+ ",CONTENT_ANALYZE"//技术目标
    				+ ",CONTENT_ANALYZE_RN"//技术内容
    				+ " FROM BR_CONTRACT"
    				+ " WHERE ID=? OR CONTRACT_NO=?";
    		List<Map<String, Object>> infoMapList = queryJdbcTemplate.queryForList(selectSQL, CONTRACT_ID, CONTRACT_NO);
    		
    		Map<String, Object> infoMap = null;
    		if (infoMapList!=null && infoMapList.size()>0) {
    			infoMap = infoMapList.get(0);
    		} else {
    			infoMap = new HashMap<String, Object>();
    		}
    		
	    	return new CurrResponseResolve(1).put("infoMap", infoMap).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/getContractInfo", e);
			log.error("/berry/project/project/getContractInfo", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 获取项目立项约束条件信息 - 合同回款情况
    @RequestMapping("/getContractHkInfo")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject getContractHkInfo(@RequestBody JSONObject data) {
    	
    	try {
    		String CONTRACT_ID = data.getString("CONTRACT_ID");
    		String CONTRACT_NO = data.getString("CONTRACT_NO");
    		
    		String selectSQL_1 = "SELECT COUNT(1) AS C, SUM(INVOICE_AMOUNT) AS S FROM BR_INVOICE_ISSUED WHERE CONTRACT_NO=?";
    		Map<String, Object> cHkInfoMap_1 = queryJdbcTemplate.queryForMap(selectSQL_1, CONTRACT_NO);
    		
    		String selectSQL_2 = "SELECT COUNT(1) AS C, SUM(INVOICE_AMOUNT) AS S FROM BR_INVOICE_ISSUED WHERE CONTRACT_NO=? AND STATUS=?";
    		Map<String, Object> cHkInfoMap_2 = queryJdbcTemplate.queryForMap(selectSQL_2, CONTRACT_NO, "待审核");
    		
    		String selectSQL_3 = "SELECT COUNT(1) AS C, SUM(INVOICE_AMOUNT) AS S FROM BR_INVOICE_ISSUED WHERE CONTRACT_NO=? AND STATUS IN (?,?)";
    		Map<String, Object> cHkInfoMap_3 = queryJdbcTemplate.queryForMap(selectSQL_3, CONTRACT_NO, "待填发票号", "已填发票号");
    		
    		String selectSQL_4 = "SELECT COUNT(1) AS C, SUM(INVOICE_AMOUNT) AS S FROM BR_INVOICE_ISSUED WHERE CONTRACT_NO=? AND STATUS=?";
    		Map<String, Object> cHkInfoMap_4 = queryJdbcTemplate.queryForMap(selectSQL_4, CONTRACT_NO, "审核不通过");
    		
    		String selectSQL_5 = "SELECT SUM(CANCEL_AMOUNT) AS S FROM BR_INVOICE_ISSUED WHERE CONTRACT_NO=? AND STATUS IN (?,?) AND CANCEL_AMOUNT IS NOT NULL AND CANCEL_AMOUNT>0";
    		Map<String, Object> cHkInfoMap_5 = queryJdbcTemplate.queryForMap(selectSQL_5, CONTRACT_NO, "待填发票号", "已填发票号");
    		
    		String selectSQL_6 = "SELECT"
    				+ " (SELECT COUNT(1) AS C FROM BR_INVOICE_REPEAL T1 WHERE T1.CONTRACT_NO=?) AS C"
    				+ ",(SELECT SUM(T2.INVOICE_AMOUNT) FROM BR_INVOICE_REPEAL T1, BR_INVOICE_ISSUED T2 WHERE T1.CONTRACT_NO=? AND T1.ID=T2.REPEAL_ID) AS S"
    				+ " FROM DUAL";
    		Map<String, Object> cHkInfoMap_6 = queryJdbcTemplate.queryForMap(selectSQL_6, CONTRACT_NO, CONTRACT_NO);

    		String selectSQL_7 = "SELECT"
    				+ " (SELECT COUNT(1) AS C FROM BR_INVOICE_REPEAL T1 WHERE T1.CONTRACT_NO=? AND T1.STATUS=?) AS C"
    				+ ",(SELECT SUM(T2.INVOICE_AMOUNT) FROM BR_INVOICE_REPEAL T1, BR_INVOICE_ISSUED T2 WHERE T1.CONTRACT_NO=? AND T1.STATUS=? AND T1.ID=T2.REPEAL_ID) AS S"
    				+ " FROM DUAL";
    		Map<String, Object> cHkInfoMap_7 = queryJdbcTemplate.queryForMap(selectSQL_7, CONTRACT_NO,"待审核", CONTRACT_NO,"待审核");

    		String selectSQL_8 = "SELECT"
    				+ " (SELECT COUNT(1) AS C FROM BR_INVOICE_REPEAL T1 WHERE T1.CONTRACT_NO=? AND T1.STATUS=?) AS C"
    				+ ",(SELECT SUM(T2.INVOICE_AMOUNT) FROM BR_INVOICE_REPEAL T1, BR_INVOICE_ISSUED T2 WHERE T1.CONTRACT_NO=? AND T1.STATUS=? AND T1.ID=T2.REPEAL_ID) AS S"
    				+ " FROM DUAL";
    		Map<String, Object> cHkInfoMap_8 = queryJdbcTemplate.queryForMap(selectSQL_8, CONTRACT_NO,"审核通过", CONTRACT_NO,"审核通过");

    		String selectSQL_9 = "SELECT"
    				+ " (SELECT COUNT(1) AS C FROM BR_INVOICE_REPEAL T1 WHERE T1.CONTRACT_NO=? AND T1.STATUS=?) AS C"
    				+ ",(SELECT SUM(T2.INVOICE_AMOUNT) FROM BR_INVOICE_REPEAL T1, BR_INVOICE_ISSUED T2 WHERE T1.CONTRACT_NO=? AND T1.STATUS=? AND T1.ID=T2.REPEAL_ID) AS S"
    				+ " FROM DUAL";
    		Map<String, Object> cHkInfoMap_9 = queryJdbcTemplate.queryForMap(selectSQL_9, CONTRACT_NO,"审核不通过", CONTRACT_NO,"审核不通过");
    		
    		CurrResponseResolve crr = new CurrResponseResolve(1);
    		crr.put("cHkInfoMap_1", cHkInfoMap_1);
    		crr.put("cHkInfoMap_2", cHkInfoMap_2);
    		crr.put("cHkInfoMap_3", cHkInfoMap_3);
    		crr.put("cHkInfoMap_4", cHkInfoMap_4);
    		crr.put("cHkInfoMap_5", cHkInfoMap_5);
    		crr.put("cHkInfoMap_6", cHkInfoMap_6);
    		crr.put("cHkInfoMap_7", cHkInfoMap_7);
    		crr.put("cHkInfoMap_8", cHkInfoMap_8);
    		crr.put("cHkInfoMap_9", cHkInfoMap_9);
    		
	    	return crr.put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/getContractHkInfo", e);
			log.error("/berry/project/project/getContractHkInfo", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 获取项目立项约束条件信息 - 合同特殊审批
    @RequestMapping("/getContractTsspInfo")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject getContractTsspInfo(@RequestBody JSONObject data) {
    	
    	try {
    		String CONTRACT_ID = data.getString("CONTRACT_ID");
    		String CONTRACT_NO = data.getString("CONTRACT_NO");
    		
    		String selectSQL = "SELECT"
    				+ " (SELECT COUNT(1) FROM BR_SPECIAL_APPROVE WHERE CONTRACT_NO=?) AS APPLY_COUNT"
    				+ ",(SELECT COUNT(1) FROM BR_SPECIAL_APPROVE WHERE CONTRACT_NO=? AND STATUS=?) AS APPROVING_COUNT"
    				+ ",(SELECT COUNT(1) FROM BR_SPECIAL_APPROVE WHERE CONTRACT_NO=? AND STATUS=?) AS APPROVED_TG_COUNT"
    				+ ",(SELECT COUNT(1) FROM BR_SPECIAL_APPROVE WHERE CONTRACT_NO=? AND STATUS=?) AS APPROVED_BTG_COUNT"
    				+ " FROM DUAL";
    		Object[] objs = { CONTRACT_NO, CONTRACT_NO,"待审批", CONTRACT_NO,"审批通过", CONTRACT_NO,"审批不通过" };
    		Map<String, Object> cTsspInfoMap = queryJdbcTemplate.queryForMap(selectSQL, objs);
    		
	    	return new CurrResponseResolve(1).put("cTsspInfoMap", cTsspInfoMap).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/project/getContractTsspInfo", e);
			log.error("/berry/project/project/getContractTsspInfo", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
