package com.kinglims.berry.project.sampleQC;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/project/sampleQC/tqTaskOrder")
@Slf4j
public class BrTQTaskOrderController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 提取任务单:提交审核
    @RequestMapping("/submit")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submit(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL_CHECK = "SELECT COUNT(1) FROM ("
    					+ "SELECT"
    					+ " (SELECT COUNT(1) FROM BR_TQ_MX_INFO MX WHERE MX.TQ_ID=M.ID) AS C"
    					+ " FROM BR_TQ_INFO M"
    					+ " WHERE M.ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS IN ('草稿','退回')"
    				+ ") T WHERE T.C=0";
    		int checkMXCount = queryJdbcTemplate.queryForObject(selectSQL_CHECK, Integer.class, ids.toArray());
    		
    		if (checkMXCount > 0) {
    			return new CurrResponseResolve(1).put("msg", "选中提交的提取任务单， 没有提取样本明细").put(responseMessageParameter).getData();
    		} else {
	    		String updateSQL_TQ = "UPDATE BR_TQ_INFO SET STATUS='待审核' WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS IN ('草稿','退回')";
	    		updateJdbcTemplate.update(updateSQL_TQ, ids.toArray());
		    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    		}
    		
		} catch (Exception e) {
			
			log.info("/berry/project/sampleQC/tqTaskOrder/submit", e);
			log.error("/berry/project/sampleQC/tqTaskOrder/submit", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提取任务单:添加样本
    @RequestMapping("/tqAddSample")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject tqAddSample(@RequestBody JSONObject data) {
    	
    	try {
    		String TQ_ID = data.getString("TQ_ID");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL_M_check = "SELECT COUNT(1) FROM BR_TQ_INFO WHERE ID=? AND STATUS IN ('草稿','退回')";
    		// 查询主表状态, 满足条件继续执行移除
    		int nextFlag = updateJdbcTemplate.queryForObject(selectSQL_M_check, Integer.class, TQ_ID);
    		if (nextFlag == 0) {
    			return new CurrResponseResolve(1).put("msg", "提取任务单状态已发生变化, 不能添加明细, 请刷新主列表、明细列表").put(responseMessageParameter).getData();
    		}
    		
    		if (ids == null || ids.size() == 0) {
				return new CurrResponseResolve(-1).put("msg", "参数错误!").put(responseMessageParameter).getData();
			}
    		
    		String insertSQL = "INSERT INTO BR_TQ_MX_INFO ("
    				+ "ID" // 唯一标识
    				+ ",TQ_ID" // 关联提取主单
    				+ ",LINK_ID" // 关联入口表ID
    				+ ",SAMPLE_IS_MERGE" // 是否合并样本
    				+ ",SAMPLE_ID" // 关联样本ID
    				+ ",SAMPLE_CODE" // 样本编编号
    				+ ",SAMPLE_NAME" // 样本名称
    				+ ",SAMPLE_TYPE" // 样本种类
    				+ ",SAMPLE_SEND_NO" // 送样单据号
    				+ ",CUSTOMER_ID" // 关联客户联系人ID
    				+ ",CUSTOMER_NAME" // 客户联系人
    				+ ",COMPANY_ID" // 关联客户单位ID
    				+ ",COMPANY_NAME" // 客户单位
    				+ ",CONTRACT_ID" // 合同ID
    				+ ",CONTRACT_NO" // 合同编号
    				+ ",CONTRACT_NAME" // 合同名称
    				+ ",SAMPLE_SEND_MAN" // 送样人
    				+ ",SAMPLE_RECEIVE_DATE" // 收样日期
    				+ ",SAMPLE_RECEIVE_SURVEYOR" // 收样检验人
    				+ ",EXAMINE_RS" // 检验结论
    				+ ",CARRIAGE_CONDITIONS" // 运输条件
    				+ ",SORT_NUM" // 排序号
    				+ ") "
    				+ "SELECT"
    				+ " ? AS ID" // 唯一标识
    				+ ",? AS TQ_ID" // 关联提取主单
    				+ ",ID AS LINK_ID" // 关联入口表ID
    				+ ",SAMPLE_IS_MERGE" // 是否合并样本
    				+ ",SAMPLE_ID" // 关联样本ID
    				+ ",SAMPLE_CODE" // 样本编编号
    				+ ",SAMPLE_NAME" // 样本名称
    				+ ",SAMPLE_TYPE" // 样本种类
    				+ ",SAMPLE_SEND_NO" // 送样单据号
    				+ ",CUSTOMER_ID" // 关联客户联系人ID
    				+ ",CUSTOMER_NAME" // 客户联系人
    				+ ",COMPANY_ID" // 关联客户单位ID
    				+ ",COMPANY_NAME" // 客户单位
    				+ ",CONTRACT_ID" // 合同ID
    				+ ",CONTRACT_NO" // 合同编号
    				+ ",CONTRACT_NAME" // 合同名称
    				+ ",SAMPLE_SEND_MAN" // 送样人
    				+ ",SAMPLE_RECEIVE_DATE" // 收样日期
    				+ ",SAMPLE_RECEIVE_SURVEYOR" // 收样检验人
    				+ ",EXAMINE_RS" // 检验结论
    				+ ",CARRIAGE_CONDITIONS" // 运输条件
    				+ ",(SELECT (CASE WHEN T.SORT_NUM IS NULL THEN 1 ELSE T.SORT_NUM+1 END) FROM (SELECT MAX(SORT_NUM) AS SORT_NUM,COUNT(1) AS C FROM BR_TQ_MX_INFO WHERE TQ_ID=?)T) AS SORT_NUM" // 排序号
    				+ " FROM BR_MODUAL_TQ WHERE ID=? AND TQ_TASK_ID IS NULL";
    		
    		// 更新入口表
    		String updateSQL_RK = "UPDATE BR_MODUAL_TQ SET TQ_TASK_ID=?,TQ_TASK_MX_ID=?,OBJ_FLAG='归结' WHERE ID=? AND TQ_TASK_ID IS NULL";
    		
    		for (String MODUAL_TQ_ID : ids) {
    			String TQ_TASK_MX_ID = SysBasic.getUUID();
    			Object[] objs = { TQ_TASK_MX_ID, TQ_ID, TQ_ID, MODUAL_TQ_ID };
    			updateJdbcTemplate.update(insertSQL, objs);
    			
    			updateJdbcTemplate.update(updateSQL_RK, TQ_ID, TQ_TASK_MX_ID, MODUAL_TQ_ID);
    		}
    		
    		// 更新主表: 样本数量
    		String updateSQL_M = "UPDATE BR_TQ_INFO BTI SET"
    				+ " TQ_SAMPLE_NUMBER=(SELECT COUNT(1) FROM BR_TQ_MX_INFO BTMI WHERE BTMI.TQ_ID=BTI.ID)"
    				+ " WHERE BTI.ID=?";
    		updateJdbcTemplate.update(updateSQL_M, TQ_ID);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/sampleQC/tqTaskOrder/tqAddSample", e);
			log.error("/berry/project/sampleQC/tqTaskOrder/tqAddSample", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 提取任务单:合并样本
    @RequestMapping("/tqSampleMerge")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject tqSampleMerge(@RequestBody JSONObject data) {
    	
    	try {
				List<String> ids = (List<String>) data.get("ids");// 待合并提取样本ID：提取模块表ID
				String MERGE_NO = data.getString("MERGE_NO");// 合并编号
				String MERGE_NAME = data.getString("MERGE_NAME");// 合并名称
		    	
		    	String MERGE_SAMPLE_ID = SysBasic.getUUID() + "@SAMPLE_MERGE";// 合并后样本ID
		    	
		    	// 更新选中样本，占位
		    	String updateSQL_RK = "UPDATE BR_MODUAL_TQ SET TQ_TASK_ID='"+MERGE_SAMPLE_ID+"',OBJ_FLAG='归结' WHERE ID IN ("+ SysBasic.getQuestionMarkBySzie(ids.size()) +") AND TQ_TASK_ID IS NULL";
		    	// 查询选中样本
		    	String selectSQL_RK = "SELECT * FROM BR_MODUAL_TQ WHERE TQ_TASK_ID='"+MERGE_SAMPLE_ID+"'";
		    	
		    	int i = updateJdbcTemplate.update(updateSQL_RK, ids.toArray());
		    	if (i > 0) {
			    	List<Map<String, Object>> rkList = updateJdbcTemplate.queryForList(selectSQL_RK);
			    	if (rkList != null && rkList.size() > 0) {
			    		// 插入样本合并中间表
			    		String insertSQL_MERGE_SAMPLE = "INSERT INTO BR_SAMPLE_INFO_DETAIL_MERGE (ID, MERGE_SAMPLE_ID, SAMPLE_ID) VALUES (?, ?, ?)";
			    		for (Map<String, Object> m : rkList) {
			    			Object[] objs = { SysBasic.getUUID(), MERGE_SAMPLE_ID, m.get("SAMPLE_ID") };
			    			updateJdbcTemplate.update(insertSQL_MERGE_SAMPLE, objs);
			    		}
			    		
			    		// 查询入口表: 样本ID, 获取其中一行
			    		Map<String, Object> rkMap = rkList.get(0);
			    		String SAMPLE_ID_OLD = (String) rkMap.get("SAMPLE_ID");
			    		// 重置样本编号,样本名称
			    		rkMap.remove("ID");
			    		rkMap.remove("SAMPLE_ID");
			    		rkMap.remove("SAMPLE_CODE");
			    		rkMap.remove("SAMPLE_NAME");
			    		rkMap.remove("TQ_TASK_ID");
			    		rkMap.put("ID", MERGE_SAMPLE_ID);
			    		rkMap.put("SAMPLE_ID", MERGE_SAMPLE_ID);
			    		rkMap.put("SAMPLE_CODE", MERGE_NO);
			    		rkMap.put("SAMPLE_NAME", MERGE_NAME);
			    		rkMap.put("SAMPLE_IS_MERGE", "是");
			    		
			    		JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(updateJdbcTemplate);
			            // 插入样本明细表
			            String sqlSampleMerge = " INSERT INTO BR_SAMPLE_INFO_DETAIL (ID, SIB_ID, SIC_ID, SEQ_PLATFORM, STATUS, CREATOR, LASTUPDATOR, LOGINCOMPANY, SAMPLE_CODE, SAMPLE_NAME, LIB_CODE, LIB_NAME, SEQ_TYPE, QUANTIFY_METHOD, SPECIES_NAME, LIB_INDEX_CODE, LIB_INDEX_SEQ, SAMPLE_STATUS, STORAGE_CONDITIONS, EXTRACT_OPINION, SAMPLE_REMARK, APPROVE_BY, APPROVE_RS, APPROVE_REMARK, SAMPLE_SEND_NO, ORIFICE_PLATE_CODE, ORIFICE_CODE, SAMPLE_SEND_MAN, SAMPLE_RECEIVE_SURVEYOR, EXAMINE_RS, CARRIAGE_CONDITIONS, SAMPLE_SEND_MAN_ID, SAMPLE_RECEIVE_SURVEYOR_ID, SAMPLE_RECEIVE_REMARK, SAMPLE_TYPE, CREATTIME, LASTUPDATETIME, CONCENTRATION, VOLUME, OD260_280, PREPARATION_TIME, LIB_SIZE, DATA_NUM, SAMPLE_NUMBER, APPROVE_TIME, SORT_NO, SAMPLE_RECEIVE_DATE, SAMPLE_SEND_DATE, SAMPLE_IS_MERGE)"
			            + " SELECT ?, SIB_ID, SIC_ID, SEQ_PLATFORM, STATUS, CREATOR, LASTUPDATOR, LOGINCOMPANY, "
			            + " ?, ?, LIB_CODE, LIB_NAME, SEQ_TYPE, QUANTIFY_METHOD, SPECIES_NAME, LIB_INDEX_CODE, LIB_INDEX_SEQ, "
			            + " SAMPLE_STATUS, STORAGE_CONDITIONS, EXTRACT_OPINION, SAMPLE_REMARK, APPROVE_BY, APPROVE_RS, APPROVE_REMARK, SAMPLE_SEND_NO, ORIFICE_PLATE_CODE, "
			            + " ORIFICE_CODE, SAMPLE_SEND_MAN, SAMPLE_RECEIVE_SURVEYOR, EXAMINE_RS, CARRIAGE_CONDITIONS, SAMPLE_SEND_MAN_ID, SAMPLE_RECEIVE_SURVEYOR_ID, "
			            + " SAMPLE_RECEIVE_REMARK, SAMPLE_TYPE, CREATTIME, LASTUPDATETIME, CONCENTRATION, VOLUME, OD260_280, PREPARATION_TIME, LIB_SIZE, DATA_NUM, SAMPLE_NUMBER, "
			            + " APPROVE_TIME, SORT_NO, SAMPLE_RECEIVE_DATE, SAMPLE_SEND_DATE, 'Y' FROM BR_SAMPLE_INFO_DETAIL WHERE ID=?";
			            updateJdbcTemplate.update(sqlSampleMerge, MERGE_SAMPLE_ID, MERGE_NO, MERGE_NAME, SAMPLE_ID_OLD);
			            
			    		// 插入提取入口表
			    		Map<String,Integer> rkMetaMap = jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_TQ");
			    		SysBasic.filterMap(rkMap, rkMetaMap.keySet());
			    		SysBasic.insertDataByTableMap(updateJdbcTemplate, "BR_MODUAL_TQ", rkMap);
			    	}
		    	}
		
			return new CurrResponseResolve(1).put(responseMessageParameter).getData();
			
		} catch (Exception e) {
			
			log.info("/berry/project/sampleQC/tqTaskOrder/tqSampleMerge", e);
			log.error("/berry/project/sampleQC/tqTaskOrder/tqSampleMerge", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提取任务单: 修改明细
    @RequestMapping("/batchEditD")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject batchEditD(@RequestBody JSONObject data) {
    	
    	try {
    		String TQ_ID = data.getString("TQ_ID");
    		String TQ_TUBE_NUM_DNA = data.getString("TQ_TUBE_NUM_DNA");
    		String TQ_TUBE_NUM_RNA = data.getString("TQ_TUBE_NUM_RNA");
    		String TQ_MX_REMARK = data.getString("TQ_MX_REMARK");
    		List<String> TQ_MX_IDS = (List<String>) data.get("ids");
    		
    		String sqlSelect_M = "SELECT COUNT(1) FROM BR_TQ_INFO WHERE ID=? AND STATUS IN ('草稿','退回')";
    		String sqlUpdate = "UPDATE BR_TQ_MX_INFO SET TQ_TUBE_NUM_DNA=?,TQ_TUBE_NUM_RNA=?,TQ_MX_REMARK=?"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(TQ_MX_IDS.size())+")";
    		
    		// 查询主表状态, 满足条件继续执行移除
    		int nextFlag = updateJdbcTemplate.queryForObject(sqlSelect_M, Integer.class, TQ_ID);
    		if (nextFlag > 0) {
	    		Object[] params = new Object[ 3+TQ_MX_IDS.size() ];
	    		params[0] = TQ_TUBE_NUM_DNA;
	    		params[1] = TQ_TUBE_NUM_RNA;
	    		params[2] = TQ_MX_REMARK;
	    		for (int i = 0; i < TQ_MX_IDS.size(); i++) {
	        		params[ 3+i ] = TQ_MX_IDS.get(i);
	    		}
	    		updateJdbcTemplate.update(sqlUpdate, params);
    		} else {
    			return new CurrResponseResolve(1).put("msg", "提取任务单状态已发生变化, 不能修改明细, 请刷新主列表、明细列表").put(responseMessageParameter).getData();
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/sampleQC/tqTaskOrder/batchEditD", e);
			log.error("/berry/project/sampleQC/tqTaskOrder/batchEditD", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 提取任务单: 移除明细
    @RequestMapping("/delD")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delD(@RequestBody JSONObject data) {
    	
    	try {
    		String TQ_ID = data.getString("TQ_ID");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		// 验证主单状态
    		String selectSQL_M_check = "SELECT COUNT(1) FROM BR_TQ_INFO WHERE ID=? AND STATUS IN ('草稿','退回')";
    		// 更新入口表: 清空关联
    		String updateSQL_RK = "UPDATE BR_MODUAL_TQ"
    				+ " SET TQ_TASK_ID=NULL,TQ_TASK_MX_ID=NULL,OBJ_FLAG=NULL"
    				+ " WHERE TQ_TASK_MX_ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 删除明细
    		String delSQL_MX = "DELETE FROM BR_TQ_MX_INFO WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新主表: 样本数量
    		String updateSQL_M = "UPDATE BR_TQ_INFO BTI"
    				+ " SET TQ_SAMPLE_NUMBER=(SELECT COUNT(1) FROM BR_TQ_MX_INFO BTMI WHERE BTMI.TQ_ID=BTI.ID)"
    				+ " WHERE BTI.ID=?";
    		
    		// 查询主表状态, 满足条件继续执行移除
    		int i = updateJdbcTemplate.queryForObject(selectSQL_M_check, Integer.class, TQ_ID);
    		if (i > 0) {
    			// 更新入口表: 清空关联
        		updateJdbcTemplate.update(delSQL_MX, ids.toArray());
        		// 删除明细
        		updateJdbcTemplate.update(updateSQL_RK, ids.toArray());
        		// 更新主表: 样本数量
        		updateJdbcTemplate.update(updateSQL_M, TQ_ID);
        		
    		} else {
    			return new CurrResponseResolve(1).put("msg", "提取任务单状态已发生变化, 不能移除明细, 请刷新主列表、明细列表").put(responseMessageParameter).getData();
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/sampleQC/tqTaskOrder/delD", e);
			log.error("/berry/project/sampleQC/tqTaskOrder/delD", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 提取任务单: 移除主单
    @RequestMapping("/delM")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delM(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		// 获取状态为草稿的ID
    		String selectSQL_M = "SELECT ID FROM BR_TQ_INFO WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS IN ('草稿','退回')";
    		
    		List<String> mIdList = queryJdbcTemplate.queryForList(selectSQL_M, String.class, ids.toArray());
    		
    		mIdList = mIdList==null ? new ArrayList<String>() : mIdList;
    		
    		if (mIdList.size() > 0) {
	    		// 更新入口表: 清空关联
	    		String updateSQL_RK = "UPDATE BR_MODUAL_TQ"
	    				+ " SET TQ_TASK_ID=NULL,TQ_TASK_MX_ID=NULL,OBJ_FLAG=NULL"
	    				+ " WHERE TQ_TASK_ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
	    		updateJdbcTemplate.update(updateSQL_RK, mIdList.toArray());
	    		
	    		// 删除明细
	    		String delSQL_MX = "DELETE FROM BR_TQ_MX_INFO WHERE TQ_ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
	    		updateJdbcTemplate.update(delSQL_MX, mIdList.toArray());
	    		
	    		// 删除主单
	    		String delSQL_M = "DELETE FROM BR_TQ_INFO WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
	    		updateJdbcTemplate.update(delSQL_M, mIdList.toArray());
    		}
    		
    		CurrResponseResolve crr = new CurrResponseResolve(1).put(responseMessageParameter);
    		if (ids.size() > mIdList.size()) {
    			crr.put("msg", "提交了 "+ids.size()+" 行提取任务单数据, 删除了 "+mIdList.size()+" 行,未处理 "+(ids.size() - mIdList.size())+" 行;"
    					+ "<br>提取任务单状态已发生变化, 不能移除明细, 请刷新主列表、明细列表");
    		}
	    	return crr.getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/sampleQC/tqTaskOrder/delM", e);
			log.error("/berry/project/sampleQC/tqTaskOrder/delM", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
