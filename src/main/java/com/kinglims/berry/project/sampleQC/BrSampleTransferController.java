package com.kinglims.berry.project.sampleQC;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/project/sampleQC/sampleTransfer")
@Slf4j
public class BrSampleTransferController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 添加明细
    @RequestMapping("/addDetails")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject addDetails(@RequestBody JSONObject data) {
    	
    	try {
    		String SAMPLE_TRANSFER_ID = data.getString("SAMPLE_TRANSFER_ID");
    		JSONArray sampleArray = data.getJSONArray("sampleArray");
    		
    		String sqlQueryM = "SELECT COUNT(1) FROM BR_SAMPLE_TRANSFER WHERE ID=? AND STATUS='草稿'";
			int countRS = queryJdbcTemplate.queryForObject(sqlQueryM, Integer.class, SAMPLE_TRANSFER_ID);
    		if (countRS == 0) {
    			return new CurrResponseResolve(-1).put("msg", "主单不是草稿状态").put(responseMessageParameter).getData();
    		}
    		
    		// 添加选中数据
    		String sqlINSERT = "INSERT INTO BR_SAMPLE_TRANSFER_DETAIL"
    		                 + " (ID,SAMPLE_TRANSFER_ID,SAMPLE_ID,SAMPLE_CODE,SAMPLE_NAME,CREATOR,CREATTIME,LASTUPDATOR,LASTUPDATETIME,LOGINCOMPANY)"
    				         + " VALUES (?,?,?,?,?,?,?,?,?,?)";
    		for (int i = 0; i < sampleArray.size(); i++) {
    			JSONObject sample = sampleArray.getJSONObject(i);
    			String SAMPLE_ID = sample.getString("ID");
    			String SAMPLE_CODE = sample.getString("SAMPLE_CODE");
    			String SAMPLE_NAME = sample.getString("SAMPLE_NAME");
    			Object[] params = { SysBasic.getUUID(), SAMPLE_TRANSFER_ID, SAMPLE_ID, SAMPLE_CODE, SAMPLE_NAME, null, new Date(), null, new Date(), null };
    			updateJdbcTemplate.update(sqlINSERT, params);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/sampleQC/sampleTransfer/addDetails", e);
			log.error("/berry/project/sampleQC/sampleTransfer/addDetails", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除明细
    @RequestMapping("/delDetails")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delDetails(@RequestBody JSONObject data) {
    	
    	try {
    		String SAMPLE_TRANSFER_ID = data.getString("SAMPLE_TRANSFER_ID");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sqlQueryM = "SELECT COUNT(1) FROM BR_SAMPLE_TRANSFER WHERE ID=? AND STATUS='草稿'";
			int countRS = queryJdbcTemplate.queryForObject(sqlQueryM, Integer.class, SAMPLE_TRANSFER_ID);
    		if (countRS == 0) {
    			return new CurrResponseResolve(-1).put("msg", "主单不是草稿状态").put(responseMessageParameter).getData();
    		}
    		
    		String sqlDelD = "DELETE BR_SAMPLE_TRANSFER_DETAIL WHERE ID=?";
			for (String id : ids) {
    			updateJdbcTemplate.update(sqlDelD, id);
			}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/sampleQC/sampleTransfer/delDetails", e);
			log.error("/berry/project/sampleQC/sampleTransfer/delDetails", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除主单
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sqlDelM = "DELETE BR_SAMPLE_TRANSFER WHERE ID=? AND STATUS='草稿'";
    		String sqlDelD = "DELETE BR_SAMPLE_TRANSFER_DETAIL WHERE SAMPLE_TRANSFER_ID=?";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(sqlDelM, id);
    			if (i > 0) {
    				// 删除明细表
    				updateJdbcTemplate.update(sqlDelD, id);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/sampleQC/sampleTransfer/del", e);
			log.error("/berry/project/sampleQC/sampleTransfer/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 确认转移
    @RequestMapping("/confirmTransfer")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject confirmTransfer(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sqlINSERT = "UPDATE BR_SAMPLE_TRANSFER SET SX_COMMIT_STATUS='已确认转移'"
    						+ " WHERE SX_COMMIT_STATUS IS NULL AND ID=?";
    		// 更新: 确认转移状态
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(sqlINSERT, id);
    			if (i > 0) {
    				// 逻辑处理，更新样本信息
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/project/sampleQC/sampleTransfer/confirmTransfer", e);
			log.error("/berry/project/sampleQC/sampleTransfer/confirmTransfer", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
