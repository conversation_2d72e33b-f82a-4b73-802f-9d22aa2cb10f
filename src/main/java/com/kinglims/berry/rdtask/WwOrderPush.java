package com.kinglims.berry.rdtask;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.berry.automation.RowSingleController;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.system.SysBasic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
@RestController
@RequestMapping("/system/jdbc")
public class WwOrderPush {
    Date date = new Date();
    //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    SimpleDateFormat dateFormat2 = new SimpleDateFormat("MM/dd/yyyy");
    Calendar cal=Calendar.getInstance();
    Date now = new Date();
    String urlhtml = "";
    @Autowired
    private JdbcTemplate queryJdbcTemplate;
    @Autowired
    private HttpCommonController httpCommonController;
    @Autowired
    private RowSingleController rowSingleController;
    @RequestMapping("/ww/orderpush")
    public JSONObject DELIVERY(@RequestBody JSONObject data) throws Exception {
        List ids = (List) data.get("Ids");
        urlhtml = (String) data.get("urlhtml");
        JSONObject jsonObject = new JSONObject();
        List list = new ArrayList();
        String sj = (String) data.get("ts");
        String ITEM_TEXT = "委外订单接口";
        String sql = "select * from SYS_DATA_DICT where ITEM_TEXT = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ITEM_TEXT);
        Map map = result.get(0);
            if (sj.equals("手动模式")) {
                for (Object id : ids) {
                    String Ids2 = id.toString();
                    jsonObject = Ts(Ids2);
             //       log.info("----------jsonObject-------" + jsonObject);
                    list.add(jsonObject);
                }
            } else if (sj.equals((String) map.get("ITEM_CODE"))) {
                //判断推送日期是否符合规定
                for (Object id : ids) {
                    String Ids2 = id.toString();
                    list.add(Ts(Ids2));
                }
            } else {
                return new CurrResponseResolve(-1).put("apiData", "自动推送无法执行手动推送功能").getData();
            }
        return new CurrResponseResolve(1).put("apiData", list).getData();
    }




    public JSONObject Ts(String ID) throws Exception {
        String sql = "select * from BIO_WW_ORDER where ID = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ID);
        String ORDER_ID = (String) result.get(0).get("ID");
        String sql2 = "select * from BIO_WW_ORDERZB where ORDER_ID = ? ORDER BY DEFINE29 ASC";
        List<Map<String, Object>> result2 = queryJdbcTemplate.queryForList(sql2, ORDER_ID);
        //延时3000毫秒
        // Thread.currentThread().sleep(10000);
        String str7 = "</body></omorder></ufinterface>";
        String str8 = "";
        String str9 = "";

        Map str = (Map) result.get(0);
        Object s4=SysBasic.toTranDateByObject(str.get("BILLDATE"));
        Object IMPUTATIONDATA= SysBasic.toTranDateByObject(str.get("IMPUTATIONDATA"));
        String BILLDATE = null;
        if(s4 != null){
            BILLDATE = dateFormat.format(s4);
        }
        //报文
        String str1 = "<?xml version=\"1.0\" encoding=\"utf-8\"?><ufinterface sender=\"020\" receiver=\"u8\" roottag=\"omorder\" docid=\"69382429\" proc=\"add\" codeexchanged=\"N\" exportneedexch=\"N\" paginate=\"0\" display=\"委外订单\" family=\"委外管理\" dynamicdate=\""
    //    String str1 = "<?xml version=\"1.0\" encoding=\"utf-8\"?><ufinterface sender=\"997\" receiver=\"u8\" roottag=\"omorder\" docid=\"69382429\" proc=\"add\" codeexchanged=\"N\" exportneedexch=\"N\" paginate=\"0\" display=\"委外订单\" family=\"委外管理\" dynamicdate=\""
                +dateFormat2.format(IMPUTATIONDATA)+"\" maxdataitems=\"20000\" bignoreextenduserdefines=\"n\" timestamp=\"0x00000000030A33A3\" lastquerydate=\""
                +dateFormat.format(IMPUTATIONDATA)+"\"><omorder>";
        str8 = "<header>"
                +"<code>"+ SysBasic.toTranStringByObject(str.get("CODE")) +"</code>"
                +"<date>"+ dateFormat.format(IMPUTATIONDATA) +"</date>"
                +"<vendorcode>"+ SysBasic.toTranStringByObject(str.get("VENDORCODE")) +"</vendorcode>"
                +"<deptcode>"+ SysBasic.toTranStringByObject(str.get("DEPTCODE")) +"</deptcode>"
                +"<purchase_type_code>"+ SysBasic.toTranStringByObject(str.get("PURCHASE_TYPE_CODE")) +"</purchase_type_code>"
                +"<ordertype>"+ SysBasic.toTranStringByObject(str.get("ORDERTYPE")) +"</ordertype>"
                +"<operation_type_code>"+ SysBasic.toTranStringByObject(str.get("OPERATION_TYPE_CODE")) +"</operation_type_code>"
                +"<currency_name>"+ SysBasic.toTranStringByObject(str.get("CURRENCY_NAME")) +"</currency_name>"
                +"<currency_rate>"+ SysBasic.toTranStringByObject(str.get("CURRENCY_RATE")) +"</currency_rate>"
                +"<tax_rate>"+ SysBasic.toTranStringByObject(str.get("TAX_RATE")) +"</tax_rate>"
                +"<maker>"+ SysBasic.toTranStringByObject(str.get("MAKER")) +"</maker>"
                +"<define10>"+ SysBasic.toTranStringByObject(str.get("DEFINE10")) +"</define10>"
                +"<define1>"+ SysBasic.toTranStringByObject(str.get("DEFINE1")) +"</define1>"
                +"</header><body>";

        for (int j = 0; j < result2.size(); j++) {
            Map map2 = (Map) result2.get(j);
            Object s1=SysBasic.toTranDateByObject(map2.get("ARRIVEDATE"));
            String split1 = null;
            if(s1 != null){
                split1 = dateFormat.format(s1);
            }
            Object s2=SysBasic.toTranDateByObject(map2.get("STARTDATE"));
            String STARTDATE = null;
            if(s2 != null){
                STARTDATE = dateFormat.format(s2);
            }
            int a = j + 1;
            String str11 = "<entry>"
                   // + "<order_id>" + map2.get("ID") + "</order_id>"
                   // + "<modetailsid>" + SysBasic.toTranStringByObject(map2.get("MODETAILSID")) + "</modetailsid>" //
                    + "<inventorycode>" + SysBasic.toTranStringByObject(map2.get("INVENTORYCODE")) + "</inventorycode>"
                    + "<quantity>" + SysBasic.toTranStringByObject(map2.get("QUANTITY")) + "</quantity>"
                    + "<item_code>" + SysBasic.toTranStringByObject(map2.get("ITEM_CODE")) + "</item_code>"
                    + "<item_name><![CDATA[" + SysBasic.toTranStringByObject(map2.get("ITEM_NAME")) + "]]></item_name>"
                    + "<define33>" + SysBasic.toTranStringByObject(map2.get("DEFINE33")) + "</define33>"
                    + "<define28>" + SysBasic.toTranStringByObject(map2.get("DEFINE28")) + "</define28>"
                    + "<define31><![CDATA[" + SysBasic.toTranStringByObject(map2.get("DEFINE31")) + "]]></define31>"
                    + "<item_class>" + SysBasic.toTranStringByObject(map2.get("ITEM_CLASS")) + "</item_class>"
                    + "<arrivedate>" + split1 + "</arrivedate>"
                    + "<startdate>" + STARTDATE + "</startdate>"
               //     + "<define23><![CDATA[" + SysBasic.toTranStringByObject(map2.get("DEFINE23")) + "]]></define23>"
                    + "<define23><![CDATA[" + SysBasic.toTranStringByObject(map2.get("CBDEFINE12")) + "]]></define23>"
                    + "<define32><![CDATA[" + SysBasic.toTranStringByObject(map2.get("DEFINE32")) + "]]></define32>"
                    + "<money>" + SysBasic.toTranStringByObject(map2.get("MONEY")) + "</money>"
                    + "<tax>" + SysBasic.toTranStringByObject(map2.get("TAX")) + "</tax>"
                    + "<taxrate>" + SysBasic.toTranStringByObject(map2.get("TAXRATE")) + "</taxrate>"
              //      + "<cbdefine12>" + SysBasic.toTranStringByObject(map2.get("CBDEFINE12")) + "</cbdefine12>"
                    + "<bomid>" + SysBasic.toTranStringByObject(map2.get("BOMID")) + "</bomid>"
                    + "<define29>" + SysBasic.toTranStringByObject(map2.get("DEFINE29")) + "</define29>"
                    + "<ivouchrowno>" + a + "</ivouchrowno>"
                    + "<details ivouchrowno=\"" + a + "\">";

            //String ORDER1_ID = (String) result2.get(j).get("ID");
            String ORDER1_ID = (String) map2.get("ID");
            String sql3 = "select * from BIO_WW_ORDERZBMX where ORDERZB_ID = ?  ORDER BY RID ASC";
            List<Map<String, Object>> result3 = queryJdbcTemplate.queryForList(sql3, ORDER1_ID);
            String str10 = "";
            for (int i = 0; i < result3.size(); i++) {
                Map map3 = (Map) result3.get(i);
                Object s=SysBasic.toTranDateByObject(map3.get("REQUIREDDATE"));
                String split = null;
                if(s != null){
                    split = dateFormat.format(s);
                }
                String str12 = "<entrys>"
                       // + "<orderzb_id>" + map3.get("ORDERZB_ID") + "</orderzb_id>"
                     //   + "<modetailsid>" + SysBasic.toTranStringByObject(map3.get("MODETAILSID")) + "</modetailsid>"
                        + "<inventorycode>" + SysBasic.toTranStringByObject(map3.get("INVENTORYCODE")) + "</inventorycode>"
                        + "<quantity>" + SysBasic.toTranStringByObject(map3.get("QUANTITY")) + "</quantity>"
                        + "<requireddate>" + split + "</requireddate>"
                       // + "<define24><![CDATA[" + SysBasic.toTranStringByObject(map3.get("DEFINE24")) + "]]></define24>"
                        + "<baseqtyn>" + SysBasic.toTranStringByObject(map3.get("BASEQTYN")) + "</baseqtyn>"
                        + "<baseqtyd>" + SysBasic.toTranStringByObject(map3.get("BASEQTYD")) + "</baseqtyd>"
                        + "<unitid>" + SysBasic.toTranStringByObject(map3.get("UNITID")) + "</unitid>"
                        + "<fvgty>" + SysBasic.toTranStringByObject(map3.get("FVGTY")) + "</fvgty>"
                        + "<wiptype>" + SysBasic.toTranStringByObject(map3.get("WIPTYPE")) + "</wiptype>"
                        + "<opcomponentid>" + SysBasic.toTranStringByObject(map3.get("OPCOMPONENTID")) + "</opcomponentid>"
                        + "<whcode>" + SysBasic.toTranStringByObject(map3.get("WHCODE")) + "</whcode>"
                        + "<usequantity>" + SysBasic.toTranStringByObject(map3.get("USEQUANTITY")) + "</usequantity>"
                        + "<sendtype>" + SysBasic.toTranStringByObject(map3.get("SENDTYPE")) + "</sendtype>"
                  //      + "<cbdefine12>" + SysBasic.toTranStringByObject(map2.get("CBDEFINE12")) + "</cbdefine12>"
                        + "</entrys>";
                str10 += str12;
            }
            str9 = str9 + str11+ str10 +"</details></entry>";
        }
        String all = str1 + str8 + str9 + str7;
        //推送
        JSONObject date = new JSONObject();

  //     log.info("++++++++++++++++"+all);
        //date.put("url", "http://bj01ufi02.bmk.local/U8EAI/import.asp");
        String url = urlhtml + "/U8EAI/import.asp";
        date.put("url", url);
        date.put("outContentType", "xml");
        date.put("outCharset", "utf-8");
        date.put("inCharset", "utf-8");
        date.put("bodyParams", all);
        return  httpCommonController.apiCommonPost(date);
    }
}