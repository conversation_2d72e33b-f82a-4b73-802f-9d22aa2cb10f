package com.kinglims.berry.rdtask;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.system.jdbc.handler.logic.UpdateTables;
import com.kinglims.framework.utils.system.SysBasic;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@Slf4j
public class AganDataReturnRunCompenSator extends QuartzJobBean {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private UpdateTables updateTables;

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        //调用补偿回传数据-正常回传
       dataZc();
        //调用补偿回传数据-异常回传
       dataException();

    }

    private  void dataZc(){
        //1.查询需要补偿的回传数据-正常回传
        String sqldata = "SELECT ONT_ID FROM BIO_ADD_DATA_ONT where RUN_COMPENSATOR = 1 AND RUN_ID is null GROUP BY ONT_ID";
        List<Map<String, Object>> data = queryJdbcTemplate.queryForList(sqldata);


        //正常数据回传补偿
        log.info("正在执行回传数据补偿任务，任务数:"+data.size());
        if( data == null || data.size() < 1){
            return;
        }

        for(int i =0;i<data.size();i++){
            //2.查询run_id
            String ontid = SysBasic.toTranStringByObject(data.get(i).get("ONT_ID"));
            String sqlrun = "select ID,RUN_ID from BIO_ONT_RUN_INFOMX where ONT_ID = ?";
            List<Map<String, Object>> datarun = queryJdbcTemplate.queryForList(sqlrun, ontid);
            if( datarun == null || datarun.size() < 1){
                continue;
            }

            //3.添加runid
            String runid = SysBasic.toTranStringByObject(datarun.get(i).get("RUN_ID"));
            String updateSql = "UPDATE BIO_ADD_DATA_ONT SET RUN_ID = ? WHERE  ONT_ID = ?";
            updateTables.updateTableObjecsBySqlcode(updateSql,runid,ontid);
        }

    }


    private  void dataException(){

        //1.查询需要补偿的回传数据-异常回传
        String sqldataexception = "SELECT ONT_ID FROM BIO_ADD_DATA_ONT_EXCEPTION where RUN_COMPENSATOR = 1 AND RUN_ID is null GROUP BY ONT_ID";
        List<Map<String, Object>> dataexception = queryJdbcTemplate.queryForList(sqldataexception);


        //异常数据回传补偿
        log.info("正在执行异常回传数据补偿任务，任务数:"+dataexception.size());
        if( dataexception == null || dataexception.size() < 1){
            return;
        }
        for(int i =0;i<dataexception.size();i++){
            //2.查询run_id
            String ontid = SysBasic.toTranStringByObject(dataexception.get(i).get("ONT_ID"));
            String sqlrun = "select ID,RUN_ID from BIO_ONT_RUN_INFOMX where ONT_ID = ?";
            List<Map<String, Object>> datarun = queryJdbcTemplate.queryForList(sqlrun, ontid);
            if( datarun == null || datarun.size() < 1){
                continue;
            }

            //3.添加runid
            String runid = SysBasic.toTranStringByObject(datarun.get(i).get("RUN_ID"));
            String updateSql = "UPDATE BIO_ADD_DATA_ONT_EXCEPTION SET RUN_ID = ? WHERE  ONT_ID = ?";
            updateTables.updateTableObjecsBySqlcode(updateSql,runid,ontid);
        }

    }


}
