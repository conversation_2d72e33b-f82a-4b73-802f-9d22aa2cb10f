package com.kinglims.berry.rdtask;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.system.SysBasic;
import com.kinglims.u8.baimaike.org.entity.RdRecords09;
import com.kinglims.u8.baimaike.org.service.GenerateMaterialOrder;
import com.kinglims.u8.baimaike.org.service.GenerateMaterialOrderProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
@RestController
@RequestMapping("/system/jdbc")
public class RdRecord09 {

    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
    Calendar cal = Calendar.getInstance();
    Date now = new Date();

    @Autowired
    private JdbcTemplate queryJdbcTemplate;
    @Autowired
    private HttpCommonController httpCommonController;

    String urlhtml = "";
    @RequestMapping("/rd/r09")
    public JSONObject Rd(@RequestBody JSONObject data) throws Exception {
        List ids = (List) data.get("Ids");
        JSONObject jsonObject = new JSONObject();
        List<String> list = new ArrayList();
        String sj = (String) data.get("ts");
        String ITEM_TEXT = "材料出库单-研发";
        String sql = "select * from SYS_DATA_DICT where ITEM_TEXT = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ITEM_TEXT);
        Map map = result.get(0);
        String days = (String) map.get("NDAYPUSH");
        int days1 = Integer.parseInt(days);
        int day = cal.get(Calendar.DAY_OF_MONTH);
        //判断推送模式
            if ("手动模式".equals(sj)) {
                //判断推送日期是否符合规定
                for (Object id : ids) {
                    String Ids2 = id.toString();
                    list.add(Ts(Ids2));
                }
            } else if (sj.equals((String) map.get("ITEM_CODE"))) {
                for (Object id : ids) {
                    String Ids2 = id.toString();
                    list.add(Ts(Ids2));
                }
            } else {
                return new CurrResponseResolve(-1).put("apiData", "自动推送无法执行手动推送功能").getData();
            }
        return new CurrResponseResolve(1).put("apiData", list).getData();
    }

    public String Ts(String ID) throws Exception {
        String sql = "select * from BIO_GOO_RDRECORD09 where ID = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ID);
        String RDRECORD09_ID = (String) result.get(0).get("ID");

        String sql2 = "select * from BIO_GOO_RDRECORDS09MX where RDRECORD09_ID = ?";
        List<Map<String, Object>> result2 = queryJdbcTemplate.queryForList(sql2, RDRECORD09_ID);
        //com.kinglims.u8.baimaike.org.entity.RdRecord09 rrList = new com.kinglims.u8.baimaike.org.entity.RdRecord09();
        com.kinglims.u8.baimaike.org.entity.RdRecord09 rrList = new com.kinglims.u8.baimaike.org.entity.RdRecord09();
        Map str = (Map) result.get(0);
        Object s1 = SysBasic.toTranDateByObject(str.get("OUTBOUND_DATE"));
        Object s2 = SysBasic.toTranDateByObject(str.get("DEFINE4"));
        Object s3 = SysBasic.toTranDateByObject(str.get("DEFINE6"));
//        Object s4 = SysBasic.toTranDateByObject(str.get("DEFINE2"));
        String OUTBOUND_DATE = null;
        String DEFINE4 = null;
        String DEFINE6 = null;
//        String DEFINE2 = null;
        if(s1 != null){
            OUTBOUND_DATE = dateFormat2.format(s1);
        }
        if(s2 != null){
            DEFINE4 = dateFormat2.format(s2);
        }
        if(s3 != null){
            DEFINE6 = dateFormat2.format(s3);
        }/*
        if(s4 != null){
            DEFINE2 = dateFormat.format(s4);
        }*/

        rrList.setVouchtype(SysBasic.toTranStringByObject(str.get("VOUCHTYPE")));//单据类型 - 固定
        rrList.setBusinesstype(SysBasic.toTranStringByObject(str.get("BUSINESSTYPE")));//业务类型 - 固定
        rrList.setWarehousecode(SysBasic.toTranStringByObject(str.get("WAREHOUSECODE")));//仓库 - 固定
        rrList.setDate(OUTBOUND_DATE);//出库时间 - 当天
        rrList.setCode(SysBasic.toTranStringByObject(str.get("OUTBOUND_CODE")));//出库单号 - 自增,不可重复
        rrList.setReceivecode(SysBasic.toTranStringByObject(str.get("RECEIVECODE")));//出库类别 - 固定
        rrList.setDepartmentcode(SysBasic.toTranStringByObject(str.get("DEPARTMENTCODE")));//部门编码 - 固定
        rrList.setMemory(SysBasic.toTranStringByObject(str.get("MEMORY")));//备注
        rrList.setMaker(SysBasic.toTranStringByObject(str.get("MAKER")));//制单人
        rrList.setDefine4(DEFINE4);//试验结束日期 - YYYY-MM-DD HH24:MI:SS
        rrList.setDefine6(DEFINE6);//试验开始日期 - YYYY-MM-DD HH24:MI:SS
        rrList.setDefine10(SysBasic.toTranStringByObject(str.get("DEFINE10")));//部门 - 固定
        rrList.setDefine12(SysBasic.toTranStringByObject(str.get("DEFINE12")));//实验员
        rrList.setDefine1(SysBasic.toTranStringByObject(str.get("DEFINE1")));//华开单据号
        //rrList.setDefine2(DEFINE2);
        rrList.setDetail( new RdRecords09[result2.size()] );//表体明细
        for (int i = 0; i < result2.size(); i++) {
            Map str1 = (Map) result2.get(i);
            Object s5 = SysBasic.toTranDateByObject(str1.get("VALIDDATE"));
            String VALIDDATE = null;
            if(s5 != null){
                VALIDDATE = dateFormat2.format(s5);
            }
            //明细1
            rrList.getDetail()[i] = new RdRecords09();
            rrList.getDetail()[i].setBarcode(SysBasic.toTranStringByObject(str1.get("BARCODE")));//材料代码 - 空
            rrList.getDetail()[i].setInventorycode(SysBasic.toTranStringByObject(str1.get("INVENTORYCODE")));//材料编码
            //数量转换
            String QUANTITY = SysBasic.toTranStringByObject(str1.get("QUANTITY"));
            BigDecimal QUANTITY_bd = new BigDecimal(QUANTITY);
            rrList.getDetail()[i].setQuantity(QUANTITY_bd);//数量
            rrList.getDetail()[i].setCmassunitname(SysBasic.toTranStringByObject(str1.get("CMASSUNITNAME")));//主计量单位
            rrList.getDetail()[i].setAssitantunitname(SysBasic.toTranStringByObject(str1.get("ASSITANTUNITNAME")));//辅计量单位
            //rrList.getDetail()[i].setIrate((String) str1.get("IRATE"));//换算率
            //rrList.getDetail()[i].setNumber((String) str1.get("NUM"));//件数
            //rrList.getDetail()[i].setPrice((String) str1.get("PRICE"));//单价
            //rrList.getDetail()[i].setCost((String) str1.get("COST"));//金额
            rrList.getDetail()[i].setSerial(SysBasic.toTranStringByObject(str1.get("SERIAL")));//批号
            rrList.getDetail()[i].setValiddate(VALIDDATE);//失效日期
            rrList.getDetail()[i].setItemclasscode(SysBasic.toTranStringByObject(str1.get("ITEMCLASSCODE")));//项目大类编码 - 固定
            rrList.getDetail()[i].setItemclassname(SysBasic.toTranStringByObject(str1.get("ITEMCLASSNAME")));//项目大类名称 - 固定
            rrList.getDetail()[i].setItemcode(SysBasic.toTranStringByObject(str1.get("ITEMCODE")));//项目编码
            rrList.getDetail()[i].setItemname(SysBasic.toTranStringByObject(str1.get("ITEMNAME")));//项目名称
            rrList.getDetail()[i].setDefine22(SysBasic.toTranStringByObject(str1.get("DEFINE22")));//工序
            rrList.getDetail()[i].setDefine24(SysBasic.toTranStringByObject(str1.get("DEFINE24")));//产品名称
            rrList.getDetail()[i].setDefine33(SysBasic.toTranStringByObject(str1.get("DEFINE33")));//项目期号
            rrList.getDetail()[i].setDefine32(SysBasic.toTranStringByObject(str1.get("DEFINE32")));//阶段
            rrList.getDetail()[i].setMemory(SysBasic.toTranStringByObject(str1.get("MEMORY")));//备注
        }
        GenerateMaterialOrder g = new GenerateMaterialOrderProxy();
        JSONObject jsonObject = new JSONObject();

        log.info("++++++++++++++++"+jsonObject.toJSONString(rrList));
        String sss = g.generateOtherOrder(rrList);
        //log.info("RdRecords09---------------:"+sss);

        System.out.println( sss );
        return sss;
    }
}





