package com.kinglims.berry.rdtask;

import com.kinglims.framework.utils.system.SysBasic;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RestController
@RequestMapping("/system/jdbc")
public class WwRequisition {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    @RequestMapping("/ww/requisiton")
    public JSONObject WwReq(@RequestBody String ID){
        JSONObject l = null;
        //String ID = "";
        System.out.println("++++++++++ID:++++++++++++++"+ID);
        String sql = "select * from BIO_WW_REQUISITION where ID = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ID);
        System.out.println("++++++++++result++++++++++++++"+result);
        Integer b = null;
        for (Map<String, Object> map : result) {
            b = WwReqMx((String) map.get("ID"));
            System.out.println(b);
        }
        return l;
    }
    public Integer WwReqMx(String REQUISITIONID){
        JSONObject l = null;
        String sql = "select * from BIO_WW_REQUISITION where REQUISITIONID = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, REQUISITIONID);
        System.out.println("++++++++++result2++++++++++++++"+result);
        Map map = new HashMap<>();
        Integer a = null;
        for (int i = 0; i < result.size(); i++) {
            String ID = SysBasic.getUUID();
            map.put("ID",result.get(i).get("ID"));
            map.put("CODE",result.get(i).get("CODE"));
            map.put("GENERATE_DATE",result.get(i).get("GENERATE_DATE"));
            map.put("DEPARTMENTCODE",result.get(i).get("DEPARTMENTCODE"));
            map.put("PURCHASETYPECODE",result.get(i).get("PURCHASETYPECODE"));
            map.put("BUSINESSTYPE",result.get(i).get("BUSINESSTYPE"));
            map.put("PUSH_STATE",result.get(i).get("PUSH_STATE"));
            List list = new ArrayList();
            for (Object key : map.keySet()) {
                list.add(key);
            }
            System.out.println("++++++++++list:jian++++++++++++++"+result);
            List list2 = new ArrayList();
            System.out.println("++++++++++list:zhi++++++++++++++"+result);
            for (Object value : map.values()) {
                list2.add(value);
            }
            String sql1 = "insert into BIO_WW_REQUISITION ( " + SysBasic.mergeListToString(list, ",") + ") values (" + SysBasic.getQuestionMarkBySzie(list2.size()) + ")";
            a = updateJdbcTemplate.update(sql1);
            bjZbMX(ID);
        }
        return a;
    }
    public Integer bjZbMX(String SID){
        Integer l = 0;
        String sql = "select * from BIO_WW_REQUISITIONMX where S_ID = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, SID);
        Map map = new HashMap<>();
        for (int i = 0; i < result.size(); i++) {
            String ID =SysBasic.getUUID();
            map.put("ID",result.get(i).get("ID"));
            map.put("REQUISITIONID",result.get(i).get("REQUISITIONID"));
            map.put("VENDORCODE",result.get(i).get("VENDORCODE"));
            map.put("INVENTORYCODE",result.get(i).get("INVENTORYCODE"));
            map.put("QUANTITY",result.get(i).get("QUANTITY"));
            map.put("TAXRATE",result.get(i).get("TAXRATE"));
            map.put("REQUIREDATE",result.get(i).get("REQUIREDATE"));
            map.put("ARRIVEDATE",result.get(i).get("ARRIVEDATE"));
            map.put("ITEM_CLASS",result.get(i).get("ITEM_CLASS"));
            map.put("ITEM_CODE",result.get(i).get("ITEM_CODE"));
            map.put("ITEM_NAME",result.get(i).get("ITEM_NAME"));
            map.put("DEFINE23",result.get(i).get("DEFINE23"));
            map.put("DEFINE24",result.get(i).get("DEFINE24"));
            map.put("DEFINE25",result.get(i).get("DEFINE25"));
            map.put("CURRENCY_NAME",result.get(i).get("CURRENCY_NAME"));
            map.put("CURRENCY_RATE",result.get(i).get("CURRENCY_RATE"));
            map.put("CBDEFINE2",result.get(i).get("CBDEFINE2"));
            List list = new ArrayList();
            for (Object key : map.keySet()) {
                list.add(key);
            }
            List list2 = new ArrayList();
            for (Object value : map.values()) {
                list2.add(value);
            }
            String sql1 = "insert into BIO_WW_REQUISITIONMX ( " + SysBasic.mergeListToString(list, ",") + ") values (" + SysBasic.getQuestionMarkBySzie(list2.size()) + ")";
            l = updateJdbcTemplate.update(sql1);
        }
        return l;
    }

}
