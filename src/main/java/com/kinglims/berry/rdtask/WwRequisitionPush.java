package com.kinglims.berry.rdtask;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.system.SysBasic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
@RestController
@RequestMapping("/system/jdbc")
public class WwRequisitionPush {
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    SimpleDateFormat dateFormat2 = new SimpleDateFormat("dd-MM-yyyy");
    Calendar cal = Calendar.getInstance();
    Date now = new Date();
    String urlhtml = "";
    @Autowired
    private JdbcTemplate queryJdbcTemplate;
    @Autowired
    private HttpCommonController httpCommonController;

    @RequestMapping("/ww/reqpush")
    public JSONObject DELIVERY(@RequestBody JSONObject data) throws Exception {
        urlhtml = (String) data.get("urlhtml");
        List ids = (List) data.get("Ids");
        JSONObject jsonObject = new JSONObject();
        List list = new ArrayList();
        String sj = (String) data.get("ts");
        String ITEM_TEXT = "委外请购接口";
        String sql = "select * from SYS_DATA_DICT where ITEM_TEXT = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ITEM_TEXT);
        Map map = result.get(0);

        String days = (String) map.get("NDAYPUSH");
        int days1 = Integer.parseInt(days);
        int day = cal.get(Calendar.DAY_OF_MONTH);
        //判断推送模式
        if (sj.equals("手动模式")) {
            for (Object id : ids) {
                String Ids2 = id.toString();
                jsonObject = Ts(Ids2);
             //   log.info("----------jsonObject-------" + jsonObject);
                list.add(jsonObject);
            }
        } else if (sj.equals((String) map.get("ITEM_CODE"))) {
            //判断推送日期是否符合规定
            if (days1 > day) {
                for (Object id : ids) {
                    String Ids2 = id.toString();
                    list.add(Ts(Ids2));
                }
            } else {
                return new CurrResponseResolve(-1).put("apiData", days1 + "号后无法推送，请于次月推送").getData();
            }
        } else {
            return new CurrResponseResolve(-1).put("apiData", "自动推送无法执行手动推送功能").getData();
        }

        //判断是否推送成功
        for (Object o : list) {
            JSONObject object = (JSONObject) o;
            log.info("++++++++++返回值------:" + o);
            int num = object.getInteger("code");
            if (num > 0) {

            } else {
                return new CurrResponseResolve(-1).put("apiData", list).getData();
            }
        }
        return new CurrResponseResolve(1).put("apiData", list).getData();
    }

    public JSONObject Ts(String ID) throws Exception {
        String sql = "select * from BIO_WW_REQUISITION where ID = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ID);
        String REQUISITIONID = (String) result.get(0).get("REQUISITIONID");
        String sql2 = "select * from BIO_WW_REQUISITIONMX where REQUISITIONID = ?";
        List<Map<String, Object>> result2 = queryJdbcTemplate.queryForList(sql2, REQUISITIONID);
        String str1 = "<?xml version=\"1.0\" encoding=\"utf-8\"?><ufinterface sender=\"997\" receiver=\"u8\" roottag=\"purchaseapp\" docid=\"100338161\" proc=\"add\" codeexchanged=\"N\" exportneedexch=\"N\" paginate=\"0\" display=\"采购请购单\" family=\"采购管理\" dynamicdate=\""
                + dateFormat2.format(now) + "\" maxdataitems=\"20000\" bignoreextenduserdefines=\"n\" timestamp=\"0x00000000030B2ED2\" lastquerydate=\""
                + dateFormat.format(now) + "\"><purchaseapp>";
        String str7 = "</body></purchaseapp></ufinterface>";
        String str8 = "";
        String str9 = "";
        Map str = (Map) result.get(0);
        Object s1 = SysBasic.toTranDateByObject(str.get("GENERATE_DATE"));
        String GENERATE_DATE = dateFormat.format(s1);
        str8 = "<header>"
                + "<code>" + str.get("CODE") + "</code>"
                + "<date>" + GENERATE_DATE + "</date>"
                + "<departmentcode>" + SysBasic.toTranStringByObject(str.get("DEPARTMENTCODE")) + "</departmentcode>"
                + "<purchasetypecode>" + SysBasic.toTranStringByObject(str.get("PURCHASETYPECODE")) + "</purchasetypecode>"
                + "<businesstype>" + SysBasic.toTranStringByObject(str.get("BUSINESSTYPE")) + "</businesstype>"
                + "</header><body>";
        for (int j = 0; j < result2.size(); j++) {
            Map map2 = (Map) result2.get(j);
            Object s2 = SysBasic.toTranDateByObject(str.get("REQUIREDATE"));
            String REQUIREDATE = dateFormat.format(s2);
            Object s3 = SysBasic.toTranDateByObject(str.get("ARRIVEDATE"));
            String ARRIVEDATE = dateFormat.format(s3);
            String str11 = "<entry>"
                    + "<vendorcode>" + SysBasic.toTranStringByObject(map2.get("VENDORCODE")) + "</vendorcode>"
                    + "<inventorycode>" + SysBasic.toTranStringByObject(map2.get("INVENTORYCODE")) + "</inventorycode>"
                    + "<quantity>" + SysBasic.toTranStringByObject(map2.get("QUANTITY")) + "</quantity>"
                    + "<taxrate>" + SysBasic.toTranStringByObject(map2.get("TAXRATE")) + "</taxrate>"
                    + "<requiredate>" + REQUIREDATE + "</requiredate>"
                    + "<arrivedate>" + ARRIVEDATE + "</arrivedate>"
                    + "<item_class>" + SysBasic.toTranStringByObject(map2.get("ITEM_CLASS")) + "</item_class>"
                    + "<item_code>" + SysBasic.toTranStringByObject(map2.get("ITEM_CODE")) + "</item_code>"
                    + "<item_name>" + SysBasic.toTranStringByObject(map2.get("ITEM_NAME")) + "</item_name>"
                    + "<currency_name>" + SysBasic.toTranStringByObject(map2.get("CURRENCY_NAME")) + "</currency_name>"
                    + "<currency_rate>" + SysBasic.toTranStringByObject(map2.get("CURRENCY_RATE")) + "</currency_rate>"
                    + "<define23>" + SysBasic.toTranStringByObject(map2.get("DEFINE23")) + "</define23>"
                    + "<define24>" + SysBasic.toTranStringByObject(map2.get("DEFINE24")) + "</define24>"
                    + "<define25>" + SysBasic.toTranStringByObject(map2.get("DEFINE25")) + "</define25>"
                    + "<cbdefine2>" + SysBasic.toTranStringByObject(map2.get("CBDEFINE2")) + "</cbdefine2>"
                    + "</entry>";
            str9 += str11;
        }
        String all = str1 + str8 + str9 + str7;
      //  log.info("委外请购接口报文："+all);
        String url = urlhtml + "/U8EAI/import.asp";

        JSONObject date = new JSONObject();
        date.put("url", url);
        date.put("outContentType", "xml");
        date.put("outCharset", "utf-8");
        date.put("inCharset", "utf-8");
        date.put("bodyParams", all);

        return httpCommonController.apiCommonPost(date);
    }
}
