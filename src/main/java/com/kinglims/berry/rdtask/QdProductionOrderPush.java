package com.kinglims.berry.rdtask;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.system.SysBasic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
@RestController
@RequestMapping("/system/jdbc")
public class QdProductionOrderPush {
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    SimpleDateFormat dateFormat2 = new SimpleDateFormat("MM/dd/yyyy");
    Calendar cal=Calendar.getInstance();
    Date now = new Date();
    String urlhtml = "";
    @Autowired
    private JdbcTemplate queryJdbcTemplate;
    @Autowired
    private HttpCommonController httpCommonController;
    @RequestMapping("/qd/pop")
    public JSONObject DELIVERY(@RequestBody JSONObject data) throws Exception {
        urlhtml = (String) data.get("urlhtml");
        List ids = (List) data.get("Ids");
        JSONObject jsonObject = new JSONObject();
        List list = new ArrayList();
        String sj = (String) data.get("ts");
        String ITEM_TEXT = "青岛U8生产订单接口";
        String sql = "select * from SYS_DATA_DICT where ITEM_TEXT = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ITEM_TEXT);
        Map map = result.get(0);
            //判断推送模式
            if (sj.equals("手动模式")) {
                for (Object id : ids) {
                    String Ids2 = id.toString();
                    jsonObject = Ts(Ids2);
                    //  log.info("----------jsonObject-------"+jsonObject);
                    list.add(jsonObject);
                }
            } else if (sj.equals((String) map.get("ITEM_CODE"))) {
                for (Object id : ids) {
                    String Ids2 = id.toString();
                    list.add(Ts(Ids2));
                }

        } else {
                return new CurrResponseResolve(-1).put("apiData", "自动推送无法执行手动推送功能").getData();
            }
        return new CurrResponseResolve(1).put("apiData", list).getData();
    }

    public JSONObject Ts(String ID) throws Exception {
        String sql = "select * from BIO_QD_PRODUCTION_ORDER where MOID = ? order by SORTSEQ";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ID);
        String str7 = "</MOrder></ufinterface>";
        String str8 = "";
        String str9 = "";
        Map str = (Map) result.get(0);
        Object s1= SysBasic.toTranDateByObject(str.get("STARTDATE"));
        Object IMPUTATIONDATA= SysBasic.toTranDateByObject(str.get("IMPUTATIONDATA"));
        String STARTDATE = null;
        if(s1 != null){
            STARTDATE = dateFormat.format(s1);
        }
        Object s2= SysBasic.toTranDateByObject(str.get("DUEDATE"));
        String DUEDATE = null;
        if(s2 != null){
            DUEDATE = dateFormat.format(s2);
        }
        String str1 = "<ufinterface sender=\"023\" receiver=\"u8\" roottag=\"MOrder\" docid=\"355266809\" proc=\"add\" codeexchanged=\"N\" exportneedexch=\"N\" paginate=\"0\" display=\"生产订单\" family=\"生产制造\" dynamicdate=\""
        //String str1 = "<ufinterface sender=\"998\" receiver=\"u8\" roottag=\"MOrder\" docid=\"355266809\" proc=\"add\" codeexchanged=\"N\" exportneedexch=\"N\" paginate=\"0\" display=\"生产订单\" family=\"生产制造\" dynamicdate=\""
                +dateFormat2.format(IMPUTATIONDATA)+"\" maxdataitems=\"20000\" bignoreextenduserdefines=\"n\" timestamp=\"0x00000000030B2ED2\" lastquerydate=\""
                +dateFormat.format(IMPUTATIONDATA)+"\"><MOrder>";
        str8 = "<Order>"
                +"<MoId>"+ SysBasic.toTranStringByObject(str.get("MOID"))+"</MoId>"
                +"<MoCode>"+ SysBasic.toTranStringByObject(str.get("MOCODE"))+"</MoCode>"
                + "<Define1>" + SysBasic.toTranStringByObject(str.get("DEFINE1")) + "</Define1>"
                + "</Order>" ;
        for (int i = 0; i <result.size() ; i++) {
            str = (Map) result.get(i);
            str8 +=  "<OrderDetail>"
                    + "<MoDId>" + SysBasic.toTranStringByObject(str.get("MODID"))+ "</MoDId>"
                    + "<MoId>" + SysBasic.toTranStringByObject(str.get("MOID"))+ "</MoId>"
                    + "<SortSeq>" + SysBasic.toTranStringByObject(str.get("SORTSEQ"))+ "</SortSeq>"
                    + "<MoClass>" + SysBasic.toTranStringByObject(str.get("MOCLASS"))+ "</MoClass>"
                    + "<MoTypeCode>" + SysBasic.toTranStringByObject(str.get("MOTYPECODE"))+ "</MoTypeCode>"
                    + "<Qty>" + SysBasic.toTranStringByObject(str.get("QTY"))+ "</Qty>"
                    + "<WhCode>" + SysBasic.toTranStringByObject(str.get("WHCODE"))+ "</WhCode>"
                    + "<MDeptCode>" + SysBasic.toTranStringByObject(str.get("MDEPTCODE"))+ "</MDeptCode>"
                    + "<InvCode>" + SysBasic.toTranStringByObject(str.get("INVCODE"))+ "</InvCode>"
                    + "<Free1>" + SysBasic.toTranStringByObject(str.get("FREE1"))+ "</Free1>"
                    + "<Free2>" + SysBasic.toTranStringByObject(str.get("FREE2"))+ "</Free2>"
                    + "<Free3>" + SysBasic.toTranStringByObject(str.get("FREE3"))+ "</Free3>"
                    + "<Free4>" + SysBasic.toTranStringByObject(str.get("FREE4"))+ "</Free4>"
                    + "<Free5>" + SysBasic.toTranStringByObject(str.get("FREE5"))+ "</Free5>"
                    + "<Free6>" + SysBasic.toTranStringByObject(str.get("FREE6"))+ "</Free6>"
                    + "<Free7>" + SysBasic.toTranStringByObject(str.get("FREE7"))+ "</Free7>"
                    + "<Free8>" + SysBasic.toTranStringByObject(str.get("FREE8"))+ "</Free8>"
                    + "<Free9>" + SysBasic.toTranStringByObject(str.get("FREE9"))+ "</Free9>"
                    + "<Free10>" + SysBasic.toTranStringByObject(str.get("FREE10"))+ "</Free10>"
                  //  + "<Define23>" + SysBasic.toTranStringByObject(str.get("DEFINE23"))+ "</Define23>"
                    + "<Define23>" + SysBasic.toTranStringByObject(str.get("CBDEFINE12"))+ "</Define23>"
                    + "<Define28>" + SysBasic.toTranStringByObject(str.get("DEFINE28"))+ "</Define28>"
                    + "<Define31><![CDATA[" + SysBasic.toTranStringByObject(str.get("DEFINE31"))+ "]]></Define31>"
                    + "<Define32><![CDATA[" + SysBasic.toTranStringByObject(str.get("DEFINE32"))+ "]]></Define32>"
                    + "<Define33>" + SysBasic.toTranStringByObject(str.get("DEFINE33"))+ "</Define33>"
                    + "<Remark><![CDATA[" + SysBasic.toTranStringByObject(str.get("Remark"))+ "]]></Remark>"
                    + "<Define24>" + SysBasic.toTranStringByObject(str.get("DEFINE24"))+ "</Define24>"
                    + "<Define25><![CDATA[" + SysBasic.toTranStringByObject(str.get("DEFINE25"))+ "]]></Define25>"
                    + "<Define29>" + SysBasic.toTranStringByObject(str.get("DEFINE29"))+ "</Define29>"
               //     + "<cbdefine12>" + SysBasic.toTranStringByObject(str.get("CBDEFINE12"))+ "</cbdefine12>"
                    +"<BomType>0</BomType><WIPType>5</WIPType>"   //U8优化要求新增。20220104
                    + "</OrderDetail>";
            str9 +=   "<MOrderDetail>"
                    + "<MoDId>" + str.get("MODID") + "</MoDId>"
                    + "<StartDate>" + STARTDATE + "</StartDate>"
                    + "<DueDate>" + DUEDATE + "</DueDate>"
                    + "</MOrderDetail>";

        }

        String all = str1 + str8 + str9 + str7;
       // log.info(all);
        String url = urlhtml + "/U8EAI/import.asp";
        JSONObject date = new JSONObject();
        //date.put("url", "http://bj01ufi02.bmk.local/U8EAI/import.asp");
        date.put("url", url);
        date.put("outContentType", "xml");
        date.put("outCharset", "utf-8");
        date.put("inCharset", "utf-8");
        date.put("bodyParams", all);
     //    log.info(SysBasic.getNowTime()+"   青岛U8生产订单："+ID+"\n 报文："+all);
//        return date;
        return httpCommonController.apiCommonPost(date);
    }
}
