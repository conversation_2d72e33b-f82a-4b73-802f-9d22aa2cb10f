package com.kinglims.berry.rdtask;


import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.system.SysBasic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
@RestController
@RequestMapping("/system/jdbc")
public class BjProductionOrderPush {


    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    SimpleDateFormat dateFormat2 = new SimpleDateFormat("MM/dd/yyyy");
    Calendar cal = Calendar.getInstance();
    Date now = new Date();

    @Autowired
    private JdbcTemplate queryJdbcTemplate;
    @Autowired
    private HttpCommonController httpCommonController;

    String urlhtml = "";

    @RequestMapping("/bj/pop")
    public JSONObject DELIVERY(@RequestBody JSONObject data) throws Exception {
        List ids = (List) data.get("Ids");
        JSONObject jsonObject = new JSONObject();
        urlhtml = (String) data.get("urlhtml");
        List list = new ArrayList();
        String sj = (String) data.get("ts");
        String ITEM_TEXT = "北京U8生产订单接口";
        String sql = "select * from SYS_DATA_DICT where ITEM_TEXT = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ITEM_TEXT);
        Map map = result.get(0);
            //判断推送模式
            if (sj.equals("手动模式")) {
                for (Object id : ids) {
                    String Ids2 = id.toString();
                    jsonObject = Ts(Ids2);
                    //  log.info("----------jsonObject-------"+jsonObject);
                    list.add(jsonObject);
                }
            } else if (sj.equals((String) map.get("ITEM_CODE"))) {
                for (Object id : ids) {
                    String Ids2 = id.toString();
                    list.add(Ts(Ids2));
                }

            } else {
                return new CurrResponseResolve(-1).put("apiData", "自动推送无法执行手动推送功能").getData();
            }
        return new CurrResponseResolve(1).put("apiData", list).getData();
    }





    public JSONObject Ts(String ID) throws Exception {


        String sql = "select * from BIO_BJ_PRODUCTION_ORDER where MOID = ?  order by SORTSEQ";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ID);

        String str7 = "</MOrder></ufinterface>";
        String str8 = "";
        String str9 = "";
        String str10 = "";
        Map str = (Map) result.get(0);
        Object s1 = SysBasic.toTranDateByObject(str.get("STARTDATE"));
        Object s2 = SysBasic.toTranDateByObject(str.get("DUEDATE"));
        Object IMPUTATIONDATA= SysBasic.toTranDateByObject(str.get("IMPUTATIONDATA"));
        String STARTDATE = null;
        String DUEDATE = null;
        if(s1 != null){
            STARTDATE = dateFormat.format(s1);
        }
        if(s2 != null){
            DUEDATE = dateFormat.format(s2);
        }

        if(IMPUTATIONDATA != null){
            IMPUTATIONDATA = dateFormat.format(IMPUTATIONDATA);
        }

        String str1 = "<ufinterface sender=\"020\" receiver=\"u8\" roottag=\"MOrder\" docid=\"355266809\" proc=\"add\" codeexchanged=\"N\" exportneedexch=\"N\" paginate=\"0\" display=\"生产订单\" family=\"生产制造\" dynamicdate=\""
     //   String str1 = "<ufinterface sender=\"997\" receiver=\"u8\" roottag=\"MOrder\" docid=\"355266809\" proc=\"add\" codeexchanged=\"N\" exportneedexch=\"N\" paginate=\"0\" display=\"生产订单\" family=\"生产制造\" dynamicdate=\""
                + IMPUTATIONDATA + "\" maxdataitems=\"20000\" bignoreextenduserdefines=\"n\" timestamp=\"0x00000000030B2ED2\" lastquerydate=\""
                + IMPUTATIONDATA + "\"><MOrder>";

        str8 = "<Order>"
                + "<MoId>" + SysBasic.toTranStringByObject(str.get("MOID")) + "</MoId>"
                + "<MoCode>" + SysBasic.toTranStringByObject(str.get("MOCODE")) + "</MoCode>"
                + "<Define1>" + SysBasic.toTranStringByObject(str.get("DEFINE1")) + "</Define1>"
                + "</Order>";

        for(int i = 0;i<result.size();i++){
            str = (Map) result.get(i);
            str8 +=   "<OrderDetail>"
                    + "<MoDId>" + SysBasic.toTranStringByObject(str.get("MODID")) + "</MoDId>"
                    + "<MoId>" + SysBasic.toTranStringByObject(str.get("MOID")) + "</MoId>"
                    + "<SortSeq>" + SysBasic.toTranStringByObject(str.get("SORTSEQ")) + "</SortSeq>"
                    + "<MoClass>" + SysBasic.toTranStringByObject(str.get("MOCLASS")) + "</MoClass>"
                    + "<MoTypeCode>" + SysBasic.toTranStringByObject(str.get("MOTYPECODE")) + "</MoTypeCode>"
                    + "<Qty>" + SysBasic.toTranStringByObject(str.get("QTY")) + "</Qty>"
                    + "<WhCode>" + SysBasic.toTranStringByObject(str.get("WHCODE")) + "</WhCode>"
                    + "<MDeptCode>" + SysBasic.toTranStringByObject(str.get("MDEPTCODE")) + "</MDeptCode>"
                    + "<BomType>" + SysBasic.toTranStringByObject(str.get("BOMTYPE")) + "</BomType>"
                    + "<InvCode>" + SysBasic.toTranStringByObject(str.get("INVCODE")) + "</InvCode>"
                    + "<Free1>" + SysBasic.toTranStringByObject(str.get("FREE1")) + "</Free1>"
                    + "<Free2>" + SysBasic.toTranStringByObject(str.get("FREE2")) + "</Free2>"
                    + "<Free3>" + SysBasic.toTranStringByObject(str.get("FREE3")) + "</Free3>"
                    + "<Free4>" + SysBasic.toTranStringByObject(str.get("FREE4")) + "</Free4>"
                    + "<Free5>" + SysBasic.toTranStringByObject(str.get("FREE5")) + "</Free5>"
                    + "<Free6>" + SysBasic.toTranStringByObject(str.get("FREE6")) + "</Free6>"
                    + "<Free7>" + SysBasic.toTranStringByObject(str.get("FREE7")) + "</Free7>"
                    + "<Free8>" + SysBasic.toTranStringByObject(str.get("FREE8")) + "</Free8>"
                    + "<Free9>" + SysBasic.toTranStringByObject(str.get("FREE9")) + "</Free9>"
                    + "<Free10>" + SysBasic.toTranStringByObject(str.get("FREE10")) + "</Free10>"
                 //   + "<Define23><![CDATA[" + SysBasic.toTranStringByObject(str.get("DEFINE23")) + "]]></Define23>"
                    + "<Define23><![CDATA[" + SysBasic.toTranStringByObject(str.get("CBDEFINE12")) + "]]></Define23>"
                    + "<Define28>" + SysBasic.toTranStringByObject(str.get("DEFINE28")) + "</Define28>"
                    + "<Define25><![CDATA[" + SysBasic.toTranStringByObject(str.get("DEFINE25")) + "]]></Define25>"
                    + "<Define31><![CDATA[" + SysBasic.toTranStringByObject(str.get("DEFINE31")) + "]]></Define31>"
                    + "<Define32><![CDATA[" + SysBasic.toTranStringByObject(str.get("DEFINE32")) + "]]></Define32>"
                    + "<Define33>" + SysBasic.toTranStringByObject(str.get("DEFINE33")) + "</Define33>"
                    + "<Remark><![CDATA[" + SysBasic.toTranStringByObject(str.get("REMARK")) + "]]></Remark>"
                    + "<Define24>" + SysBasic.toTranStringByObject(str.get("DEFINE24")) + "</Define24>"
                    + "<Define29>" + SysBasic.toTranStringByObject(str.get("DEFINE29")) + "</Define29>"
                 //   + "<cbdefine12>" + SysBasic.toTranStringByObject(str.get("CBDEFINE12")) + "</cbdefine12>"
              //      + "<cbdefine12>" + SysBasic.toTranStringByObject(str.get("CBDEFINE12")) + "</cbdefine12>"

                    + "</OrderDetail>";
            str10 += "<MOrderDetail>"
                    + "<MoDId>" + SysBasic.toTranStringByObject(str.get("MODID")) + "</MoDId>"
                    + "<StartDate>" + STARTDATE + "</StartDate>"
                    + "<DueDate>" + DUEDATE + "</DueDate>"
                    + "</MOrderDetail>";


            String MODID = SysBasic.toTranStringByObject(str.get("MODID"));
            String sql2 = "select * from BIO_BJ_PRODUCTION_ALLOCATEMX where MODID = ? ORDER BY SORTSEQ ASC";
            List<Map<String, Object>> result2 = queryJdbcTemplate.queryForList(sql2, MODID);

            for (int j = 0; j < result2.size(); j++) {
                Map map2 = (Map) result2.get(j);
                Object s3 = SysBasic.toTranDateByObject(map2.get("STARTDEMDATE"));
                //SysBasic.toTranStringByObject(map2.get("MODID"));
                String STARTDEMDATE = null;
                Object s4 = SysBasic.toTranDateByObject(map2.get("ENDDEMDATE"));
                String ENDDEMDATE = null;
                if(s3 != null){
                    STARTDEMDATE = dateFormat.format(s3);
                }
                if(s4 != null){
                    ENDDEMDATE = dateFormat.format(s4);
                }
                String str11 = "<Allocate>"
                        + "<AllocateId>" + SysBasic.toTranStringByObject(map2.get("ALLOCATEID")) + "</AllocateId>"
                        + "<MoDId>" + SysBasic.toTranStringByObject(map2.get("MODID")) + "</MoDId>"
                        + "<SortSeq>" + SysBasic.toTranStringByObject(map2.get("SORTSEQ")) + "</SortSeq>"
                        + "<BaseQtyN>" + SysBasic.toTranStringByObject(map2.get("BASEQTYN")) + "</BaseQtyN>"
                        + "<BaseQtyD>" + SysBasic.toTranStringByObject(map2.get("BASEQTYD")) + "</BaseQtyD>"
                        + "<Qty>" + SysBasic.toTranStringByObject(map2.get("QTY")) + "</Qty>"
                        + "<StartDemDate>" + STARTDEMDATE + "</StartDemDate>"
                        + "<EndDemDate>" + ENDDEMDATE + "</EndDemDate>"
                        + "<WhCode>" + SysBasic.toTranStringByObject(map2.get("WHCODE")) + "</WhCode>"
                        + "<WIPType>" + SysBasic.toTranStringByObject(map2.get("WIPTYPE")) + "</WIPType>"
                        + "<ByproductFlag>" + SysBasic.toTranStringByObject(map2.get("BYPRODUCTFLAG")) + "</ByproductFlag>"
                        + "<InvCode>" + SysBasic.toTranStringByObject(map2.get("INVCODE")) + "</InvCode>"
                        + "<Free1>" + SysBasic.toTranStringByObject(map2.get("FREE1")) + "</Free1>"
                        + "<Free2>" + SysBasic.toTranStringByObject(map2.get("FREE2")) + "</Free2>"
                        + "<Free3>" + SysBasic.toTranStringByObject(map2.get("FREE3")) + "</Free3>"
                        + "<Free4>" + SysBasic.toTranStringByObject(map2.get("FREE4")) + "</Free4>"
                        + "<Free5>" + SysBasic.toTranStringByObject(map2.get("FREE5")) + "</Free5>"
                        + "<Free6>" + SysBasic.toTranStringByObject(map2.get("FREE6")) + "</Free6>"
                        + "<Free7>" + SysBasic.toTranStringByObject(map2.get("FREE7")) + "</Free7>"
                        + "<Free8>" + SysBasic.toTranStringByObject(map2.get("FREE8")) + "</Free8>"
                        + "<Free9>" + SysBasic.toTranStringByObject(map2.get("FREE9")) + "</Free9>"
                        + "<Free10>" + SysBasic.toTranStringByObject(map2.get("FREE10")) + "</Free10>"
                        + "<AuxUnitCode>" + SysBasic.toTranStringByObject(map2.get("AUXUNITCODE")) + "</AuxUnitCode>"
                        + "<ChangeRate>" + SysBasic.toTranStringByObject(map2.get("CHANGERATE")) + "</ChangeRate>"
                        + "<AuxBaseQtyN>" + SysBasic.toTranStringByObject(map2.get("AUXBASEQTYN")) + "</AuxBaseQtyN>"
                        + "<AuxQty>" + SysBasic.toTranStringByObject(map2.get("AUXQTY")) + "</AuxQty>"
                    //    + "<cbdefine12>" + SysBasic.toTranStringByObject(str.get("CBDEFINE12")) + "</cbdefine12>"
                   //     + "<cbdefine12>" + SysBasic.toTranStringByObject(str.get("CBDEFINE12")) + "</cbdefine12>"
                        + "</Allocate>";

                str9 += str11;
            }
        }

        String all = str1 + str8 + str10 + str9 + str7;
        String url = urlhtml + "/U8EAI/import.asp";
        JSONObject date = new JSONObject();
        //date.put("url", "http://bj01ufi02.bmk.local/U8EAI/import.asp");
        date.put("url", url);
        date.put("outContentType", "xml");
        date.put("outCharset", "utf-8");
        date.put("inCharset", "utf-8");
        date.put("bodyParams", all);
     //  log.info(SysBasic.getNowTime()+"   北京U8生产订单："+ID+"\n 报文："+all);
//        return date;
        return httpCommonController.apiCommonPost(date);

    }
}
