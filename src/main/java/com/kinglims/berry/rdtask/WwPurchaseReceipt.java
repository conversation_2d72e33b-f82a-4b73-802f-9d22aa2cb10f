package com.kinglims.berry.rdtask;


import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.system.SysBasic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
@RestController
@RequestMapping("/system/jdbc")
public class WwPurchaseReceipt {
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    SimpleDateFormat dateFormat2 = new SimpleDateFormat("MM/dd/yyyy");
    SimpleDateFormat dateFormat3 = new SimpleDateFormat("yyyy-MM-dd");
    Calendar cal=Calendar.getInstance();
    Date now = new Date();
    String urlhtml = "";
    @Autowired
    private JdbcTemplate queryJdbcTemplate;
    @Autowired
    private HttpCommonController httpCommonController;







    @RequestMapping("/ww/purchasereceipt")
    public JSONObject DELIVERY(@RequestBody JSONObject data) throws Exception {
        urlhtml = (String) data.get("urlhtml");
        List ids = (List) data.get("Ids");
        JSONObject jsonObject = new JSONObject();
        List list = new ArrayList();
        String sj = (String) data.get("ts");
        String ITEM_TEXT = "委外采购入库单";
        String sql = "select * from SYS_DATA_DICT where ITEM_TEXT = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ITEM_TEXT);
        Map map = result.get(0);
        String days = (String) map.get("NDAYPUSH");
            //判断推送模式
            if (sj.equals("手动模式")) {
                for (Object id : ids) {
                    String Ids2 = id.toString();
                    jsonObject = Ts(Ids2);
                    //  log.info("----------jsonObject-------"+jsonObject);
                    list.add(jsonObject);
                }
            } else if (sj.equals((String) map.get("ITEM_CODE"))) {

                for (Object id : ids) {
                    String Ids2 = id.toString();
                    list.add(Ts(Ids2));
                }

            } else {
                return new CurrResponseResolve(-1).put("apiData", "自动推送无法执行手动推送功能").getData();
            }

        return new CurrResponseResolve(1).put("apiData", list).getData();
    }



    public JSONObject Ts(String ID) throws Exception {
        String sql = "select * from BIO_WW_PURCHASE_RECEIPT where ID = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ID);
        String BILL_ID = (String) result.get(0).get("BILL_ID");
        String sql2 = "select * from BIO_WW_PURCHASE_RECEIPT_MX where BILL_ID = ?";
        List<Map<String, Object>> result2 = queryJdbcTemplate.queryForList(sql2, BILL_ID);
        String str7 = "</body>" +
                "</storein>" +
                "</ufinterface>";
        String str8 = "";
        String str9 = "";
        Map str = (Map) result.get(0);
        Object s1 = SysBasic.toTranDateByObject(str.get("WAREHOUSING_DATE"));
        Object IMPUTATIONDATA= SysBasic.toTranDateByObject(str.get("IMPUTATIONDATA"));
        String WAREHOUSING_DATE = null;
        if(s1 != null){
            WAREHOUSING_DATE = dateFormat3.format(s1);
        }
        String str1 = "<ufinterface sender=\"020\" receiver=\"u8\" roottag=\"storein\" docid=\"377987086\" proc=\"add\" codeexchanged=\"N\" exportneedexch=\"N\" paginate=\"0\" display=\"入库单\" family=\"库存管理\" dynamicdate=\""
                +dateFormat2.format(IMPUTATIONDATA)+"\" maxdataitems=\"20000\" bignoreextenduserdefines=\"n\" timestamp=\"0x0000000003101A6E\" lastquerydate=\""+dateFormat.format(IMPUTATIONDATA)+"\"><storein>";

        str8 = "<header>"
                + "<id>" + str.get("BILL_ID") + "</id>"
                + "<receiveflag>" + SysBasic.toTranStringByObject(str.get("RECEIVEFLAG")) + "</receiveflag>"
                + "<vouchtype>" + SysBasic.toTranStringByObject(str.get("VOUCHTYPE")) + "</vouchtype>"
                + "<businesstype>" + SysBasic.toTranStringByObject(str.get("BUSINESSTYPE")) + "</businesstype>"
                + "<purchasetypecode>" + SysBasic.toTranStringByObject(str.get("PURCHASETYPECODE")) + "</purchasetypecode>"
                + "<source>" + SysBasic.toTranStringByObject(str.get("SOURCE")) + "</source>"
                + "<warehousecode>" + SysBasic.toTranStringByObject(str.get("WAREHOUSECODE")) + "</warehousecode>"
                + "<date>" + WAREHOUSING_DATE + "</date>"
                + "<code>" + SysBasic.toTranStringByObject(str.get("BILL_CODE")) + "</code>"
                + "<receivecode>" + SysBasic.toTranStringByObject(str.get("RECEIVECODE")) + "</receivecode>"
                + "<departmentcode>" + SysBasic.toTranStringByObject(str.get("DEPARTMENTCODE")) + "</departmentcode>"
                + "<vendorcode>" + SysBasic.toTranStringByObject(str.get("VENDORCODE")) + "</vendorcode>"
                + "<memory>" + SysBasic.toTranStringByObject(str.get("EX_DH_NO")) + "</memory>"
                + "<maker>" + SysBasic.toTranStringByObject(str.get("MAKER")) + "</maker>"
                + "<define10><![CDATA[" + SysBasic.toTranStringByObject(str.get("DEFINE10")) + "]]></define10>"
                + "<taxrate>" + SysBasic.toTranStringByObject(str.get("TAXRATE")) + "</taxrate>"
                + "<exchname>" + SysBasic.toTranStringByObject(str.get("EXCHNAME")) + "</exchname>"
                + "<exchrate>" + SysBasic.toTranStringByObject(str.get("EXCHRATE")) + "</exchrate>"
                + "</header>"
                + "<body>";

        for (int j = 0; j < result2.size(); j++) {
            Map map2 = (Map) result2.get(j);
            String str11 = "<entry>"
                    + "<id>" + SysBasic.toTranStringByObject(map2.get("BILL_ID")) + "</id>"
                    + "<autoid>" + SysBasic.toTranStringByObject(map2.get("AUTOID")) + "</autoid>"
                    + "<inventorycode>" + SysBasic.toTranStringByObject(map2.get("INVENTORYCODE")) + "</inventorycode>"
                    + "<invname><![CDATA[" + SysBasic.toTranStringByObject(map2.get("INVNAME")) + "]]></invname>"
                    + "<quantity>" + SysBasic.toTranStringByObject(map2.get("QUANTITY")) + "</quantity>"
                    + "<cmassunitname><![CDATA[" + SysBasic.toTranStringByObject(map2.get("CMASSUNITNAMECMASSUNITNAME")) + "]]></cmassunitname>"
                    + "<price>" + SysBasic.toTranStringByObject(map2.get("PRICE")) + "</price>"
                    + "<cost>" + SysBasic.toTranStringByObject(map2.get("COST")) + "</cost>"
                    + "<itemclasscode>" + SysBasic.toTranStringByObject(map2.get("ITEMCLASSCODE")) + "</itemclasscode>"
                    + "<itemclassname><![CDATA[" + SysBasic.toTranStringByObject(map2.get("ITEMCLASSNAME")) + "]]></itemclassname>"
                    + "<itemcode>" + SysBasic.toTranStringByObject(map2.get("ITEMCODE")) + "</itemcode>"
                    + "<itemname><![CDATA[" + SysBasic.toTranStringByObject(map2.get("ITEMNAME")) + "]]></itemname>"
                    + "<define23>" + SysBasic.toTranStringByObject(map2.get("DEFINE23")) + "</define23>"
                    + "<define28>" + SysBasic.toTranStringByObject(map2.get("DEFINE28")) + "</define28>"
                    + "<define31>" + SysBasic.toTranStringByObject(map2.get("DEFINE31")) + "</define31>"
                    + "<define32>" + SysBasic.toTranStringByObject(map2.get("DEFINE32")) + "</define32>"
                    + "<define33>" + SysBasic.toTranStringByObject(map2.get("DEFINE33")) + "</define33>"
                    + "<iorimoney>" + SysBasic.toTranStringByObject(map2.get("IORIMONEY")) + "</iorimoney>"
                    + "<ioritaxprice>" + SysBasic.toTranStringByObject(map2.get("IORITAXPRICE")) + "</ioritaxprice>"
                    + "<iorisum>" + SysBasic.toTranStringByObject(map2.get("IORISUM")) + "</iorisum>"
                    + "<taxrate>" + SysBasic.toTranStringByObject(map2.get("TAXRATE")) + "</taxrate>"
                    + "<memory>" + SysBasic.toTranStringByObject(map2.get("MEMORY")) + "</memory>"
                    + "<cbdefine1>" + SysBasic.toTranStringByObject(map2.get("CBDEFINE1")) + "</cbdefine1>"
                    + "<cbdefine2><![CDATA[" + SysBasic.toTranStringByObject(map2.get("CBDEFINE2")) + "]]></cbdefine2>"
                    + "<cbdefine3><![CDATA[" + SysBasic.toTranStringByObject(map2.get("CBDEFINE3")) + "]]></cbdefine3>"
                    + "<cbdefine4><![CDATA[" + SysBasic.toTranStringByObject(map2.get("CBDEFINE4")) + "]]></cbdefine4>"
                    + "</entry>";
            str9 += str11;
        }
        String all = str1 + str8+ str9 + str7;
        JSONObject date = new JSONObject();
        String url = urlhtml + "/U8EAI/import.asp";
        date.put("url", url);
        date.put("outContentType", "xml");
        date.put("outCharset", "utf-8");
        date.put("inCharset", "utf-8");
        date.put("bodyParams", all);
   //     log.info(all);
        return httpCommonController.apiCommonPost(date);
    }
}
