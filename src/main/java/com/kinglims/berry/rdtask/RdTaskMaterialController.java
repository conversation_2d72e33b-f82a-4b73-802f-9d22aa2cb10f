package com.kinglims.berry.rdtask;

import cn.hutool.json.serialize.JSONSerializer;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.common.system.jdbc.handler.query.QueryTables;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
@RestController
@RequestMapping("/system/jdbc")
public class RdTaskMaterialController {
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
    SimpleDateFormat dateFormat2 = new SimpleDateFormat("dd/MM/yyyy");
    Date now = new Date();
    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private HttpCommonController httpCommonController;
    @RequestMapping("/rd/material")
    public JSONObject DELIVERY(@RequestBody JSONObject data) throws Exception {
        List ids = (List) data.get("Ids");
        JSONObject jsonObject = new JSONObject();
        List list = new ArrayList();
        for (Object id : ids) {
            String Ids2 =  id.toString();
            list.add(Ts(Ids2));
            //System.out.println("---------LIST-------"+list);
        }
        for (Object o : list) {
            JSONObject object = (JSONObject) o;
            int num = object.getInteger("code");
            if (num > 0){
            }
            else{
                return new CurrResponseResolve(-1).put("apiData", list).getData();
            }
        }
         return new CurrResponseResolve(1).put("apiData", list).getData();
    }

    public JSONObject Ts(String Id) throws Exception {
        String LIMS_TO_U8ID = Id;
        //System.out.println("000000" + UNS_TO_U8ID);
        String sql = "select * from BIO_RD_MATERIAL_DELIVERY where LIMS_TO_U8ID = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, LIMS_TO_U8ID);
        String sql2 = "select * from BIO_RD_MATERIAL_DELIVERY_MX where LIMS_TO_U8ID = ?";
        List<Map<String, Object>> result2 = queryJdbcTemplate.queryForList(sql2, LIMS_TO_U8ID);
        String str1 = "<ufinterface sender=\"997\" receiver=\"u8\" roottag=\"storeout\" docid=\"238226592\" proc=\"add\" codeexchanged=\"N\" exportneedexch=\"N\" paginate=\"0\" display=\"出库单\" family=\"库存管理\" dynamicdate=\"";
        String str2 = "\" maxdataitems=\"20000\" bignoreextenduserdefines=\"n\" timestamp=\"0x00000000030A2375\" lastquerydate=\"";
        String str3 = "\"><storeout>";
        String str4 = dateFormat.format(now);
        String str5 = dateFormat2.format(now);
        String str6 = "\n<body>";
        String str7 = "</body>" +
                "</storeout>" +
                "</ufinterface>";
        String str8 = "";
        String str9 = "";
            Map str = (Map) result.get(0);
           // System.out.println(str);
            str8 = "<header>"
                    + "<id>" + str.get("UNS_TO_U8ID") + "</id>"
                    + "<vouchtype>" + str.get("VOUCHTYPE") + "</vouchtype>"
                    + "<businesstype>" + str.get("BUSINESSTYPE") + "</businesstype>"
                    + "<warehousecode>" + str.get("WAREHOUSECODE") + "</warehousecode>"
                    + "<date>" + str.get("DELIVERYDATE") + "</date>"
                    + "<code>" + str.get("CODE") + "</code>"
                    + "<receivecode>" + str.get("RECEIVECODE") + "</receivecode>"
                    + "<departmentcode>" + str.get("DEPARTMENTCODE") + "</departmentcode>"
                    + "<memory>" + str.get("MEMORY") + "</memory>"
                    + "<maker>" + str.get("MAKER") + "</maker>"
                    + "<define4>" + str.get("DEFINE4") + "</define4>"
                    + "<define6>" + str.get("DEFINE6") + "</define6>"
                    + "<define10>" + str.get("DEFINE10") + "</define10>"
                    + "<define12>" + str.get("DEFINE12") + "</define12>"
                    + "</header>";

            for (int j = 0; j < result2.size(); j++) {
                Map map2 = (Map) result2.get(j);
                //System.out.println(str.get("UNS_TO_U8ID"));
                //System.out.println(map2.get("LIMS_TO_U8ID"));
                //System.out.println(str.get("UNS_TO_U8ID").equals(map2.get("LIMS_TO_U8ID")));

                //if (str.get("UNS_TO_U8ID").equals(map2.get("LIMS_TO_U8ID"))) {
                   // System.out.println("-----------------------子表------------------------");
                  //  System.out.println(map2);
                    String str11 = "<entry>"
                            + "<id>" + map2.get("LIMS_TO_U8ID") + "</id><barcode/>"
                            + "<inventorycode>" + map2.get("INVENTORYCODE") + "</inventorycode>"
                            + "<quantity>" + map2.get("QUANTITY") + "</quantity>"
                            + "<cmassunitname>" + map2.get("CMASSUNITNAME") + "</cmassunitname>"
                            + "<assitantunitname>" + map2.get("ASSITANTUNITNAME") + "</assitantunitname>"
                            + "<irate>" + map2.get("IRATE") + "</irate>"
                            + "<number>" + map2.get("NUMBERS") + "</number>"
                            + "<price>" + map2.get("PRICE") + "</price>"
                            + "<cost>" + map2.get("COST") + "</cost>"
                            + "<serial>" + map2.get("SERIAL") + "</serial>"
                            + "<validdate>" + map2.get("VALIDDATE") + "</validdate>"
                            + "<itemclasscode>" + map2.get("ITEMCLASSCODE") + "</itemclasscode>"
                            + "<itemclassname>" + map2.get("ITEMCLASSNAME") + "</itemclassname>"
                            + "<itemcode>" + map2.get("ITEMCODE") + "</itemcode>"
                            + "<itemname>" + map2.get("ITEMNAME") + "</itemname>"
                            + "<define22>" + map2.get("DEFINE22") + "</define22>"
                            + "<define24>" + map2.get("DEFINE24") + "</define24>"
                            + "<define33>" + map2.get("DEFINE33") + "</define33>"
                            + "<memory>" + map2.get("MEMORY") + "</memory>"
                            + "</entry>";
                    str9 += str11;
               // } else {
               //     System.out.println("--------------------------23213213---------------------");
               //     break;
               // }
            }
        String all = str1 + str5 + str2 + str4 + str3 + str8 + str6 + str9 + str7;
        //System.out.println(all);
        List list = new ArrayList();
        list.add(all);
        //System.out.println(list);
        //Map date = new HashMap();
        JSONObject date = new JSONObject();
        date.put("url", "http://bj01ufi02.bmk.local/U8EAI/import.asp");
        date.put("outContentType", "xml");
        date.put("outCharset", "utf-8");
        date.put("inCharset", "utf-8");
        date.put("bodyParams", list);
        JSONObject sss = httpCommonController.apiCommonPost(date);
        //System.out.println("------1234564-----------"+sss);
        return sss;
    }
}