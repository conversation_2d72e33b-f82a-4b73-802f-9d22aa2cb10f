package com.kinglims.berry.rdtask;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.system.SysBasic;
import com.kinglims.u8.baimaike.org.entity.Outsourcing;
import com.kinglims.u8.baimaike.org.entity.OutsourcingDetail;
import com.kinglims.u8.baimaike.org.entity.OutsourcingMater;
import com.kinglims.u8.baimaike.org.service.GenerateMaterialOrder;
import com.kinglims.u8.baimaike.org.service.GenerateMaterialOrderProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Null;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
@RestController
@RequestMapping("/system/jdbc")
public class OutSourcings {
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
   // SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Calendar cal = Calendar.getInstance();
    @Autowired
    private JdbcTemplate queryJdbcTemplate;
    @Autowired
    private HttpCommonController httpCommonController;

    @RequestMapping("/oss/push")
    public JSONObject DELIVERY(@RequestBody JSONObject data) throws Exception {
        List ids = (List) data.get("Ids");
        JSONObject jsonObject = new JSONObject();
        List<String> list = new ArrayList();
        String sj = (String) data.get("ts");
        String ITEM_TEXT = "材料出库单-生产";
        String sql = "select * from SYS_DATA_DICT where ITEM_TEXT = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ITEM_TEXT);
        Map map = result.get(0);
        //判断推送模式
        if ("手动模式".equals(sj)) {
            for (Object id : ids) {
                String Ids2 = id.toString();
                //jsonObject = Ts(Ids2);
                try {
                    list.add(Ts(Ids2));
                }catch (Exception e){
                    System.out.println(e);
                    return new CurrResponseResolve(-1).put("apiData", "请联系管理员解决").getData();
                }
                //System.out.println("---------LIST-------"+list);
            }
        } else if (sj.equals((String) map.get("ITEM_CODE"))) {
                for (Object id : ids) {
                    String Ids2 = id.toString();
                    list.add(Ts(Ids2));
                }
        } else {
            return new CurrResponseResolve(-1).put("apiData", "自动推送无法执行手动推送功能").getData();
        }
        return new CurrResponseResolve(1).put("apiData", list).getData();
    }


    public String Ts(String ID) throws Exception {

        String sql = "select * from BIO_OUT_SOURCING where ID = ?";
        List<Map<String, Object>> result = queryJdbcTemplate.queryForList(sql, ID);
        String SOURCING_ID = (String) result.get(0).get("ID");
        String sql2 = "select * from BIO_OUT_SOURCING_DETAILMX where SOURCING_ID = ? ORDER BY LINENUMBER ASC";
        List<Map<String, Object>> result2 = queryJdbcTemplate.queryForList(sql2, SOURCING_ID);

        OutsourcingDetail[] outsDs = new OutsourcingDetail[result2.size()];//委外订单子表明细	outdetail	OutsourcingDetail[]

        for (int i = 0; i < result2.size(); i++) {
            String Detail_ID = (String) result2.get(i).get("ID");
            String sql3 = "select * from BIO_OUT_SOURCING_MATERMX where Detail_ID = ?  ORDER BY RID ASC";
            List<Map<String, Object>> result3 = queryJdbcTemplate.queryForList(sql3, Detail_ID);

            OutsourcingDetail outsourcingDetail = new OutsourcingDetail();//委外订单子表明细	outdetail	OutsourcingDetail
            Map str1 = (Map) result2.get(i);
            outsourcingDetail.setProductCode(SysBasic.toTranStringByObject(str1.get("PRODUCTCODE")));//产品编码	productCode
            //outsourcingDetail.setLineNumber((String) str1.get("LINENUMBER"));//行号	lineNumber
            outsourcingDetail.setLineNumber(SysBasic.toTranStringByObject(str1.get("LINENUMBER")));//行号	lineNumber
            outsourcingDetail.setVdef2(SysBasic.toTranStringByObject(str1.get("VDEF2")));//产品名称	vdef2	string
            outsourcingDetail.setMaterDetail(new OutsourcingMater[result3.size()]);//物料明细	materDetail	OutsourcingMater[]
            int vudef9 = 0;
            for (int j = 0; j < result3.size(); j++) {
                vudef9 = vudef9 + 1;
                Map str2 = (Map) result3.get(j);
                Object s4=SysBasic.toTranDateByObject(str2.get("VUDEF4"));
                String VUDEF4 = null;
                if(s4 != null){
                    VUDEF4 = dateFormat.format(s4);
                }
                OutsourcingMater outsourcingMater = new OutsourcingMater();//物料明细	materDetail	OutsourcingMater
                outsourcingMater.setMaterialCode(SysBasic.toTranStringByObject(str2.get("MATERIALCODE")));//材料编码	materialCode	string
                outsourcingMater.setProject(SysBasic.toTranStringByObject(str2.get("PROJECT")));//项目	project	string
                outsourcingMater.setNum(SysBasic.toTranStringByObject(str2.get("SNUM")));//数量	num	string
//	        	outsourcingMater.setPrice((String) str2.get("PRICE"));//单价	price	string - 不用传
//	        	outsourcingMater.setVudef1((String) str2.get("VUDEF1"));//件数	vudef1	string - 不用传
//	        	outsourcingMater.setVudef2((String) str2.get("VUDEF2"));//换算率	vudef2	string - 不用传
                outsourcingMater.setVudef1((String) str2.get("VUDEF1"));//项目编码	vudef1	string - 不用传
                outsourcingMater.setVudef2((String) str2.get("VUDEF2"));//项目名称	vudef2	string - 不用传
                outsourcingMater.setVudef3(SysBasic.toTranStringByObject(str2.get("VUDEF3")));//批号 	vudef3	string
                outsourcingMater.setVudef4(VUDEF4);//失效日期	vudef4	string
                outsourcingMater.setVudef5(SysBasic.toTranStringByObject(str2.get("VUDEF5")));//工序	vudef5	string
                outsourcingMater.setVudef6(SysBasic.toTranStringByObject(str2.get("VUDEF6")));//材料名称	vudef6	string
                outsourcingMater.setVudef7(SysBasic.toTranStringByObject(str2.get("VUDEF7")));//阶段	vudef7	string
                outsourcingMater.setVudef8(SysBasic.toTranStringByObject(str2.get("VUDEF8")));//期号	vudef8	string
                outsourcingMater.setVudef9(String.valueOf(vudef9));//期号	vudef8	string
                outsourcingMater.setVudef10(SysBasic.toTranStringByObject(str2.get("CBDEFINE12")));//任务单编号	vudef10	string
                outsourcingDetail.getMaterDetail()[j] = outsourcingMater;
            }
            outsDs[i] = outsourcingDetail;
        }
        Map str = (Map) result.get(0);
        Outsourcing outs = new Outsourcing();
        Object s4=SysBasic.toTranDateByObject(str.get("DEFINE7"));
        Object s5=SysBasic.toTranDateByObject(str.get("DEFINE8"));
        Object s6=SysBasic.toTranDateByObject(str.get("DEFINE2"));
        String DEFINE7 = null;
        String DEFINE8 = null;
        String DEFINE2 = null;
        if(s4 != null){
            DEFINE7 = dateFormat.format(s4);
        }
        if(s5 != null){
            DEFINE8 = dateFormat.format(s5);
        }
        if(s6 != null){
            DEFINE2 = dateFormat2.format(s6);
        }

//		outs.setTrade_no("2107110070");//委外订单号	trade_no
        outs.setTrade_no(SysBasic.toTranStringByObject(str.get("TRADE_NO")));//委外订单号	trade_no
        outs.setDefine4(SysBasic.toTranStringByObject(str.get("DEFINE4")));//备注	Define4	string
        outs.setDefine5(SysBasic.toTranStringByObject(str.get("DEFINE5")));//样品数	Define5	string
        outs.setDefine6(SysBasic.toTranStringByObject(str.get("DEFINE6")));//建库数	Define6	string
        outs.setDefine7(DEFINE7);//实验开始日期	Define7	string
        outs.setDefine8(DEFINE8);//实验结束日期	Define8	string
        outs.setDefine9(SysBasic.toTranStringByObject(str.get("DEFINE9")));//实验员	Defin9	string
        outs.setDefine1(SysBasic.toTranStringByObject(str.get("DEFINE1")));// 华开单号 Define1
        outs.setDefine2(DEFINE2);//出库单号
        outs.setOutdetail(outsDs);//委外订单子表明细	outdetail	OutsourcingDetail[]

        GenerateMaterialOrder g = new GenerateMaterialOrderProxy();
        JSONObject jsonObject = new JSONObject();
        String sss="";
      //  log.info("++++++++++++++++"+jsonObject.toJSONString(outs));
        try {

              sss = g.generateMaterialPush(outs);
        }catch (Exception e){
            log.error("123",e);
        }

        return sss;
    }
}


















