package com.kinglims.berry.exception.prod;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.system.workflow.handler.logic.Modulars;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowMessage;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/exception/prod")
@Slf4j
public class BrProdExceptionController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;

    @Autowired
    private WorkflowUtils workflowUtils;

    @Autowired
    private ResponseMessageParameter responseMessageParameter;

    // 发送销售
    @RequestMapping("/sendSale")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject sendSale(@RequestBody JSONObject data) {

    	try {
    		List<String> ids = (List<String>) data.get("ids");

    		String updateSQL = "UPDATE BR_MODUAL_ABNOPRODU SET SEND_SALE_FLAG='Y' WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";

	    	updateJdbcTemplate.update(updateSQL, ids.toArray());

	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();

		} catch (Exception e) {

			log.info("/berry/exception/prod/sendSale", e);
			log.error("/berry/exception/prod/sendSale", e);

			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 取消发送销售
    @RequestMapping("/unSendSale")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject unSendSale(@RequestBody JSONObject data) {

    	try {
    		List<String> ids = (List<String>) data.get("ids");

    		String updateSQL = "UPDATE BR_MODUAL_ABNOPRODU SET SEND_SALE_FLAG='N' WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";

	    	updateJdbcTemplate.update(updateSQL, ids.toArray());

	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();

		} catch (Exception e) {

			log.info("/berry/exception/prod/unSendSale", e);
			log.error("/berry/exception/prod/unSendSale", e);

			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 发送CSS
    @RequestMapping("/sendCSS")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject sendCSS(@RequestBody JSONObject data) {

    	try {
    		List<String> ids = (List<String>) data.get("ids");

    		String updateSQL = "UPDATE BR_MODUAL_ABNOPRODU SET SEND_CSS_FLAG='Y' WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";

	    	updateJdbcTemplate.update(updateSQL, ids.toArray());

	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();

		} catch (Exception e) {

			log.info("/berry/exception/prod/sendCSS", e);
			log.error("/berry/exception/prod/sendCSS", e);

			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 取消发送CSS
    @RequestMapping("/unSendCSS")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject unSendCSS(@RequestBody JSONObject data) {

    	try {
    		List<String> ids = (List<String>) data.get("ids");

    		String updateSQL = "UPDATE BR_MODUAL_ABNOPRODU SET SEND_CSS_FLAG='N' WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";

	    	updateJdbcTemplate.update(updateSQL, ids.toArray());

	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();

		} catch (Exception e) {

			log.info("/berry/exception/prod/unSendCSS", e);
			log.error("/berry/exception/prod/unSendCSS", e);

			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 删除
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {

    	try {
    		List<String> ids = (List<String>) data.get("ids");

    		String deleteSQL = "DELETE FROM BR_MODUAL_ABNOPRODU WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND DISPOSE_FLAG='待处理'";

	    	updateJdbcTemplate.update(deleteSQL, ids.toArray());

	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();

		} catch (Exception e) {

			log.info("/berry/exception/prod/del", e);
			log.error("/berry/exception/prod/del", e);

			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 提交流程
    @RequestMapping("/submitFlow")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitFlow(@RequestBody JSONObject data) {

    	try {
    		int code=1;
    		List<String> ids = (List<String>) data.get("ids");

    		String seleteSQL = "SELECT ID FROM BR_MODUAL_ABNOPRODU WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND DISPOSE_FLAG='待处理'";

    		List<String> idList = queryJdbcTemplate.queryForList(seleteSQL, String.class, ids.toArray());

	    	if (idList.size() > 0) {
	    		WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submit(Modulars.ABNOPRODU,idList);
	    		if (wflowMsg.isB()) {
	    			String updateSQL = "UPDATE BR_MODUAL_ABNOPRODU SET DISPOSE_FLAG='已处理' WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(idList.size())+")";
	    			updateJdbcTemplate.update(updateSQL, idList.toArray());
	    		}else{
	                code = -1;
	                SysBasic.rollBack();
	            }
	    	}

	    	return new CurrResponseResolve(code).put(responseMessageParameter).getData();

		} catch (Exception e) {

			log.info("/berry/exception/prod/submitFlow", e);
			log.error("/berry/exception/prod/submitFlow", e);

			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }

    // 撤销流程
    @RequestMapping("/revokeFlow")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeFlow(@RequestBody JSONObject data) {

    	try {
    		int code=1;
    		List<String> ids = (List<String>) data.get("ids");

    		String seleteSQL = "SELECT ID FROM BR_MODUAL_ABNOPRODU WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND DISPOSE_FLAG='已处理'";

    		List<String> idList = queryJdbcTemplate.queryForList(seleteSQL, String.class, ids.toArray());

	    	if (idList.size() > 0) {
	    		WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.ABNOPRODU,idList);
	    		if (wflowMsg.isB()) {
	    			String updateSQL = "UPDATE BR_MODUAL_ABNOPRODU SET DISPOSE_FLAG='待处理' WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(idList.size())+")";
	    			updateJdbcTemplate.update(updateSQL, idList.toArray());
	    		}else{
	                code = -1;
	                SysBasic.rollBack();
	            }
	    	}

	    	return new CurrResponseResolve(code).put(responseMessageParameter).getData();

		} catch (Exception e) {

			log.info("/berry/exception/prod/revokeFlow", e);
			log.error("/berry/exception/prod/revokeFlow", e);

			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
