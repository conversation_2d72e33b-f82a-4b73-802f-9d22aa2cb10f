package com.kinglims.berry.prod.tq;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.security.entity.User;
import com.kinglims.framework.common.system.workflow.handler.logic.Modulars;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowUtils;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/tq")
@Slf4j
public class BrTqTaskController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    
    @Autowired
    private WorkflowUtils workflowUtils;

    // 提取任务单: 完成
    @RequestMapping("/tqTask/tqFinish")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject tqFinish(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		// 确认提取
    		String updateSQL_TQ_finish = "UPDATE BR_TQ_INFO SET STATUS='已完成'"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND STATUS='进行中'";
    		
    		updateJdbcTemplate.update(updateSQL_TQ_finish, ids.toArray());
    		
    		return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/tq/tqTask/tqFinish", e);
			log.error("/berry/prod/tq/tqTask/tqFinish", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
//    // 提取任务单: 撤回项目  -- 改为审核不通过退回
//    @RequestMapping("/tqTask/returnProject")
//    @Transactional(rollbackFor = Exception.class)
//    public JSONObject tqReturnProject(@RequestBody JSONObject data) {
//    	
//    	try {
//    		List<String> ids = (List<String>) data.get("ids");
//    		String updateSQL_returnProject = "UPDATE BR_TQ_INFO SET STATUS='退回' WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") and STATUS='待审核'";
//    		
//    		updateJdbcTemplate.update(updateSQL_returnProject, ids.toArray());
//    		
//	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
//	    	
//		} catch (Exception e) {
//			
//			log.info("/berry/prod/tq/tqTask/returnProject", e);
//			log.error("/berry/prod/tq/tqTask/returnProject", e);
//			
//			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
//		}
//    }
    // 提取任务单: 任务明细样本: 分派提取任务
    @RequestMapping("/tqTask/assign")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject assign(@RequestBody JSONObject data, @RequestHeader(name="Authorization")String token) {
    	
    	try {
    		Date nowDateTime = new Date();
    		User user = SysBasic.getUserByTokenRedis(token);
    		List<String> TQ_MX_IDS = (List<String>) data.get("TQ_MX_IDS");
    		String TQ_MAN_ID = data.getString("TQ_MAN_ID");//提取人ID
    		String TQ_MAN = data.getString("TQ_MAN");//提取人姓名
    		String TQ_MX_TYPE = data.getString("TQ_MX_TYPE");//提取后产物
    		int TQ_TUBE_NUM = data.getInteger("TQ_TUBE_NUM");//提取管数
    		int QC_TUBE_NUM = data.getInteger("QC_TUBE_NUM");//送检管数
    		String SOP_VALUE_TQ = data.getString("SOP_VALUE_TQ");//SOP值
    		String SOP_NAME_TQ = data.getString("SOP_NAME_TQ");//SOP名称
    		Date START_DATE = data.getDate("START_DATE");//任务开始时间
    		Date CLOSING_DATE = data.getDate("CLOSING_DATE");//任务结束时间
    		
    		String TQ_ASSIGNOR_ID = user.getID();//指派人ID
    		String TQ_ASSIGNOR = user.getNAME();//指派人姓名
    		Date TQ_ASSIGNOR_TIME = nowDateTime;//指派时间
    		
    		String CREATOR = user.getID();//创建人
    		Date CREATTIME = nowDateTime;//创建时间
    		String LASTUPDATOR = user.getID();//修改人
    		Date LASTUPDATETIME = nowDateTime;//修改时间
    		String LOGINCOMPANY = "";//帐套
    		
    		String[] TQ_MX_TYPE_Array = TQ_MX_TYPE.split("&");
    		
    		// 任务分派SQL
    		String insertSQL_tqTaskAssign = "INSERT INTO BR_TQ_MX_RESULT"
    		+ "(ID"//唯一标识
    		+ ",TQ_ID"//提取任务单ID
    		+ ",TQ_MX_ID"//提取样本明细ID
    		+ ",TQ_MX_LINK_ID"//提取样本明细关联模块表
    		+ ",TQ_MX_TYPE"//提取后核酸类型
    		+ ",TQ_ASSIGNOR_ID"//指派人ID
    		+ ",TQ_ASSIGNOR"//指派人姓名
    		+ ",TQ_ASSIGNOR_TIME"//指派时间
    		+ ",TQ_MAN_ID"//提取人ID
    		+ ",TQ_MAN"//提取人姓名
    		+ ",SOP_VALUE_TQ"//SOP值
    		+ ",SOP_NAME_TQ"//SOP名称
    		+ ",START_DATE"//任务开始时间
    		+ ",CLOSING_DATE"//任务结束时间
    		+ ",CREATOR"//创建人
    		+ ",CREATTIME"//创建时间
    		+ ",LASTUPDATOR"//修改人
    		+ ",LASTUPDATETIME"//修改时间
    		+ ",LOGINCOMPANY"//帐套
    		+ ",TQ_TUBE_NUM"//提取管数
//    		+ ",STATUS"//状态 -- 数据库默认值
    		+ ") SELECT ?,TQ_ID,ID,LINK_ID,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,? FROM BR_TQ_MX_INFO WHERE ID=?";
    		
    		// 任务分派参数
    		Object[] params = { "SysBasic.getUUID()" //唯一标识
    					, "TQ_MX_TYPE_Array[i]" //提取后核酸类型
	    				,TQ_ASSIGNOR_ID //指派人ID
	    				,TQ_ASSIGNOR //指派人姓名
	    				,TQ_ASSIGNOR_TIME //指派时间
	    				,TQ_MAN_ID //提取人ID
	    				,TQ_MAN //提取人姓名
	    				,SOP_VALUE_TQ //SOP值
	    				,SOP_NAME_TQ //SOP名称
	    				,START_DATE //任务开始时间
	    				,CLOSING_DATE //任务结束时间
	    				,CREATOR //创建人
	    				,CREATTIME //创建时间
	    				,LASTUPDATOR //修改人
	    				,LASTUPDATETIME //修改时间
	    				,LOGINCOMPANY //帐套
	    				,TQ_TUBE_NUM //提取管数
	    				,"BR_TQ_MX_INFO ID" // WHERE 提取任务单明细: 提取样本明细ID
    				};
    		
    		// 执行分派逻辑
    		for (int i = 0; i < QC_TUBE_NUM; i++) {// 管数
    			
    			for (int j = 0; j < TQ_MX_TYPE_Array.length; j++) {// 提取后产物
    				
    				String tq_mx_type = TQ_MX_TYPE_Array[j];//提取后产物
    				
    				for (int z = 0; z < TQ_MX_IDS.size(); z++) {
    					String TQ_MX_ID = TQ_MX_IDS.get(z);
    					
    					params[0] = SysBasic.getUUID();
    					params[1] = tq_mx_type;
    					params[ params.length-1 ] = TQ_MX_ID;
    					
    					updateJdbcTemplate.update(insertSQL_tqTaskAssign, params);
    	    		}
    				// --------------------------------
        		}
    			// --------------------------------
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/tq/tqTask/assign", e);
			log.error("/berry/prod/tq/tqTask/assign", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提取任务单: 任务明细样本: 我的提取任务: 保存提取结果
    @RequestMapping("/tqTaskMy/saveTqTaskResult")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject saveTqTaskResult(@RequestBody JSONObject data) {
    	
    	try {
    		String IDS = data.getString("IDS");
    		String[] idArray = IDS.split(",");//我的提取任务ID(提取结果表ID)
    		String DNA_RNA_CODE = data.getString("DNA_RNA_CODE");//核酸编号
    		Date TQ_CLOSING_TIME = data.getDate("TQ_CLOSING_TIME");//提取完成时间
    		double TQ_CONCENTRATION = data.getDouble("TQ_CONCENTRATION");//浓度(ng/ul)
    		double TQ_VOLUME = data.getDouble("TQ_VOLUME");//体积(ul)
    		double TQ_TOTAL = data.getDouble("TQ_TOTAL");//总量(ng)
    		String TQ_RESULT = data.getString("TQ_RESULT");//提取结果
    		String TQ_REMARK = data.getString("TQ_REMARK");//提取结果备注
    		
    		String updateSQL_tqTaskResult = "UPDATE BR_TQ_MX_RESULT"
    				+ " SET TQ_CLOSING_TIME=?"
    				+ ",TQ_CONCENTRATION=?"
    				+ ",TQ_VOLUME=?"
    				+ ",TQ_TOTAL=?"
    				+ ",TQ_RESULT=?"
    				+ ",TQ_REMARK=?"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(idArray.length)+") AND STATUS='待提取'";
    		
    		int updateFieldCount = 6;//更新字段数
    		Object[] params = new Object[ updateFieldCount + idArray.length ];
    		params[0] = TQ_CLOSING_TIME;//提取完成时间
    		params[1] = TQ_CONCENTRATION;//浓度(ng/ul)
    		params[2] = TQ_VOLUME;//体积(ul)
    		params[3] = TQ_TOTAL;//总量(ng)
    		params[4] = TQ_RESULT;//提取结果
    		params[5] = TQ_REMARK;//提取结果备注
    		for (int i = 0; i < idArray.length; i++) {//我的提取任务ID(提取结果表ID)
    			params[ updateFieldCount + i ] = idArray[i];
    		}
    		
    		updateJdbcTemplate.update(updateSQL_tqTaskResult, params);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/tqTaskMy/saveTqTaskResult", e);
			log.error("/berry/prod/tqTaskMy/saveTqTaskResult", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提取任务单: 任务明细样本: 我的提取任务: 保存提取结果
    @RequestMapping("/tqTaskMy/setDNA_RNA_Code")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject setDNA_RNA_Code(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL_tqinfo = "SELECT ID,TQ_MX_TYPE FROM BR_TQ_MX_RESULT WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND DNA_RNA_CODE IS NULL";
    		List<Map<String, Object>> tqinfoList = queryJdbcTemplate.queryForList(selectSQL_tqinfo, ids.toArray());
    		
    		String updateSQL_DNA_RNA_CODE = "UPDATE BR_TQ_MX_RESULT SET DNA_RNA_CODE=? WHERE ID=? AND DNA_RNA_CODE IS NULL";
    		
    		for (Map<String, Object> tqinfo : tqinfoList) {
    			String ID = tqinfo.get("ID")+"";
    			String TQ_MX_TYPE = tqinfo.get("TQ_MX_TYPE")+"";
    			// 核酸编号: 生成
    			String DNA_RNA_CODE = generateDNA_RNA_CODE(TQ_MX_TYPE);
    			// 更新核酸编号
    			if (DNA_RNA_CODE!=null && DNA_RNA_CODE.length()>0) {
    				updateJdbcTemplate.update(updateSQL_DNA_RNA_CODE, DNA_RNA_CODE, ID);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/tqTaskMy/setDNA_RNA_Code", e);
			log.error("/berry/prod/tqTaskMy/setDNA_RNA_Code", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 核酸编号生成: DNA_CODE = (2位年号)+(T)+(_D_)+(5位流水号) ; RNA_CODE = (2位年号)+(T)+(_D_)+(5位流水号)
    private String generateDNA_RNA_CODE(String TQ_MX_TYPE) {
    	// 年号
		int yyyy = new Date().getYear() + 1900 - 2000;
		// 核酸编号
		String DNA_RNA_CODE = null;
    	// 序列名称前缀
    	String SEQUENCE_NAME_PREFIX = "SEQ_TQ_DNA_RNA_CODE";
    	// 序列名称
    	String SEQUENCE_NAME = null;
		
		// DNA 核酸提取物
		if ( "DNA".equals(TQ_MX_TYPE) ) {
			DNA_RNA_CODE = yyyy + "T-D-";
			SEQUENCE_NAME_PREFIX += "_D_";
		}
		// RNA 核酸提取物
		else if ( "RNA".equals(TQ_MX_TYPE) ) {
			DNA_RNA_CODE = yyyy + "T-R-";
			SEQUENCE_NAME_PREFIX += "_R_";
		}
		
		if (DNA_RNA_CODE!=null) {
			SEQUENCE_NAME = SEQUENCE_NAME_PREFIX + yyyy;
			// 查询不是当前年号的样本编号序列, 并删除
			String seqSelectSQL = "SELECT SEQUENCE_NAME FROM USER_SEQUENCES"
					+ " WHERE SEQUENCE_NAME LIKE '"+SEQUENCE_NAME_PREFIX+"%' "
					+ " AND SEQUENCE_NAME NOT LIKE '%_"+yyyy+"' ";
			List<String> seqList = updateJdbcTemplate.queryForList(seqSelectSQL, String.class);
			if (seqList != null && seqList.size() > 0) {
				for (String seq : seqList) {
					updateJdbcTemplate.update("DROP SEQUENCE " + seq);
				}
			}
	    	
	    	// 查询当前年份使用的序列是否存在
	    	String seqFlagSQL = "SELECT COUNT(1) FROM USER_SEQUENCES WHERE SEQUENCE_NAME='"+SEQUENCE_NAME+"'";
	    	int seqFlag = updateJdbcTemplate.queryForObject(seqFlagSQL, Integer.class);
	    	if (seqFlag == 0) {
	    		// 序列不存在, 创建序列
	    		String seqCreateSQL = "CREATE SEQUENCE "+SEQUENCE_NAME+" INCREMENT BY 1 START WITH 1 MAXVALUE 9999999";
	    		updateJdbcTemplate.update(seqCreateSQL);
	    	}
	    	// 获取序列值, 并生成编号
	    	String seqValueSQL = "SELECT "+SEQUENCE_NAME+".NEXTVAL FROM DUAL";
	    	int seqValue = updateJdbcTemplate.queryForObject(seqValueSQL, Integer.class);
			if ( seqValue < 10 ) {
				DNA_RNA_CODE +=  "0000" + seqValue;
			} else if ( seqValue < 100 ) {
				DNA_RNA_CODE += "000" + seqValue;
			} else if ( seqValue < 1000 ) {
				DNA_RNA_CODE += "00" + seqValue;
			} else if ( seqValue < 10000 ) {
				DNA_RNA_CODE += "0" + seqValue;
			} else {
				DNA_RNA_CODE += seqValue;
			}
		}
		return DNA_RNA_CODE;
    }
    
    /**
     * 提取任务单: 任务明细样本: 我的提取任务: 提交提取结果(提交流程方案)
     */
    @RequestMapping("/tqTaskMy/submitFlow")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitFlow(@RequestBody JSONObject data) {
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL_tqTaskResult = "SELECT * FROM BR_TQ_MX_RESULT"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") and STATUS='待提取'";
    		List<Map<String, Object>> tqTaskResultList = queryJdbcTemplate.queryForList(selectSQL_tqTaskResult, ids.toArray());
    		
        	if ( tqTaskResultList != null && tqTaskResultList.size() > 0 ) {
	        	JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(updateJdbcTemplate);
	    		// 入口表表名
	        	String BR_MODUAL_tableName = "BR_MODUAL_TQ";
	            // 查询入口表结构
	            Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName(BR_MODUAL_tableName);
	            
	            //提取任务单明细
	            String selectSQL_tqTaskMx = "SELECT * FROM BR_TQ_MX_INFO WHERE ID=?";
            	//提取任务单主单
	            String selectSQL_tqTaskMain = "SELECT * FROM BR_TQ_INFO WHERE ID=?";
	            //提取任务模块表
	            String selectSQL_tqTaskRK = "SELECT * FROM " + BR_MODUAL_tableName + " WHERE ID=?";
	            //我的提取任务: 更新状态及提交后关联模块表ID
	            String updateSQL_tqTaskResult = "UPDATE BR_TQ_MX_RESULT SET STATUS='已提取',LINK_ID=? WHERE ID=?";
	            for (int i = 0; i < tqTaskResultList.size(); i++) {
	            	//提取任务单结果
	            	Map<String, Object> tqTaskResult = tqTaskResultList.get(i);
	            	//提取任务单明细
	            	Map<String, Object> tqTaskMx = queryJdbcTemplate.queryForMap(selectSQL_tqTaskMx, tqTaskResult.get("TQ_MX_ID") );
	            	//提取任务单主单
	            	Map<String, Object> tqTaskMain = queryJdbcTemplate.queryForMap(selectSQL_tqTaskMain, tqTaskResult.get("TQ_ID") );
	            	//提取任务模块表
	            	Map<String, Object> tqTaskRK = queryJdbcTemplate.queryForMap(selectSQL_tqTaskRK, tqTaskResult.get("TQ_MX_LINK_ID") );
	            	
	            	//入口表新ID
	            	String newRkID = SysBasic.getUUID();
	            	//处理数据
	            	tqTaskRK.putAll(tqTaskMain);
	            	tqTaskRK.putAll(tqTaskMx);
	            	tqTaskRK.putAll(tqTaskResult);
	            	tqTaskRK.put("ID", newRkID);
					
			    	// 判断是否变更新方案
			    	Object FLOW_PLAN_ID_NEW = tqTaskRK.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
			    	Object FLOW_PLAN_ID = tqTaskRK.get("FLOW_PLAN_ID");//模块表:原方案ID
			    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
			    		tqTaskRK.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
			    		tqTaskRK.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
			    	}
			    	
	            	//根据入口表字段，过滤查询结果多余的字段
		    		SysBasic.filterMap(tqTaskRK, metaMap.keySet());
		    		SysBasic.insertDataByTableMap(updateJdbcTemplate, BR_MODUAL_tableName, tqTaskRK);
		    		//提交流程
		    		List<String> rkIdList = new ArrayList<String>();
		    		rkIdList.add(newRkID);
		    		workflowUtils.set(queryJdbcTemplate, updateJdbcTemplate).submit(Modulars.TQ, rkIdList);
		    		
		    		//我的提取任务: 更新状态及提交后关联模块表ID
		    		updateJdbcTemplate.update(updateSQL_tqTaskResult, newRkID, tqTaskResult.get("ID"));
	            }
        	}
        	
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			log.info("/berry/prod/tq/tqTaskMy/submitFlow", e);
			log.error("/berry/prod/tq/tqTaskMy/submitFlow", e);
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
//    // 提取任务单: 任务明细样本: 我的提取任务: 终止提取任务
//    @RequestMapping("/tqTaskMy/tqTaskStop")
//    @Transactional(rollbackFor = Exception.class)
//    public JSONObject tqTaskStop(@RequestBody JSONObject data) {
//    	
//    	try {
//    		List<String> ids = (List<String>) data.get("ids");
//    		String updateSQL_tqTaskResult = "UPDATE BR_TQ_MX_RESULT SET STATUS='终止'"
//    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") and STATUS='待提取'";
//    		
//    		updateJdbcTemplate.update(updateSQL_tqTaskResult, ids.toArray());
//    		
//	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
//	    	
//		} catch (Exception e) {
//			
//			log.info("/berry/prod/tqTaskMy/tqTaskStop", e);
//			log.error("/berry/prod/tqTaskMy/tqTaskStop", e);
//			
//			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
//		}
//    }
}
