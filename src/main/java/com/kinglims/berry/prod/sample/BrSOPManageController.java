package com.kinglims.berry.prod.sample;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/sop/sopmanage")
@Slf4j
public class BrSOPManageController {

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private JdbcTemplate queryJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    /**
     * 保存SOP物料明细
     * @param data
     * @return
     */
    @RequestMapping("/saveSOPManage")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject saveSampleDetails(@RequestBody JSONObject data) {
    	try {
    		//新增或者更新SOP物料明细
    		String SOP_MANAGE_ID = data.getString("SOP_MANAGE_ID");//物料管理id
    		//保存前先删除，再新增
    		String delSql = "DELETE FROM BR_SOP_MANAGE_DETAIL WHERE SOP_MANAGE_ID=?";
    		updateJdbcTemplate.update(delSql, SOP_MANAGE_ID);
    		//保存明细
    		JSONArray sopDetail = data.getJSONArray("sopDetail");
    		String svaeSql ="INSERT INTO BR_SOP_MANAGE_DETAIL(ID, MATERIAL_CODE, MATERIAL_EXPLAIN, COMPONENT, MATERIAL_UNIT, SOP_MANAGE_ID, SORT_NO) "
    				+ "VALUES (?, ?, ?, ?, ?, ?, ?)";

    		for(int i=0; i<sopDetail.size(); i++) {
    			JSONObject saveObj = sopDetail.getJSONObject(i);
    			String ID = saveObj.getString("ID");
    			if(ID == null || ID.length() == 0) {
    				ID = SysBasic.getUUID();
    			}
    			String MATERIAL_CODE = saveObj.getString("MATERIAL_CODE");//物料编码
    			String MATERIAL_EXPLAIN = saveObj.getString("MATERIAL_EXPLAIN");//物料说明
    			String COMPONENT = saveObj.getString("COMPONENT");//组件
    			String MATERIAL_UNIT = saveObj.getString("MATERIAL_UNIT");//物料单位
    			String SORT_NO = (i+1)+"";
    			Object[] saveParam = {ID, MATERIAL_CODE, MATERIAL_EXPLAIN, COMPONENT, MATERIAL_UNIT, SOP_MANAGE_ID, SORT_NO};
    			updateJdbcTemplate.update(svaeSql, saveParam);
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sop/sopmanage/add", e);
			log.error("/berry/prod/sop/sopmanage/add", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    /**
     * SOP管理删除
     * @param data
     * @return
     */
    @RequestMapping("/deleteSOPManage")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject deleteSOPManage(@RequestBody JSONObject data) {
    	try {
    		JSONArray SOP_MANAGE_IDS = data.getJSONArray("SOP_MANAGE_IDS");//物料管理id
    		Object[] delParams = new Object[SOP_MANAGE_IDS.size()];
    		for(int i = 0; i<SOP_MANAGE_IDS.size(); i++) {
    			delParams[i] = SOP_MANAGE_IDS.get(i);
    			//删除物料明细
    			String delSOPDetail = "DELETE FROM BR_SOP_MANAGE_DETAIL WHERE SOP_MANAGE_ID=?";
    			updateJdbcTemplate.update(delSOPDetail, delParams[i]);
    		}
    		//删除物料管理
    		String delSOP = "DELETE FROM BR_SOP_MANAGE WHERE ID in ("+SysBasic.getQuestionMarkBySzie(delParams.length)+")";
    		updateJdbcTemplate.update(delSOP, delParams);
    		
    		return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    		
    	} catch (Exception e) {
    		
    		log.info("/berry/prod/sop/sopmanage/add", e);
    		log.error("/berry/prod/sop/sopmanage/add", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
    	}
    }
    
    /**
     * 库存修改（入库新增库存数、出库减去库存数）
     * @param data
     * @return
     */
    @RequestMapping("/updateKC")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject updateKC(@RequestBody JSONObject data) {
    	try {
    		String MATERIAL_CODE = data.getString("MATERIAL_CODE");//物料编码
    		String JR_TYPE = data.getString("JR_TYPE"); //流水类型(入库,出库)
    		Integer add_KC_NUM = StringUtils.isNotBlank(data.getString("JR_NUM")) ? Integer.parseInt(data.getString("JR_NUM")) : 0;//新增或减少的库存数
    		
    		//查询库存信息
    		String kcSql = "SELECT ID, MATERIAL_CODE, MATERIAL_NAME, KC_NUM FROM BR_MATERIAL_KC WHERE MATERIAL_CODE=?";
    		Map<String, Object> kcData = queryJdbcTemplate.queryForMap(kcSql, MATERIAL_CODE);
    		//更新库存信息
    		String updateSql = "UPDATE BR_MATERIAL_KC SET KC_NUM=? WHERE ID=?";
    		Integer KC_NUM = kcData.get("KC_NUM") == null ? 0 : Integer.parseInt(kcData.get("KC_NUM").toString());
    		if(JR_TYPE.equals("入库")) {
    			KC_NUM = KC_NUM+add_KC_NUM;
    		}else {
    			KC_NUM = KC_NUM-add_KC_NUM;
    		}
    		updateJdbcTemplate.update(updateSql, KC_NUM, kcData.get("ID"));

    		return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    		
    	} catch (Exception e) {
    		
    		log.info("/berry/prod/sop/sopmanage/add", e);
    		log.error("/berry/prod/sop/sopmanage/add", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
    	}
    }
}
