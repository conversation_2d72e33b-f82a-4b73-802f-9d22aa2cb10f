package com.kinglims.berry.prod.sample;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/sample/container")
@Slf4j
public class BrContainerController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    @RequestMapping("/treeview")
    public JSONObject treeview() {
    	
    	log.info("/berry/prod/sample/container/treeview ------------ 进入");
    	
    	try {
    		String sql = "SELECT * FROM BR_CONTAINER WHERE P_CODE='' OR P_CODE IS NULL ORDER BY SORT_NO";
    		List<Map<String, Object>> treeview = queryJdbcTemplate.queryForList(sql);
    		
    		if (treeview == null) {
    			treeview = new ArrayList<Map<String, Object>>();
    		} else {
    			for (Map<String, Object> m : treeview) {
    				List<Map<String, Object>> nodeList = treeviewNodeList( m.get("CODE")+"" );
    				if (nodeList != null && nodeList.size() > 0) {
    					m.put("items", nodeList);
    				}
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).put("treeview", treeview).getData();
    	
    	} catch (Exception e) {
    		
    		log.info("/berry/prod/sample/container/treeview", e);
    		log.error("/berry/prod/sample/container/treeview", e);
    		
    		return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    private List<Map<String, Object>> treeviewNodeList(String pCode) {
    	String sql = "SELECT * FROM BR_CONTAINER WHERE P_CODE=? ORDER BY SORT_NO";
    	List<Map<String, Object>> nodeList = queryJdbcTemplate.queryForList(sql, pCode);
    	if (nodeList != null) {
			for (Map<String, Object> m : nodeList) {
				List<Map<String, Object>> nodeSubList = treeviewNodeList( m.get("CODE")+"" );
				if (nodeSubList != null && nodeSubList.size() > 0) {
					m.put("items", nodeSubList);
				}
			}
		}
    	return nodeList;
    }
    
    @RequestMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject add(@RequestBody JSONObject data) {
    	
    	try {
    		String ID = data.getString("ID");
    		String CODE = data.getString("CODE");
    		String NAME = data.getString("NAME");
    		String NODE_TYPE = data.getString("NODE_TYPE");
    		String ORIFICE_PLATE_SIZE = data.getString("ORIFICE_PLATE_SIZE");
    		String ORIFICE_PLATE_TYPE = data.getString("ORIFICE_PLATE_TYPE");
    		String SORT_NO = data.getString("SORT_NO");
    		String REMARK = data.getString("REMARK");
    		String P_CODE = data.getString("P_CODE");
    		String IS_VALID = data.getString("IS_VALID");
    		String CREATOR = data.getString("CREATOR");
    		String CREATTIME = data.getString("CREATTIME");
    		String LASTUPDATOR = data.getString("LASTUPDATOR");
    		String LASTUPDATETIME = data.getString("LASTUPDATETIME");
    		// String tableName = data.getString("tableName");
    		
    		if ( ID == null || ID.trim().length()==0 ) {//新增
    			
    			String sql = "insert into BR_CONTAINER (ID,CODE,NAME,NODE_TYPE,ORIFICE_PLATE_SIZE,SORT_NO,REMARK,P_CODE,IS_VALID,CREATOR,CREATTIME,LASTUPDATOR,LASTUPDATETIME,ORIFICE_PLATE_TYPE,STATUS) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    			
    			ID = SysBasic.getUUID();
    			String STATUS = "2".equals(NODE_TYPE) ? "待入库" : null;
    			
    			Object[] args = {ID,CODE,NAME,NODE_TYPE,ORIFICE_PLATE_SIZE,SORT_NO,REMARK,P_CODE,IS_VALID,CREATOR,CREATTIME,LASTUPDATOR,LASTUPDATETIME,ORIFICE_PLATE_TYPE,STATUS};
    			
    			updateJdbcTemplate.update(sql, args);
    			
    			if ("2".equals(NODE_TYPE)) {// 按照规格创建孔位
    				
    				String[] xy = ORIFICE_PLATE_SIZE.split("\\*");
    				int x = Integer.parseInt(xy[0]);
    				int y = Integer.parseInt(xy[1]);
    				
    				String sql2 = "INSERT INTO BR_CONTAINER_ORIFICE_PLATE (ID, ORIFICE_PLATE_CODE, ORIFICE_CODE, ORIFICE_CODE_X, ORIFICE_CODE_Y, ORIFICE_ROW_NO, CREATOR, CREATTIME, LASTUPDATOR, LASTUPDATETIME) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    				Object[] args2 = new Object[10];
    				
    				for (int i = 0; i < y; i++) {
    					String Y = getYCode(i+1);
    					for (int j = 0; j < x; j++) {
		    				args2[0] = SysBasic.getUUID();//ID
		    				args2[1] = CODE;//ORIFICE_PLATE_CODE
		    				args2[2] = Y + ( (j+1) > 9 ? ""+(j+1) : "0"+(j+1) );//ORIFICE_CODE
		    				args2[3] = j+1;//ORIFICE_CODE_X
		    				args2[4] = Y;//ORIFICE_CODE_Y
		    				args2[5] = i+1;//ORIFICE_ROW_NO
		    				args2[6] = CREATOR;//CREATOR
		    				args2[7] = CREATTIME;//CREATTIME
		    				args2[8] = LASTUPDATOR;//LASTUPDATOR
		    				args2[9] = LASTUPDATETIME;//LASTUPDATETIME
		    				
		    				updateJdbcTemplate.update(sql2, args2);
						}
					}
    				
    			}
    			
    		} else {// 修改
    			
    			String sql = "update BR_CONTAINER set NAME=?,SORT_NO=?,REMARK=?,LASTUPDATOR=?,LASTUPDATETIME=? where ID=?";
    			Object[] args = {NAME,SORT_NO,REMARK,LASTUPDATOR,LASTUPDATETIME,ID};
    			updateJdbcTemplate.update(sql, args);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/container/add", e);
			log.error("/berry/prod/sample/container/add", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    public String getYCode(int i) {// 生成 A - ZZ
		String[] YArray = { "A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z" };
		String rs = "";
		String s = "";
		int z = 0;
		for (int j = 0; j < i && z < 26 ; j++) {
			if (j>0 && j%26==0 && z==26) {
				break;
			}
			if ( j>0 && j%26==0 ) {
				s = YArray[z];
				z = z + 1;
			}
			rs = s + YArray[j%26];
		}
		return rs;
	}
    
    @RequestMapping("/delnodes")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delTreeNodes(@RequestBody JSONObject data) {
    	
    	try {
    		String id = data.getString("id");
    		String code = data.getString("code");
    		
    		delTreeNodes(id, code);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/container/delnodes", e);
			log.error("/berry/prod/sample/container/delnodes", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    private void delTreeNodes(String id, String code) throws Exception {
		
    	String querySql = "select ID,CODE from BR_CONTAINER where P_CODE=?";
    	List<Map<String, Object>> list = queryJdbcTemplate.queryForList(querySql, code);
    	if (list != null && list.size() > 0) {
    		for (Map<String, Object> map : list) {
    			String rsId = map.get("ID")+"";
    			String rsCode = map.get("CODE")+"";
    			delTreeNodes(rsId, rsCode);
    		}
    	}
    	
		String delSql = "delete from BR_CONTAINER where ID=?";
		updateJdbcTemplate.update(delSql, id);
    	
		String delSql2 = "delete from BR_CONTAINER_ORIFICE_PLATE where ORIFICE_PLATE_CODE=?";
		updateJdbcTemplate.update(delSql2, code);
    }

    // 清孔
    @RequestMapping("/clearOrifice")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject clearOrifice(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		for (String id : ids) {//新增
    			String sql = "UPDATE BR_CONTAINER_ORIFICE_PLATE SET "
    					+ "SAMPLE_CODE=NULL,SAMPLE_NAME=NULL,SAMPLE_SOURCE=NULL,SAMPLE_TYPE=NULL,SAMPLE_NUMBER=NULL,SAMPLE_CONCENTRATION=NULL,SAMPLE_VOLUME=NULL"
    					+ ",LASTUPDATOR=?,LASTUPDATETIME=? WHERE ID=?";
    			Object[] args = {"","",id};
    			updateJdbcTemplate.update(sql, args);
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/container/clearOrifice", e);
			log.error("/berry/prod/sample/container/clearOrifice", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
