package com.kinglims.berry.prod.sample;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.security.entity.User;
import com.kinglims.framework.common.system.workflow.handler.logic.Modulars;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowMessage;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowUtils;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/sample/sample")
@Slf4j
public class BrSampleController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;

    @Autowired
    private WorkflowUtils workflowUtils;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 提交到审核
    @RequestMapping("/submitToApprove")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject submitToApprove(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String SAMPLE_SEND_MODE_NAME = data.getString("SAMPLE_SEND_MODE_NAME");
    		String SAMPLE_SEND_MODE_NO = data.getString("SAMPLE_SEND_MODE_NO");
    		// 更新基本信息: 状态
    		String sqlUPDATE_BASIC = "UPDATE BR_SAMPLE_INFO_BASIC SET STATUS='待审核',SAMPLE_SEND_MODE_NAME=?,SAMPLE_SEND_MODE_NO=? WHERE ID=? AND STATUS='草稿'";
    		for (String id : ids) {
    			updateJdbcTemplate.update(sqlUPDATE_BASIC, SAMPLE_SEND_MODE_NAME, SAMPLE_SEND_MODE_NO, id);
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/submitToApprove", e);
			log.error("/berry/prod/sample/sample/submitToApprove", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 撤销提交到审核 : 撤回草稿
    @RequestMapping("/revokeSubmitToApprove")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeSubmitToApprove(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String BACK_TO_CG_YY = data.getString("BACK_TO_CG_YY");
    		String BACK_TO_CG_IMG = data.getString("BACK_TO_CG_IMG");
    		// 更新基本信息: 状态
    		String sqlUPDATE_BASIC = "UPDATE BR_SAMPLE_INFO_BASIC SET STATUS='草稿',BACK_TO_CG_YY=?,BACK_TO_CG_IMG=? WHERE ID=? AND STATUS='待审核'";
    		for (String id : ids) {
    			updateJdbcTemplate.update(sqlUPDATE_BASIC, BACK_TO_CG_YY, BACK_TO_CG_IMG, id);
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/revokeSubmitToApprove", e);
			log.error("/berry/prod/sample/sample/revokeSubmitToApprove", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 撤销提交到审核 : 撤回待审核
    @RequestMapping("/revokeToApprove")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeToApprove(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		// 更新样本基本信息: 状态
    		String sqlUPDATE_BASIC = "UPDATE BR_SAMPLE_INFO_BASIC SET STATUS='待审核' WHERE ID=? AND STATUS='收样完成'";
    		// 重置样本明细: 收样状态
    		String sqlUPDATE_DETAIL = "UPDATE BR_SAMPLE_INFO_DETAIL SET STATUS=NULL WHERE SIB_ID=?";
    		// 删除入口表
    		String sqlDELETE_SM = "DELETE FROM BR_MODUAL_SM WHERE SAMPLE_ID IN (SELECT ID FROM BR_SAMPLE_INFO_DETAIL WHERE SIB_ID=?)";
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(sqlUPDATE_BASIC, id);
    			if (i > 0) {
    				updateJdbcTemplate.update(sqlUPDATE_DETAIL, id);
    				updateJdbcTemplate.update(sqlDELETE_SM, id);
    			}
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/revokeToApprove", e);
			log.error("/berry/prod/sample/sample/revokeToApprove", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 样本明细单，确认收样
    @RequestMapping("/sampleDetailsReceive")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject sampleDetailsReceive(@RequestBody JSONObject data) {
    	
    	try {
    		// 明细信息
    		String SAMPLE_SEND_MAN_ID = data.getString("SAMPLE_SEND_MAN_ID");//送样人
    		String SAMPLE_SEND_MAN = data.getString("SAMPLE_SEND_MAN");
    		Date SAMPLE_SEND_DATE = data.getDate("SAMPLE_SEND_DATE");//送样时间
    		String SAMPLE_RECEIVE_SURVEYOR_ID = data.getString("SAMPLE_RECEIVE_SURVEYOR_ID");//接收人
    		String SAMPLE_RECEIVE_SURVEYOR = data.getString("SAMPLE_RECEIVE_SURVEYOR");
    		Date SAMPLE_RECEIVE_DATE = data.getDate("SAMPLE_RECEIVE_DATE");//接收时间
    		String EXAMINE_RS = data.getString("EXAMINE_RS");//检验结果
    		String CARRIAGE_CONDITIONS = data.getString("CARRIAGE_CONDITIONS");//存储条件
    		String SAMPLE_RECEIVE_REMARK = data.getString("SAMPLE_RECEIVE_REMARK");//接收备注
    		
    		//样本ID
    		List<String> ids = (List<String>) data.get("ids");
    		//送检主单ID
    		String SIB_ID = data.getString("SIB_ID");
    		// 主单信息: 确认接收信息
    		String SAMPLE_SEND_MODE_CONFIRM = data.getString("SAMPLE_SEND_MODE_CONFIRM");
    		//列表信息
    		JSONArray rData = data.getJSONArray("rData");
    		
    		// 查询样本号是否存在
    		String selectSQL_DETAIL_C1 = "SELECT COUNT(1) C1 FROM BR_SAMPLE_INFO_DETAIL WHERE ID=? AND SAMPLE_CODE IS NULL";
    		// 样本明细,确认收样状态
    		String updateSQL_DETAIL = "UPDATE BR_SAMPLE_INFO_DETAIL SET STATUS='确认收样'"
    				+ ",SAMPLE_SEND_MAN_ID=?"
    				+ ",SAMPLE_SEND_MAN=?"
    				+ ",SAMPLE_SEND_DATE=?"
    				+ ",SAMPLE_RECEIVE_SURVEYOR_ID=?"
    				+ ",SAMPLE_RECEIVE_SURVEYOR=?"
    				+ ",SAMPLE_RECEIVE_DATE=?"
    				+ ",EXAMINE_RS=?"
    				+ ",CARRIAGE_CONDITIONS=?"
    				+ ",SAMPLE_RECEIVE_REMARK=?"
    				+ ",SAMPLE_CODE=SAMPLE_CODE"
    				+ " WHERE ID=? AND STATUS IS NULL";
    		// 更新主单收样状态
    		String selectSQL_DETAIL_C2 = "SELECT COUNT(1) C2 FROM BR_SAMPLE_INFO_DETAIL WHERE SIB_ID=?";
    		String selectSQL_DETAIL_C3 = "SELECT COUNT(1) C3 FROM BR_SAMPLE_INFO_DETAIL WHERE SIB_ID=? AND STATUS='确认收样'";
    		String updateSQL_BASIC = "UPDATE BR_SAMPLE_INFO_BASIC SET STATUS=?, SAMPLE_SEND_MODE_CONFIRM=? WHERE ID=?";
    		
    		int updateFlag = 0;
    		for (int i = 0; i < rData.size(); i++) {
    			JSONObject r = rData.getJSONObject(i);
    			String ID = r.getString("ID");
    			String SAMPLE_SEND_TYPE = r.getString("SAMPLE_SEND_TYPE");//送样单类型
    			String SAMPLE_TYPE = r.getString("SAMPLE_TYPE");//样本类型
    			
    			// 更新语句
    			String updateSQL_BY_ID = updateSQL_DETAIL;
    			
    			// 生成样本编号  SAMPLE_CODE LIB_CODE
    			int c1 = updateJdbcTemplate.queryForObject(selectSQL_DETAIL_C1, Integer.class, ID);
    			if (c1 > 0) {
    				String SAMPLE_CODE = generateSAMPLE_CODE(SAMPLE_SEND_TYPE, SAMPLE_TYPE);
    				String SAMPLE_CODE_SQL = ",SAMPLE_CODE='"+SAMPLE_CODE+"'";
    				if (SAMPLE_SEND_TYPE.indexOf("文库") > -1) {
    					SAMPLE_CODE_SQL += ",LIB_CODE='"+SAMPLE_CODE+"'";
    				}
    				updateSQL_BY_ID = updateSQL_BY_ID.replace(",SAMPLE_CODE=SAMPLE_CODE", SAMPLE_CODE_SQL);
    			}
    			
    			// 更新收样信息
    			Object[] args = { SAMPLE_SEND_MAN_ID,SAMPLE_SEND_MAN,SAMPLE_SEND_DATE,SAMPLE_RECEIVE_SURVEYOR_ID
    					,SAMPLE_RECEIVE_SURVEYOR,SAMPLE_RECEIVE_DATE,EXAMINE_RS,CARRIAGE_CONDITIONS,SAMPLE_RECEIVE_REMARK, ID };
    			int nextFlag = updateJdbcTemplate.update(updateSQL_BY_ID, args);
    			if (nextFlag > 0) {
    				updateFlag++;
    				// 确认收样，写入业务流程入口表
    				insertRKB(SIB_ID, ID, SAMPLE_SEND_TYPE);
    			}
    		}
    		// 更新主单收样状态
    		if (updateFlag > 0) {
	    		int c2 = updateJdbcTemplate.queryForObject(selectSQL_DETAIL_C2, Integer.class, SIB_ID);
	    		int c3 = updateJdbcTemplate.queryForObject(selectSQL_DETAIL_C3, Integer.class, SIB_ID);
	    		if (c2 == c3 && c3 > 0) {
	    			updateJdbcTemplate.update(updateSQL_BASIC, "收样完成", SAMPLE_SEND_MODE_CONFIRM, SIB_ID);
	    		} else if (c2 != c3 && c3 > 0) {
	    			updateJdbcTemplate.update(updateSQL_BASIC, "部分已确认收样", SAMPLE_SEND_MODE_CONFIRM, SIB_ID);
	    		}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/sampleDetailsReceive", e);
			log.error("/berry/prod/sample/sample/sampleDetailsReceive", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 样本号生成
    private String generateSAMPLE_CODE(String SAMPLE_SEND_TYPE, String SAMPLE_TYPE) {
    	SAMPLE_TYPE = SAMPLE_TYPE==null ? "" : SAMPLE_TYPE;
    	// 样本年号
		int SAMPLE_CODE_YY = new Date().getYear() + 1900 - 2000;
    	//样本号前缀
    	String SAMPLE_CODE_TYPE = "O";//无法识别的情况, 默认 other
    	if ( "二代DNA样品建库测序信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "D";
    	}
    	else if ( "二代RNA样品建库测序信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "R";
    	}
    	else if ( "三代DNA样品建库测序信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "T";
    	}
    	else if ( "三代RNA样品建库测序信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "R";
    	}
    	else if ( "二代单上机文库信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "W";
    	}
    	else if ( "三代单上机文库信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "P";
    	}
    	else if ( "二代/三代待提取材料信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "Z";
    	}
    	else if ( "Bionano待提取样品信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "B";
    	}
    	else if ( "Hi-C待提取样品信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "H";
    	}
    	else if ( "10X Genomics单细胞建库测序样品信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "M";
    	}
    	else if ( "10X Genomics空间转录组待提取材料信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "M";
    	}
    	else if ( "10X Genomics空间转录组样品信息单".equals(SAMPLE_SEND_TYPE) ) {
    		SAMPLE_CODE_TYPE = "M";
    	}
    	else if ( "FFPE样品信息单".equals(SAMPLE_SEND_TYPE) && SAMPLE_TYPE.contains("DNA") ) {// FFEPE DNA
    		SAMPLE_CODE_TYPE = "D";
    	}
    	else if ( "FFPE样品信息单".equals(SAMPLE_SEND_TYPE) ) {// FFPE 组织
    		SAMPLE_CODE_TYPE = "Z";
    	}
    	else if ( "极速外显子建库测序样品信息单".equals(SAMPLE_SEND_TYPE) && "待提取".equals("111") ) {
    		SAMPLE_CODE_TYPE = "K";
    	}
    	else if ( "极速外显子建库测序样品信息单".equals(SAMPLE_SEND_TYPE) && "DNA".equals("111") ) {
    		SAMPLE_CODE_TYPE = "A";
    	}
    	
    	//样本编号
    	String SAMPLE_CODE = SAMPLE_CODE_YY + SAMPLE_CODE_TYPE;
    	
    	// 序列名称前缀
    	String SEQUENCE_NAME_PREFIX = "SEQ_SAMPLE_CODE_";
    	// 序列名称
    	String SEQUENCE_NAME = SEQUENCE_NAME_PREFIX + SAMPLE_CODE_TYPE + "_" + SAMPLE_CODE_YY;
    	
		// 查询不是当前年号的样本编号序列, 并删除
		String seqSelectSQL = "SELECT SEQUENCE_NAME FROM USER_SEQUENCES"
				+ " WHERE SEQUENCE_NAME LIKE '"+SEQUENCE_NAME_PREFIX+"%' "
				+ " AND SEQUENCE_NAME NOT LIKE '%_"+SAMPLE_CODE_YY+"' ";
		List<String> seqList = updateJdbcTemplate.queryForList(seqSelectSQL, String.class);
		if (seqList != null && seqList.size() > 0) {
			for (String seq : seqList) {
				updateJdbcTemplate.update("DROP SEQUENCE " + seq);
			}
		}
    	
    	// 查询当前年份使用的序列是否存在
    	String seqFlagSQL = "SELECT COUNT(1) FROM USER_SEQUENCES WHERE SEQUENCE_NAME='"+SEQUENCE_NAME+"'";
    	int seqFlag = updateJdbcTemplate.queryForObject(seqFlagSQL, Integer.class);
    	if (seqFlag == 0) {
    		// 序列不存在, 创建序列
    		String seqCreateSQL = "CREATE SEQUENCE "+SEQUENCE_NAME+" INCREMENT BY 1 START WITH 1 MAXVALUE 9999999";
    		updateJdbcTemplate.update(seqCreateSQL);
    	}
    	
    	// 获取序列值, 并生成编号
    	String seqValueSQL = "SELECT "+SEQUENCE_NAME+".NEXTVAL FROM DUAL";
    	int seqValue = updateJdbcTemplate.queryForObject(seqValueSQL, Integer.class);
		if ( seqValue < 10 ) {
			SAMPLE_CODE +=  "0000" + seqValue;
		} else if ( seqValue < 100 ) {
			SAMPLE_CODE += "000" + seqValue;
		} else if ( seqValue < 1000 ) {
			SAMPLE_CODE += "00" + seqValue;
		} else if ( seqValue < 10000 ) {
			SAMPLE_CODE += "0" + seqValue;
		} else {
			SAMPLE_CODE += seqValue;
		}
    	
    	return SAMPLE_CODE;
    }
    // 确认收样，写入业务流程入口表
    private boolean insertRKB(String SIB_ID, String ID, String SAMPLE_SEND_TYPE) {
    	boolean rs = true;
    	
    	JdbcTemplateUtils jdbcTemplateUtils=new JdbcTemplateUtils(updateJdbcTemplate);
    	// 入口表表名
    	String BR_MODUAL_tableName = "BR_MODUAL_SM";
        // 查询入口表结构
        Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName(BR_MODUAL_tableName);
    	
    	String sql_1 = "SELECT * FROM BR_SAMPLE_INFO_BASIC WHERE ID=?";
    	String sql_3 = "SELECT * FROM BR_SAMPLE_INFO_DETAIL WHERE ID=?";
    	
    	Map<String,Object> resultMap = jdbcTemplateUtils.queryTableMapBySqlcode(sql_1, SIB_ID);
    	Map<String,Object> resultMap3 = jdbcTemplateUtils.queryTableMapBySqlcode(sql_3, ID);
    	// 合并数据
    	resultMap.putAll(resultMap3);
    	// 数据处理
    	resultMap.put("ID", SysBasic.getUUID());// 创建新ID
    	resultMap.put("SAMPLE_ID", resultMap3.get("ID"));// 设置样本ID
    	// 设置初始收样流程方案
    	if ("极速外显子建库测序样品信息单".equals(SAMPLE_SEND_TYPE)) {
    		resultMap.put("FLOW_PLAN_ID", "bcb8602f-f995-4549-a5d3-935277cca0b1");
    	} else {
    		resultMap.put("FLOW_PLAN_ID", "bcb8602f-f995-4549-a5d3-935277cca0b1");
    	}
    	
    	//根据入口表字段，过滤查询结果多余的字段
    	SysBasic.filterMap(resultMap, metaMap.keySet());
    	// 执行插入样本收样入口表
    	int u = SysBasic.insertDataByTableMap(updateJdbcTemplate, BR_MODUAL_tableName, resultMap);
    	if (u < 1) {
    		rs = false;
    	}
    	return rs;
    }
    
    /**
     * 流程提交
     * @param map
     * @return
     */
    @RequestMapping(value = "/SMwfSubmit", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject SMwfSubmit(@RequestBody JSONObject data) {
    	try {
    		List<String> SIB_IDS = (List<String>) data.get("ids");
    		
    		String sqlSelectSMIDS = "SELECT SM.ID FROM BR_MODUAL_SM SM, BR_SAMPLE_INFO_BASIC SIB WHERE SM.SIB_ID IN (";
    		for (int i = 0; i < SIB_IDS.size(); i++) {
    			sqlSelectSMIDS += (i > 0 ? ",?" : "?");
    		}
    		sqlSelectSMIDS += ") AND SM.SIB_ID=SIB.ID AND SIB.STATUS='收样完成'";
    		
    		List<String> ids = queryJdbcTemplate.queryForList(sqlSelectSMIDS, String.class, SIB_IDS.toArray());
    		
    		WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submit(Modulars.SM,ids);
    		
    		if (ids!=null && ids.size() > 0 && wflowMsg.isB()) {// 更新: BR_SAMPLE_INFO_BASIC表状态
    			String sqlUpdate = "UPDATE BR_SAMPLE_INFO_BASIC SET STATUS='已提交方案' WHERE ID IN (";
        		for (int i = 0; i < SIB_IDS.size(); i++) {
        			sqlUpdate += (i > 0 ? ",?" : "?");
        		}
        		sqlUpdate += ")";
        		updateJdbcTemplate.update(sqlUpdate, SIB_IDS.toArray());
    		}
    		
    		int rsCode = wflowMsg.isB() ? 1 : -1;
	    	return new CurrResponseResolve(rsCode).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/SMwfSubmit", e);
			log.error("/berry/prod/sample/sample/SMwfSubmit", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    
    @RequestMapping(value = "/revokeSMwfSubmit", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject revokeSMwfSubmit(@RequestBody JSONObject data) {
    	try {
    		List<String> SIB_IDS = (List<String>) data.get("ids");
    		
    		String sqlSelectSMIDS = "SELECT SM.ID FROM BR_MODUAL_SM SM, BR_SAMPLE_INFO_BASIC SIB WHERE SM.SIB_ID IN (";
    		for (int i = 0; i < SIB_IDS.size(); i++) {
    			sqlSelectSMIDS += (i > 0 ? ",?" : "?");
    		}
    		sqlSelectSMIDS += ") AND SM.SIB_ID=SIB.ID AND SIB.STATUS='已提交方案'";
    		
    		List<String> ids = queryJdbcTemplate.queryForList(sqlSelectSMIDS, SIB_IDS.toArray(), String.class);
    		
    		WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.SM,ids);
    		
    		if (ids!=null && ids.size() > 0 && wflowMsg.isB()) {// 更新: BR_SAMPLE_INFO_BASIC表状态
    			String sqlUpdate = "UPDATE BR_SAMPLE_INFO_BASIC SET STATUS='收样完成' WHERE ID IN (";
        		for (int i = 0; i < SIB_IDS.size(); i++) {
        			sqlUpdate += (i > 0 ? ",?" : "?");
        		}
        		sqlUpdate += ")";
        		updateJdbcTemplate.update(sqlUpdate, SIB_IDS.toArray());
    		}
    		
    		int rsCode = wflowMsg.isB() ? 1 : -1;
	    	return new CurrResponseResolve(rsCode).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/revokeSMwfSubmit", e);
			log.error("/berry/prod/sample/sample/revokeSMwfSubmit", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    
  	/**
  	 * 样本入库
  	 * @param data
  	 * @return
  	 */
    @RequestMapping("/selectSample")
    @Transactional
    public JSONObject selectSample(@RequestBody JSONObject data) {
    	log.info("/berry/prod/sample/sample/selectSample 进入");
    	
    	String RK_SX = data.getString("RK_SX");
    	String ORIFICE_PLATE_CODE = data.getString("ORIFICE_PLATE_CODE");
		JSONArray sampleArr = data.getJSONArray("SAMPLE_INFO");
		
    	try {
    		String querySql = "SELECT ID, ORIFICE_PLATE_CODE, ORIFICE_CODE, ORIFICE_CODE_X, ORIFICE_CODE_Y, ORIFICE_ROW_NO, SAMPLE_CODE, SAMPLE_NAME, SAMPLE_SOURCE, SAMPLE_TYPE, SAMPLE_NUMBER, SAMPLE_CONCENTRATION, SAMPLE_VOLUME"+
    				" FROM BR_CONTAINER_ORIFICE_PLATE T"+
    				" WHERE T.ORIFICE_PLATE_CODE = ? AND T.SAMPLE_CODE IS NULL";
    		String orderBy = " ORDER BY ORIFICE_CODE_X, ORIFICE_CODE_Y";//纵向存入
    		if(RK_SX.equals("横向")) {
    			orderBy = " ORDER BY ORIFICE_CODE_Y, ORIFICE_CODE_X";//纵向存入
    		}
    		querySql = querySql+orderBy;
    		List<Map<String, Object>> orificePlate= queryJdbcTemplate.queryForList(querySql, ORIFICE_PLATE_CODE);
    		int sizeInt = orificePlate.size() > sampleArr.size() ? sampleArr.size() : orificePlate.size();
    		for(int i=0; i<sizeInt; i++) {
    			JSONObject sampleObj = sampleArr.getJSONObject(i);//样本明细
    			Map<String, Object> map = orificePlate.get(i);//容器板孔信息
    			String SAMPLE_CODE = sampleObj.getString("SAMPLE_CODE");//样本编号
    			String SAMPLE_NAME = sampleObj.getString("SAMPLE_NAME");//样本名称
    			String SAMPLE_NUMBER = sampleObj.getString("SAMPLE_NUMBER");//样本数量
    			String SAMPLE_CONCENTRATION = sampleObj.getString("CONCENTRATION");//浓度
    			String SAMPLE_VOLUME = sampleObj.getString("VOLUME");//体积
    			//更新样本:容器板孔信息表
    			String orificePlateSql = "UPDATE BR_CONTAINER_ORIFICE_PLATE SET SAMPLE_CODE=?, SAMPLE_NAME=?, SAMPLE_NUMBER=?, SAMPLE_CONCENTRATION=?, SAMPLE_VOLUME=? WHERE ID=?";
    			Object[] orificePlateParams = {SAMPLE_CODE, SAMPLE_NAME, SAMPLE_NUMBER, SAMPLE_CONCENTRATION, SAMPLE_VOLUME, map.get("ID").toString()};
    			updateJdbcTemplate.update(orificePlateSql, orificePlateParams);
    			//更新样本明细信息表
    			String ORIFICE_CODE = map.get("ORIFICE_CODE").toString();//孔口号
    			String ID = sampleObj.getString("ID");//明细ID
    			String sampleSql = "UPDATE BR_SAMPLE_INFO_DETAIL SET ORIFICE_CODE=?, ORIFICE_PLATE_CODE=?, STATUS=? WHERE ID=?";
    			Object[] sampleParams = {ORIFICE_CODE, ORIFICE_PLATE_CODE, "已入库", ID};
    			updateJdbcTemplate.update(sampleSql, sampleParams); 
    		}
    		log.info("/berry/prod/sample/sample/selectSample  完成");
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/selectSample", e);
			log.error("/berry/prod/sample/sample/selectSample", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 保存送检主单
    @RequestMapping("/saveSampleInfoBasic")
    @Transactional
    public JSONObject saveSampleInfoBasic(@RequestBody JSONObject data, @RequestHeader(name="Authorization")String token) {
    	log.info("/berry/prod/sample/sample/saveSampleInfoBasic 进入");
    	try {
    		User user = SysBasic.getUserByTokenRedis(token);
    		
    		String ID = data.getString("ID");
    		ID = ID==null ? "" : ID;
    		// 数据处理
    		data.put("SAMPLE_SEND_DATE", data.getDate("SAMPLE_SEND_DATE") );//送样日期转为日期
    		data.remove("SAMPLE_COUNT");//移除样本数量: 添加明细时更新
    		
    		String tableName = "BR_SAMPLE_INFO_BASIC";
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(queryJdbcTemplate);
    		Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName(tableName);
    		
    		SysBasic.filterMap(data, metaMap.keySet());//过滤表不存在的字段
    		
    		Date nowDateTime = new Date();
    		if ("".equals(ID)) {
    			ID = SysBasic.getUUID();
    			data.put("ID", ID);
    			data.put("STATUS", "草稿");
    			data.put("CREATOR", user.getID());
    			data.put("CREATTIME", nowDateTime);
    			data.put("LASTUPDATOR", user.getID());
    			data.put("LASTUPDATETIME", nowDateTime);
    			// data.put("LOGINCOMPANY", );//帐套
    			SysBasic.insertDataByTableMap(updateJdbcTemplate, tableName, data);
    		} else {
    			data.put("LASTUPDATOR", user.getID());
    			data.put("LASTUPDATETIME", nowDateTime);
    			SysBasic.updateDataByTableMap(updateJdbcTemplate, tableName, data);
    		}
    		
    		return new CurrResponseResolve(1).put("ID", ID).put(responseMessageParameter).getData();
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/saveSampleInfoBasic", e);
			log.error("/berry/prod/sample/sample/saveSampleInfoBasic", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 更新送检主单样本数量; 样本明细排序号
    @RequestMapping("/updateSAMPLE_COUNT")
    @Transactional
    public JSONObject updateSAMPLE_COUNT(@RequestBody JSONObject data) {
    	log.info("/berry/prod/sample/sample/updateSAMPLE_COUNT 进入");
    	try {
    		String SIB_ID = data.getString("SIB_ID");
    		String SAMPLE_ID = data.getString("SAMPLE_ID");
    		String LIB_INDEX_SEQ = data.getString("LIB_INDEX_SEQ");
    		LIB_INDEX_SEQ = LIB_INDEX_SEQ==null ? "" : LIB_INDEX_SEQ.trim();
    		String LIB_INDEX_CODE="";
    		
    		// 更新送检主单: 样本数量
    		String selectSQL_COUNT = "select count(1) from BR_SAMPLE_INFO_DETAIL where SIB_ID=?";
    		int SAMPLE_COUNT = queryJdbcTemplate.queryForObject(selectSQL_COUNT, Integer.class, SIB_ID);
    		String updateSQL_COUNT = "update BR_SAMPLE_INFO_BASIC set SAMPLE_COUNT=? where ID=?";
    		updateJdbcTemplate.update(updateSQL_COUNT, SAMPLE_COUNT, SIB_ID);
    		
    		// 更新样本排序号
    		String selectSQL_SORT_COUNT = "select count(1) as c, max(SORT_NO) as m from BR_SAMPLE_INFO_DETAIL where SIB_ID=? and SORT_NO is not null";
    		Map<String, Object> countRS = queryJdbcTemplate.queryForMap(selectSQL_SORT_COUNT, SIB_ID);
    		
    		String selectSQL_SORT_IDS = "select ID from BR_SAMPLE_INFO_DETAIL where SIB_ID=? and SORT_NO is null";
    		List<String> dIDS = queryJdbcTemplate.queryForList(selectSQL_SORT_IDS, String.class, SIB_ID);
    		
    		int c = ( (BigDecimal) countRS.get("c") ).intValue();
    		int m;
    		if (c == 0 || countRS.get("m")==null) {
    			m = 0;
    		} else {
    			m = ( (BigDecimal) countRS.get("m") ).intValue();
    		}
    		for (String dID : dIDS) {
    			m = m + 1;
    			updateJdbcTemplate.update("update BR_SAMPLE_INFO_DETAIL set SORT_NO=? where ID=?", m, dID);
    		}
    		
    		//更新样本INDEX编号
    		if ( !"".equals(LIB_INDEX_SEQ) ) {
    			LIB_INDEX_SEQ = LIB_INDEX_SEQ.trim();
    			String selectSQL = "SELECT ITEM_CODE FROM BR_DATA_DICT WHERE CODE='INDEX' AND IS_VALID='Y' AND ITEM_TEXT=?";
        		List<String> itemCodeList = queryJdbcTemplate.queryForList(selectSQL, String.class, LIB_INDEX_SEQ);
        		if (itemCodeList!=null && itemCodeList.size()>0) {
        			LIB_INDEX_CODE = itemCodeList.get(0);
            		String updateSQL = "UPDATE BR_SAMPLE_INFO_DETAIL SET LIB_INDEX_CODE=? WHERE ID=?";
        			updateJdbcTemplate.update(updateSQL, LIB_INDEX_CODE, SAMPLE_ID);
        		}
    		}
    		
    		return new CurrResponseResolve(1).put("SAMPLE_COUNT", SAMPLE_COUNT).put("LIB_INDEX_CODE", LIB_INDEX_CODE).put("LIB_INDEX_SEQ", LIB_INDEX_SEQ).put(responseMessageParameter).getData();
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/updateSAMPLE_COUNT", e);
			log.error("/berry/prod/sample/sample/updateSAMPLE_COUNT", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除送样主单
    @RequestMapping("/delSampleInfos")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delSampleInfos(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String STATUS = data.getString("STATUS");
    		// 删除基本信息
    		String sqlDEL_BASIC = "DELETE FROM BR_SAMPLE_INFO_BASIC WHERE ID=? AND STATUS=?";
    		// 删除样本明细
    		String sqlDEL_DETAIL = "DELETE FROM BR_SAMPLE_INFO_DETAIL WHERE SIB_ID=?";
    		// 删除样本明细: 子文库
    		String sqlDEL_DETAIL_LIB = "DELETE FROM BR_SAMPLE_INFO_DETAIL_LIB WHERE SIB_ID=?";
    		// 删除入口数据
    		String sqlDEL_SM = "DELETE FROM BR_MODUAL_SM WHERE SIB_ID=?";
    		// 删除3个表的数据
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(sqlDEL_BASIC, id, STATUS);
    			if (i > 0) {
	    			updateJdbcTemplate.update(sqlDEL_DETAIL, id);
	    			updateJdbcTemplate.update(sqlDEL_DETAIL_LIB, id);
					updateJdbcTemplate.update(sqlDEL_SM, id);
    			}
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/saveSampleDetails", e);
			log.error("/berry/prod/sample/sample/saveSampleDetails", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除样本明细
    @RequestMapping("/delSampleMx")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delSampleMx(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		// 删除样本明细
    		String sqlDEL_DETAIL = "DELETE FROM BR_SAMPLE_INFO_DETAIL MX"
    				+ " WHERE MX.ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")"
    				+ " AND (SELECT COUNT(1) FROM BR_SAMPLE_INFO_BASIC M WHERE M.ID=MX.SIB_ID AND M.STATUS='草稿')>0";
    		// 删除入口数据
    		String sqlDEL_SM = "DELETE FROM BR_MODUAL_SM SM"
    				+ " WHERE SM.SAMPLE_ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")"
    				+ " AND (SELECT COUNT(1) FROM BR_SAMPLE_INFO_BASIC M WHERE M.ID=SM.SIB_ID AND M.STATUS='草稿')>0";
    		
    		updateJdbcTemplate.update(sqlDEL_DETAIL, ids.toArray());
    		updateJdbcTemplate.update(sqlDEL_SM, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/delSampleMx", e);
			log.error("/berry/prod/sample/sample/delSampleMx", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 更新文库 INDEX CODE : 根据输入的序列
    @RequestMapping("/updateSampleMxLib_INDEX_CODE_TOMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject updateSampleMxLib_INDEX_CODE_TOMX(@RequestBody JSONObject data) {
    	
    	try {
    		String SAMPLE_ID = data.getString("SAMPLE_ID");
    		
    		String selectSQL = "SELECT LIB_INDEX_CODE, LIB_INDEX_SEQ FROM BR_SAMPLE_INFO_DETAIL_LIB"
    				+ " WHERE SAMPLE_ID=? ORDER BY SORT_NO";
    		List<Map<String,Object>> rsList = queryJdbcTemplate.queryForList(selectSQL, SAMPLE_ID);
    		
    		int D_LIB_NUM = 0;
    		String LIB_INDEX_CODE = "";
    		String LIB_INDEX_SEQ = "";
    		if (rsList!=null && rsList.size()>0) {
    			D_LIB_NUM = rsList.size();
	    		for (int i=0; i<rsList.size(); i++) {
	    			Map<String,Object> rs = rsList.get(i);
	    			if (i>0) {
	    				LIB_INDEX_CODE += "-";
	    	    		LIB_INDEX_SEQ += "-";
	    			}
					LIB_INDEX_CODE += rs.get("LIB_INDEX_CODE");
		    		LIB_INDEX_SEQ += rs.get("LIB_INDEX_SEQ");
	    		}
	    		Object[] args = { LIB_INDEX_CODE, LIB_INDEX_SEQ, SAMPLE_ID };
	    		updateJdbcTemplate.update("UPDATE BR_SAMPLE_INFO_DETAIL SET LIB_INDEX_CODE=?, LIB_INDEX_SEQ=? WHERE ID=?", args);
    		}
    		
	    	return new CurrResponseResolve(1).put("D_LIB_NUM", D_LIB_NUM).put("LIB_INDEX_CODE",LIB_INDEX_CODE).put("LIB_INDEX_SEQ",LIB_INDEX_SEQ).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/updateSampleMxLib_INDEX_CODE_TOMX", e);
			log.error("/berry/prod/sample/sample/updateSampleMxLib_INDEX_CODE_TOMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 更新子文库 INDEX CODE : 根据输入的序列
    @RequestMapping("/updateSampleMxLib_INDEX_CODE")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject updateSampleMxLib_INDEX_CODE(@RequestBody JSONObject data) {
    	
    	try {
    		//String SIB_ID = data.getString("SIB_ID");
    		String SAMPLE_ID = data.getString("SAMPLE_ID");
    		String ID = data.getString("ID");
    		String LIB_INDEX_SEQ = data.getString("LIB_INDEX_SEQ");
    		
    		// 更新INDEX_CODE
    		String selectSQL = "SELECT ITEM_CODE FROM BR_DATA_DICT WHERE CODE='INDEX' AND IS_VALID='Y' AND ITEM_TEXT=?";
    		List<String> itemCodeList = queryJdbcTemplate.queryForList(selectSQL, String.class, LIB_INDEX_SEQ);

    		String updateSQL = "UPDATE BR_SAMPLE_INFO_DETAIL_LIB SET LIB_INDEX_CODE=? WHERE ID=?";
    		if (itemCodeList!=null && itemCodeList.size()>0) {
    			updateJdbcTemplate.update(updateSQL, itemCodeList.get(0), ID);
    		}
    		
    		// 更新排序号
    		String selectSQL_COUNT = "select count(1) as c, max(SORT_NO) as m from BR_SAMPLE_INFO_DETAIL_LIB where SAMPLE_ID=? and SORT_NO is not null";
    		Map<String, Object> countRS = queryJdbcTemplate.queryForMap(selectSQL_COUNT, SAMPLE_ID);
    		
    		String selectSQL_IDS = "select ID from BR_SAMPLE_INFO_DETAIL_LIB where SAMPLE_ID=? and SORT_NO is null";
    		List<String> dLibIDS = queryJdbcTemplate.queryForList(selectSQL_IDS, String.class, SAMPLE_ID);
    		
    		int c = ( (BigDecimal) countRS.get("c") ).intValue();
    		int m;
    		if (c == 0 || countRS.get("m")==null) {
    			m = 0;
    		} else {
    			m = ( (BigDecimal) countRS.get("m") ).intValue();
    		}
    		for (String dLibID : dLibIDS) {
    			m = m + 1;
    			updateJdbcTemplate.update("update BR_SAMPLE_INFO_DETAIL_LIB set SORT_NO=? where ID=?", m, dLibID);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/sample/updateSampleMxLib_INDEX_CODE", e);
			log.error("/berry/prod/sample/sample/updateSampleMxLib_INDEX_CODE", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
