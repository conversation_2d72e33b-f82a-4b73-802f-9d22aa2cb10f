package com.kinglims.berry.prod.sample;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/sample/ready")
@Slf4j
public class BrSampleReadyController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 样品: 选择添加 -> 预处理
    @RequestMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject add(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String insertSQL = "INSERT INTO BR_SAMPLE_READY ("
    				+ "ID" //唯一标识
    				+ ",LINK_ID" //关联入口表ID
    				+ ",TASK_ID" //关联任务单主表ID
    				+ ",TASK_MX_ID" //关联任务单明细ID
    				+ ",TASK_NO" //任务单编号
    				+ ",TASK_NAME" //任务单名称
    				+ ",TASK_TYPE" //任务单类型
    				+ ",EXEC_SAMLL" //执行小组
    				+ ",PROJECT_ID" //关联项目ID
    				+ ",PROJECT_NO" //项目编号
    				+ ",PROJECT_NAME" //项目名称
    				+ ",SAMPLE_ID" //关联样品ID
    				+ ",SAMPLE_CODE" //样品编号
    				+ ",SAMPLE_NAME" //样品名称
    				+ ",LIB_CODE" //文库名称
    				+ ",LIB_TYPE" //文库类型
    				+ ",LIB_QC_TYPE" //质控类型
    				+ ",LIB_INDEX_CODE" //index号
    				+ ",DATA_NUM" //数据量（M）
//    				+ ",Y_RESULT" //检测结果
//    				+ ",Y_RESULT_REMARK" //检测结果备注
    				+ ",Y_FLAG" //检测状态
//    				+ ",Y_MAN" //实验人员
//    				+ ",Y_REMARK" //检测备注
//    				+ ",Y_FILE1" //附件1
//    				+ ",Y_FILE2" //附件2
//    				+ ",Y_FILE3" //附件3
//    				+ ",Y_BOARD96_ID" //样品预处理关联96孔板ID
//    				+ ",Y_BOARD96_CODE" //样品预处理96孔板板号
//    				+ ",Y_BOARD96_HOLE" //样品预处理96孔板孔号
//    				+ ",Y_IS_BOARD" //样品预处理是否拼板
//    				+ ",Y_START_DATETIME" //预处理开始时间
//    				+ ",Y_CLOSING_DATETIME" //预处理结束时间
//    				+ ",Y_SAMPLE_USENUM" //样品使用量（ng）
//    				+ ",Y_CHECK_MD" //检测浓度（ng/ul）
//    				+ ",Y_SAMPLE_TJ" //样品体积(ul)
//    				+ ",Y_SAMPLE_ZL" //样品总量(ng)
//    				+ ",Y_CREATOR" //样品预处理创建人
//    				+ ",Y_CREATTIME" //样品预处理创建时间
//    				+ ",Y_LASTUPDATOR" //样品预处理最近修改人
//    				+ ",Y_LASTUPDATETIME" //样品预处理最近修改时间
//    				+ ",LOGINCOMPANY" //账套
    				+ ") "
    				+ "SELECT"
    				+ " ? AS ID" // 唯一标识
    				+ ",ID AS LINK_ID" //唯一标识
    				+ ",TASK_ID" //关联任务单主表ID
    				+ ",TASK_MX_ID" //关联任务单明细ID
    				+ ",TASK_NO" //任务单编号
    				+ ",TASK_NAME" //任务单名称
    				+ ",TASK_TYPE" //任务单类型
    				+ ",EXEC_SAMLL" //执行小组
    				+ ",PROJECT_ID" //关联项目ID
    				+ ",PROJECT_NO" //项目编号
    				+ ",PROJECT_NAME" //项目名称
    				+ ",SAMPLE_ID" //关联样品ID
    				+ ",SAMPLE_CODE" //样品编号
    				+ ",SAMPLE_NAME" //样品名称
    				+ ",LIB_CODE" //文库名称
    				+ ",LIB_TYPE" //文库类型
    				+ ",LIB_QC_TYPE" //质控类型
    				+ ",LIB_INDEX_CODE" //index号
    				+ ",DATA_NUM" //数据量（M）
//    				+ ",Y_RESULT" //检测结果
//    				+ ",Y_RESULT_REMARK" //检测结果备注
    				+ ",'进行中' AS Y_FLAG" //检测状态
//    				+ ",Y_MAN" //实验人员
//    				+ ",Y_REMARK" //检测备注
//    				+ ",Y_FILE1" //附件1
//    				+ ",Y_FILE2" //附件2
//    				+ ",Y_FILE3" //附件3
//    				+ ",Y_BOARD96_ID" //样品预处理关联96孔板ID
//    				+ ",Y_BOARD96_CODE" //样品预处理96孔板板号
//    				+ ",Y_BOARD96_HOLE" //样品预处理96孔板孔号
//    				+ ",Y_IS_BOARD" //样品预处理是否拼板
//    				+ ",Y_START_DATETIME" //预处理开始时间
//    				+ ",Y_CLOSING_DATETIME" //预处理结束时间
//    				+ ",Y_SAMPLE_USENUM" //样品使用量（ng）
//    				+ ",Y_CHECK_MD" //检测浓度（ng/ul）
//    				+ ",Y_SAMPLE_TJ" //样品体积(ul)
//    				+ ",Y_SAMPLE_ZL" //样品总量(ng)
//    				+ ",Y_CREATOR" //样品预处理创建人
//    				+ ",Y_CREATTIME" //样品预处理创建时间
//    				+ ",Y_LASTUPDATOR" //样品预处理最近修改人
//    				+ ",Y_LASTUPDATETIME" //样品预处理最近修改时间
//    				+ ",FLOW_PLAN_ID" //流程唯一标识
    				+ " FROM BR_MODUAL_READY WHERE ID=?";
    		
    		for (String MODUAL_READY_ID : ids) {
    			Object[] objs = { SysBasic.getUUID(), MODUAL_READY_ID };
    			updateJdbcTemplate.update(insertSQL, objs);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/ready/add", e);
			log.error("/berry/prod/sample/ready/add", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 样品: 预处理提交
    @RequestMapping("/commit")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commit(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_SAMPLE_READY SET Y_FLAG='已提交' WHERE ID=? AND Y_FLAG='进行中'";
    		
    		String selectSQL = "SELECT * FROM BR_SAMPLE_READY WHERE ID=?";
    		
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(updateJdbcTemplate);
    		Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_READY");
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(updateSQL, id);
    			if (i > 0) {
    				// 更新预处理结果到入口表
    				Map<String, Object> resultMap = updateJdbcTemplate.queryForMap(selectSQL, id);
    				resultMap.put("ID", resultMap.get("LINK_ID"));
    				SysBasic.filterMap(resultMap, metaMap.keySet());
    				SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_READY", resultMap);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/ready/commit", e);
			log.error("/berry/prod/sample/ready/commit", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 样品: 预处理删除
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String delSQL = "DELETE FROM BR_SAMPLE_READY WHERE ID=? AND Y_FLAG='进行中'";
    		
    		for (String id : ids) {
    			updateJdbcTemplate.update(delSQL, id);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/sample/ready/del", e);
			log.error("/berry/prod/sample/ready/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
