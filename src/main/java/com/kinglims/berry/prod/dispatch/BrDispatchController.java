package com.kinglims.berry.prod.dispatch;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Value;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@RestController
@RequestMapping("/berry/prod/dispatch/dispatch")
@Slf4j
public class BrDispatchController {
    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;

    @Autowired
    private ResponseMessageParameter responseMessageParameter;

    @Autowired
    private HttpCommonController httpCommon;

    @Value("${feign.jira-api.url}")
    private String jira_url;
    @Value("${feign.jira-api.jiraId_bp}")
    private String JiraID_BP;

    // 质检报告审核推送数据
    @RequestMapping("/pushData")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject pushData(@RequestBody JSONObject data) {
        try {
            int code = 0;
            String msg = "";
            List<String> IDS = (List<String>) data.get("IDS");
            String TS_OPERATOR = SysBasic.toTranStringByObject(data.get("TS_OPERATOR"));//当前操作人
            String main_sql = " SELECT " +
                    "btt.BUSINESS_DEVISONB, " +
                    "btt.CONTRACT_SIGN_COMPANY," +
                    "bqr.R_P_TIME,  " +
                    " bqr.ID, " +
                    " bqr.R_FILE,  " +
                    " bqr.R_P_NAME, " +
                    " btt.PROJECT_MANAGER, " +
                    " btt.CONTRACT_NO, " +
                    " btt.PROJECT_NO, " +
                    " btt.PROJECT_NAME, " +
                    " btt.LSM_KEY, " +
                    " btt.LSM_KEY_P, " +
                    " bqr.TASK_NO, " +
                    " bqr.R_STATUS, " +
                    " bqr.SAMPLE_NO, " +
                    " bqr.SAMPLE_NO_HG   " +
                    "FROM " +
                    " BIO_QC_REPORT bqr " +
                    " LEFT JOIN ( SELECT " +
                    "btt.BUSINESS_DEVISONB," +
                    "btt.CONTRACT_SIGN_COMPANY, " +
                    " btt.ID, " +
                    " btt.PROJECT_MANAGER, " +
                    " btt.CONTRACT_NO, " +
                    " btt.PROJECT_NO, " +
                    " btt.PROJECT_NAME, " +
                    " btt.LSM_KEY, " +
                    " btt.LSM_KEY_P  " +
                    "FROM " +
                    " BIO_TQ_TASK btt UNION ALL " +
                    "SELECT " +
                    "btt.BUSINESS_DEVISONB," +
                    "btt.CONTRACT_SIGN_COMPANY," +
                    " btt.ID, " +
                    " btt.PROJECT_MANAGER, " +
                    " btt.CONTRACT_NO, " +
                    " btt.PROJECT_NO, " +
                    " btt.PROJECT_NAME, " +
                    " btt.LSM_KEY, " +
                    " btt.LSM_KEY_P  " +
                    "FROM " +
                    " BIO_TASK_LIB btt  " +
                    " ) btt ON bqr.TASK_ID = btt.ID  " +
                    "WHERE bqr.ID IN (" + SysBasic.getQuestionMarkBySzie(IDS.size()) + ") and bqr.R_STATUS in ('运营待审核')";

            List<Map<String, Object>> ts_list = new ArrayList<>();  //B端BMK
            List<Map<String, Object>> bpts_list = new ArrayList<>();  //百谱
//            Map<String, Object> ts_map = new HashMap<String, Object>();
            List<Map<String, Object>> main_list = queryJdbcTemplate.queryForList(main_sql,IDS.toArray());
            String mx_sql = " SELECT " +
                    " bdrq.DJC_RESULT, " +
                    " bdrq.SAMPLE_NAME  " +
                    "FROM " +
                    " BIO_QC_REPORT_MX bqrm " +
                    " INNER JOIN ( " +
                    "SELECT " +
                    " bdrq.ID, " +
                    " bdrq.DJC_RESULT, " +
                    " bttm.SAMPLE_NAME, " +
                    " bttm.SAMPLE_CODE  " +
                    "FROM " +
                    " BIO_DNA_RNA_QC bdrq " +
                    " INNER JOIN EXE_TQQC_SHEET ets ON bdrq.EXE_TQQC_ID = ets.ID " +
                    " INNER JOIN BIO_TQ_TASK_MX bttm ON bttm.ID = bdrq.TASK_TQ_ID UNION ALL " +
                    "SELECT " +
                    " bdrq.ID, " +
                    " bdrq.DJC_RESULT, " +
                    " bttm.SAMPLE_NAME, " +
                    " bttm.SAMPLE_CODE  " +
                    " FROM " +
                    " BIO_TQ_TASK btt " +
                    " INNER JOIN BIO_TQ_TASK_MX bttm ON btt.ID = bttm.TASK_ID " +
                    " INNER JOIN BIO_DNA_RNA_QC bdrq ON bttm.ID = bdrq.TASK_TQ_ID " +
                    " INNER JOIN EXE_TQQC_SHEET ets ON bdrq.EXE_TQQC2_ID = ets.ID  " +
                    " AND btt.TASK_TYPE_LB = '核酸检测'  " +
                    " AND ets.EX_TYPE_LB != 'RNA检测' UNION ALL " +
                    "SELECT " +
                    " blqi.ID, " +
                    " bli.JK_ATAC_RESULT AS DJC_RESULT, " +
                    " bttm.SAMPLE_NAME, " +
                    " bttm.SAMPLE_CODE  " +
                    "FROM " +
                    " BIO_LIB_INFO bli " +
                    " INNER JOIN BIO_LIB_QC_INFO blqi ON bli.ID = blqi.TASK_JK_ID " +
                    " INNER JOIN BIO_TQ_TASK_MX bttm ON bttm.ID = bli.TASK_LIB_MX_ID UNION ALL " +
                    "SELECT " +
                    " bsm.ID, " +
                    " blqi.TASKZJ_ZJ_RESULT_WLWK AS DJC_RESULT, " +
                    " btlmx.SAMPLE_NAME, " +
                    " btlmx.SAMPLE_CODE  " +
                    "FROM " +
                    " BIO_SEQ_MOD bsm " +
                    " INNER JOIN BIO_LIB_INFO lib ON bsm.LIB_ID = lib.ID " +
                    " INNER JOIN BIO_TASK_LIBMX btlmx ON lib.TASK_LIB_MX_ID = btlmx.ID " +
                    " INNER JOIN BIO_TASK_LIB btl ON btl.ID = btlmx.TASK_LS_ID " +
                    " LEFT JOIN BIO_LIB_QC_INFO blqi ON lib.ID = blqi.TASK_JK_ID  " +
//                    "WHERE " +
//                    " btl.TASK_LS_TYPE = '文库库检'  " +
//                    " AND blqi.TASKZJ_ZJ_STATUS = '已审核'  " +
                    " ) bdrq ON bdrq.ID = bqrm.R_OBJ_ID  " +
                    "WHERE " +
                    " bqrm.R_ID = ?  " +
                    "ORDER BY " +
                    " bdrq.SAMPLE_CODE";
            List<String> push_IDS = new ArrayList<>();
            List<Map<String,Object>> push_List = new ArrayList<>();
            if (main_list.size() > 0) {
                for (int i = 0; i < main_list.size(); i++) {
                    Map<String, Object> ts_map = new HashMap<String, Object>();
                    int err_num = 0;
                    int corr_num = 0;
                    int num = 0;
                    Map<String, Object> main_map = main_list.get(i);
                    String ID = SysBasic.toTranStringByObject(main_map.get("ID"));
                    String R_FILE = SysBasic.toTranStringByObject(main_map.get("R_FILE"));
                    String R_FILE_2 = R_FILE.substring(0, R_FILE.indexOf(".zip"));
                    String BUSINESS_DEVISONB = SysBasic.toTranStringByObject(main_map.get("BUSINESS_DEVISONB"));//指定委外公司
                    String CONTRACT_SIGN_COMPANY = SysBasic.toTranStringByObject(main_map.get("CONTRACT_SIGN_COMPANY"));//指定委外公司
                    Date R_P_TIME = SysBasic.toTranDateByObject(main_map.get("R_P_TIME")); //报告时间

                    // 创建一个SimpleDateFormat对象，并设置日期时间格式（不包括毫秒和时区偏移）
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                    // 格式化日期时间部分
                    String formattedDateTime = sdf.format(R_P_TIME);
                    String formattedDate = formattedDateTime + ".000+0800";



                    String R_P_NAME = SysBasic.toTranStringByObject(main_map.get("R_P_NAME"));
                    String PROJECT_MANAGER = SysBasic.toTranStringByObject(main_map.get("PROJECT_MANAGER"));
                    String CONTRACT_NO = SysBasic.toTranStringByObject(main_map.get("CONTRACT_NO"));
                    String PROJECT_NO = SysBasic.toTranStringByObject(main_map.get("PROJECT_NO"));
                    String PROJECT_NAME = SysBasic.toTranStringByObject(main_map.get("PROJECT_NAME"));
                    String LSM_KEY = SysBasic.toTranStringByObject(main_map.get("LSM_KEY"));
                    String LSM_KEY_P = SysBasic.toTranStringByObject(main_map.get("LSM_KEY_P"));
                    int SAMPLE_NO = SysBasic.toTranIntegerByObject(main_map.get("SAMPLE_NO"));
                    int SAMPLE_NO_HG = SysBasic.toTranIntegerByObject(main_map.get("SAMPLE_NO_HG"));
//                    String R_FILE_PATH = "/data/app/biomarker/temp/" + R_FILE_2 + "/Web_Report";
                    String R_FILE_PATH = "/data/app/apache-tomcat-9.0.27-port8086/webapps/report/" + R_FILE_2 + "/Web_Report";
                    String TASK_NO = SysBasic.toTranStringByObject(main_map.get("TASK_NO"));
                    String R_STATUS = SysBasic.toTranStringByObject(main_map.get("R_STATUS"));
                    if(!R_STATUS.equals("运营待审核")){
                        msg+="任务单号为"+TASK_NO+"的数据在状态已发生改变，请刷新列表后再进行操作 \n";
                    }
                    String zf_sql = "select * from BIO_QC_REPORT where TASK_NO=? and R_STATUS='运营已审核' and ID <> ?";
                    List<Map<String,Object>> zf_list = queryJdbcTemplate.queryForList(zf_sql,TASK_NO,ID);
                    if(zf_list.size()>0){
                        msg+="任务单号为"+TASK_NO+"的数据在已审核有报告 \n";
                    }
                    int sample_count = 0;
                    int sample_count_hg = 0;
                    List<Map<String, Object>> mx_list = queryJdbcTemplate.queryForList(mx_sql, ID);
                    String SAMPLE_NAMES = "";
                    for (Map<String, Object> mx_map : mx_list) {
                        String SAMPLE_NAME = SysBasic.toTranStringByObject(mx_map.get("SAMPLE_NAME"));
                        String DJC_RESULT = SysBasic.toTranStringByObject(mx_map.get("DJC_RESULT"));
                        if (DJC_RESULT.equals("A") || DJC_RESULT.equals("B") || DJC_RESULT.equals("合格") || DJC_RESULT.equals("B-")) {
                            corr_num++;
                        } else {
                            err_num++;
                        }
                        if (mx_list.size() == 1) {
                            SAMPLE_NAMES = SAMPLE_NAME;
                        }
                        if (mx_list.size() >= 2) {
                            if (num < 2) {
                                SAMPLE_NAMES = SAMPLE_NAMES + SAMPLE_NAME;
                                num++;
                                if(num==1){
                                    SAMPLE_NAMES = SAMPLE_NAMES+",";
                                }
                            }

                        }
                    }
                    sample_count = mx_list.size();
                    sample_count_hg = corr_num;
                    if(SAMPLE_NO>0){
                        sample_count = SAMPLE_NO;
                        sample_count_hg = SAMPLE_NO_HG;
                    }
                    if(msg.length()==0){
                        ts_map.put("reportDate", formattedDate);
                        ts_map.put("ID", ID);
                        ts_map.put("phaseJiraNumber", LSM_KEY_P);
                        ts_map.put("lsmJiraNumber", LSM_KEY);
                        ts_map.put("sampleCount", sample_count);
                        ts_map.put("qualifiedCount", sample_count_hg);
                        ts_map.put("sampleName", SAMPLE_NAMES);
                        ts_map.put("reportPush", R_FILE_PATH);
                        ts_map.put("projectName", R_P_NAME);
                        ts_map.put("projectCode", R_FILE_2);
                        ts_map.put("manualPush","true");
//                        ts_map.put("reportDate","true");
                        if(CONTRACT_SIGN_COMPANY.equals("青岛百谱生物科技有限公司")){

                             String[] fields = {"status",JiraID_BP};
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("jiraKey", LSM_KEY_P);
                            jsonObject.put("fields", fields);
                            JSONObject inobjjson = new JSONObject();
                            inobjjson.put("url", "http://"+jira_url + "/synchronize_info/api/jira/searchByFields");
                            inobjjson.put("bodyParams", jsonObject);
                            String msJiraNumber="";
//                            try {
//                                JSONObject jsonObject1 = httpCommon.apiCommonPost(new JSONObject(inobjjson));
//                                List<JSONObject> apiData = (List<JSONObject>) jsonObject1.get("apiData");
//                                JSONObject jiraFields = (JSONObject) apiData.get(0).get("fields");
//                                 msJiraNumber = SysBasic.toTranStringByObject(jiraFields.get(JiraID_BP));
//                            }catch (Exception e){
//                                msg+="任务单号为"+TASK_NO+"的百谱JiraID查询失败 \n";
//                                continue;
//                            }
//                           ts_map.put("msJiraNumber",msJiraNumber);

                            bpts_list.add(ts_map);
                        }else {
                            ts_list.add(ts_map);
                        }
                    }else {
                        code=1;
                    }
                }
                if(msg.length()==0) {
                    if (ts_list.size() > 0) {
                        Map<String, Object> mm = new HashMap<>();
                        Date date = new Date();
                        SimpleDateFormat sdf = new SimpleDateFormat("YYYYMMddHHmmssSSS");
                        String curDate_str = sdf.format(date);
                        mm.put("batchNo", curDate_str);
                        mm.put("reportDetailList", ts_list);
                        HashMap<String, Object> paramsMap = new LinkedHashMap<>();
//                    paramsMap.put("object", ts_list);
                        paramsMap.put("bodyParams", mm);
                        paramsMap.put("url", "http://"+jira_url + "/modules/api/v1/push/huakai/pushMultiReport");
                        String str1 = paramsMap.toString();
                        log.info(str1);
                        JSONObject jsonObject1 = httpCommon.apiCommonPost(new JSONObject(paramsMap));
                        String code2 = jsonObject1.get("code").toString();
                        List reportResultList = (List) ((Map<String, Object>) jsonObject1.get("apiData")).get("reportResultList");
                        for (int j = 0; j < reportResultList.size(); j++) {
                            Map reportResultMap = (Map<String, Object>) reportResultList.get(j);
                            String ID = SysBasic.toTranStringByObject(reportResultMap.get("ID"));
                            String return_data = SysBasic.toTranStringByObject(reportResultMap.get("data"));
                            String return_message = SysBasic.toTranStringByObject(reportResultMap.get("message"));
                            if (return_data.equals("false")) {
                                msg += return_message + " \n";
                                String update_sql = "update BIO_QC_REPORT set TS_STATUS='推送失败',TS_FAIL_REASON='" + return_message + "' where ID =?";
                                int u = updateJdbcTemplate.update(update_sql, ID);
                                if (u > 0) {
                                    code = 1;
                                } else {
                                    SysBasic.rollBack();
                                    return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
                                }
                            } else {
                                push_IDS.add(ID);
                            }
                        }
                        if (push_IDS.size() > 0) {
                            String update_sql = "update BIO_QC_REPORT set TS_STATUS='推送中',R_STATUS='运营已审核',TS_OPERATOR='" + TS_OPERATOR + "',TS_DATE=SYSDATE,TS_MODE='手动' where ID in(" + SysBasic.getQuestionMarkBySzie(push_IDS.size()) + ")";
                            int u = queryJdbcTemplate.update(update_sql, push_IDS.toArray());
                            if (u > 0) {
                                code = 1;
                            } else {
                                code = -1;
                            }
                        } else {
                            return new CurrResponseResolve(1).put(responseMessageParameter).put("msg", msg).getData();
                        }
                    }
                    if (bpts_list.size() > 0) {
                        Map<String, Object> mm = new HashMap<>();
                        Date date = new Date();
                        SimpleDateFormat sdf = new SimpleDateFormat("YYYYMMddHHmmssSSS");
                        String curDate_str = sdf.format(date);
                        mm.put("batchNo", curDate_str);
                        mm.put("reportDetailList", bpts_list);
                        HashMap<String, Object> paramsMap = new LinkedHashMap<>();
//                    paramsMap.put("object", bpts_list);
                        paramsMap.put("bodyParams", mm);
                        paramsMap.put("url", "http://api.bestms.cn/modules/api/v1/push/huakai/pushMultiReport");
                        String str1 = paramsMap.toString();
                        log.info(str1);
                        JSONObject jsonObject1 = httpCommon.apiCommonPost(new JSONObject(paramsMap));
                        String code2 = jsonObject1.get("code").toString();
                        List reportResultList = (List) ((Map<String, Object>) jsonObject1.get("apiData")).get("reportResultList");
                        for (int j = 0; j < reportResultList.size(); j++) {
                            Map reportResultMap = (Map<String, Object>) reportResultList.get(j);
                            String ID = SysBasic.toTranStringByObject(reportResultMap.get("ID"));
                            String return_data = SysBasic.toTranStringByObject(reportResultMap.get("data"));
                            String return_message = SysBasic.toTranStringByObject(reportResultMap.get("message"));
                            if (return_data.equals("false")) {
                                msg += return_message + " \n";
                                String update_sql = "update BIO_QC_REPORT set TS_STATUS='推送失败',TS_FAIL_REASON='" + return_message + "' where ID =?";
                                int u = updateJdbcTemplate.update(update_sql, ID);
                                if (u > 0) {
                                    code = 1;
                                } else {
                                    SysBasic.rollBack();
                                    return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
                                }
                            } else {
                                push_IDS.add(ID);
                            }
                        }
                        if (push_IDS.size() > 0) {
                            String update_sql = "update BIO_QC_REPORT set TS_STATUS='推送中',R_STATUS='运营已审核',TS_OPERATOR='" + TS_OPERATOR + "',TS_DATE=SYSDATE,TS_MODE='手动' where ID in(" + SysBasic.getQuestionMarkBySzie(push_IDS.size()) + ")";
                            int u = queryJdbcTemplate.update(update_sql, push_IDS.toArray());
                            if (u > 0) {
                                code = 1;
                            } else {
                                code = -1;
                            }
                        } else {
                            return new CurrResponseResolve(1).put(responseMessageParameter).put("msg", msg).getData();
                        }
                    }

                }

            }
            return new CurrResponseResolve(code).put(responseMessageParameter).put("msg",msg).getData();
        } catch (Exception e) {
            log.info("/berry/prod/dispatch/dispatch/pushData", e);
            log.error("/berry/prod/dispatch/dispatch/pushData", e);

            return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
        }

    }

    @RequestMapping("/cancel")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject cancelTSData(@RequestBody JSONObject data) {
        try {
            int code=0;
            List<String> IDS = (List<String>)data.get("IDS");
            String sql = "select * from BIO_QC_REPORT where ID in ("+SysBasic.getQuestionMarkBySzie(IDS.size())+")";
            String msg = "";
            List<Map<String,Object>> list = queryJdbcTemplate.queryForList(sql,IDS.toArray());
            for(int i=0;i<list.size();i++){
                Map<String,Object> map = list.get(i);
                String R_P_NAME = SysBasic.toTranStringByObject(map.get("R_P_NAME"));
                String TS_STATUS = SysBasic.toTranStringByObject(map.get("TS_STATUS"));
                if(TS_STATUS.equals("推送中")){
                    code=1;
                    msg += "报告名称为"+R_P_NAME+"的数据状态为推送中，无法作废 \n";
                }
            }
            if(msg.length()==0){
                String sql2 = "update BIO_QC_REPORT set R_STATUS='已作废' where R_STATUS='运营已审核' and ID in("+SysBasic.getQuestionMarkBySzie(IDS.size())+")";
                int u=updateJdbcTemplate.update(sql2,IDS.toArray());
                if(u>0){
                    code=1;
                }else {
                    code=-1;
                }
            }
            return new CurrResponseResolve(code).put(responseMessageParameter).put("msg",msg).getData();
        }catch (Exception e){
            log.info("/berry/prod/dispatch/dispatch/cancelTSData", e);
            log.error("/berry/prod/dispatch/dispatch/cancelTSData", e);

            return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
        }
    }

    @RequestMapping("/comfirmNoPush")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject comfirmNoPush(@RequestBody JSONObject data) {
        try {
            int code=0;
            List<String> IDS = (List<String>)data.get("IDS");
            String curUserName = SysBasic.toTranStringByObject(data.get("curUserName"));
            String sql1 = "select ID from BIO_QC_REPORT where ID in ("+SysBasic.getQuestionMarkBySzie(IDS.size())+")";
            List<Map<String,Object>> list1 = queryJdbcTemplate.queryForList(sql1,IDS.toArray());
            List<String> OT_IDS = new ArrayList<>();
            if(list1.size()>0){
                for(Map<String,Object> map1 : list1){
                    String ID = SysBasic.toTranStringByObject(map1.get("ID"));
                    OT_IDS.add(ID);
                }
            }
            String sql2 = "select * from BIO_TQ_TASK where ID in("+SysBasic.getQuestionMarkBySzie(IDS.size())+")";
            List<Map<String,Object>> list2 = queryJdbcTemplate.queryForList(sql2,IDS.toArray());
            List<String> WW_IDS = new ArrayList<>();
            if(list2.size()>0){
                for(Map<String,Object> map2 : list2){
                    String ID = SysBasic.toTranStringByObject(map2.get("ID"));
                    WW_IDS.add(ID);
                }
            }
            if(OT_IDS.size()>0){
                String u_sql1 = "update BIO_QC_REPORT set R_STATUS='运营已审核',TS_OPERATOR='"+curUserName+"' where ID in("+SysBasic.getQuestionMarkBySzie(OT_IDS.size())+")";
                int u = updateJdbcTemplate.update(u_sql1,OT_IDS.toArray());
                if(u>0){
                    code = 1;
                }else {
                    code = -1;
                }
            }
            if(WW_IDS.size()>0){
                String u_sql2 = "update BIO_TQ_TASK set DD_TASK_STATUS ='运营已审核',TS_OPERATOR='"+curUserName+"' where ID in("+SysBasic.getQuestionMarkBySzie(WW_IDS.size())+")";
                int u2 = updateJdbcTemplate.update(u_sql2,WW_IDS.toArray());
                if(u2>0){
                    code = 1;
                }else {
                    code = -1;
                }
            }

            return new CurrResponseResolve(code).put(responseMessageParameter).getData();
        }catch (Exception e){
            log.info("/berry/prod/dispatch/dispatch/comfirmNoPush", e);
            log.error("/berry/prod/dispatch/dispatch/comfirmNoPush", e);

            return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
        }
    }

    @RequestMapping("/withdraw")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject withdraw(@RequestBody JSONObject data) {
        try {
            int code=0;
            String msg = "";
            List<String> IDS = (List<String>)data.get("IDS");
            String sql1 = "select ID from BIO_QC_REPORT where ID in ("+SysBasic.getQuestionMarkBySzie(IDS.size())+")";
            List<Map<String,Object>> list1 = queryJdbcTemplate.queryForList(sql1,IDS.toArray());
            List<String> OT_IDS = new ArrayList<>();
            if(list1.size()>0){
                for(Map<String,Object> map1 : list1){
                    String ID = SysBasic.toTranStringByObject(map1.get("ID"));
                    String TASK_NO = SysBasic.toTranStringByObject(map1.get("TASK_NO"));
                    String TS_STATUS = SysBasic.toTranStringByObject(map1.get("TS_STATUS"));
                    if(TS_STATUS.equals("推送中")){
                        msg += "任务单编号为"+TASK_NO+"的数据推送状态为推送中，不可撤回\r\n";
                    }
                    OT_IDS.add(ID);
                }
            }
            String sql2 = "select * from BIO_TQ_TASK where ID in("+SysBasic.getQuestionMarkBySzie(IDS.size())+")";
            List<Map<String,Object>> list2 = queryJdbcTemplate.queryForList(sql2,IDS.toArray());
            List<String> WW_IDS = new ArrayList<>();
            if(list2.size()>0){
                for(Map<String,Object> map2 : list2){
                    String ID = SysBasic.toTranStringByObject(map2.get("ID"));
                    String TASK_NO = SysBasic.toTranStringByObject(map2.get("TASK_NO"));
                    String TS_STATUS = SysBasic.toTranStringByObject(map2.get("TS_STATUS"));
                    if(TS_STATUS.equals("推送中")){
                        msg += "任务单编号为"+TASK_NO+"的数据推送状态为推送中，不可撤回\r\n";
                    }
                    WW_IDS.add(ID);
                }
            }
            if(msg.length()>0){
                return new CurrResponseResolve(1).put(responseMessageParameter).put("msg",msg).getData();
            }else {
                if(OT_IDS.size()>0){
                    String u_sql1 = "update BIO_QC_REPORT set R_STATUS='运营待审核' where ID in("+SysBasic.getQuestionMarkBySzie(OT_IDS.size())+")";
                    int u = updateJdbcTemplate.update(u_sql1,OT_IDS.toArray());
                    if(u>0){
                        code = 1;
                    }else {
                        code = -1;
                    }
                }
                if(WW_IDS.size()>0){
                    String u_sql2 = "update BIO_TQ_TASK set DD_TASK_STATUS ='运营待审核' where ID in("+SysBasic.getQuestionMarkBySzie(WW_IDS.size())+")";
                    int u2 = updateJdbcTemplate.update(u_sql2,WW_IDS.toArray());
                    if(u2>0){
                        code = 1;
                    }else {
                        code = -1;
                    }
                }
            }


            return new CurrResponseResolve(code).put(responseMessageParameter).getData();
        }catch (Exception e){
            log.info("/berry/prod/dispatch/dispatch/withdraw", e);
            log.error("/berry/prod/dispatch/dispatch/withdraw", e);

            return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
        }
    }

    //提取质检报告审核前验证
    @RequestMapping("/tqReportSHVerif")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject tqReportSHVerif(@RequestBody JSONObject data) {
        try {
            String msg="";
            List<String> IDS = (List<String>)data.get("ids");
            String sql1 = "select * from BIO_QC_REPORT where ID in("+SysBasic.getQuestionMarkBySzie(IDS.size())+")";
            String zf_sql = "select * from BIO_QC_REPORT where TASK_NO=? and R_P_M_FILE=? and R_STATUS='运营已审核'";
            List<Map<String,Object>> list1 = queryJdbcTemplate.queryForList(sql1,IDS.toArray());
            for(Map<String,Object> map1:list1){
                String ID = SysBasic.toTranStringByObject(map1.get("ID"));
                String TASK_NO = SysBasic.toTranStringByObject(map1.get("TASK_NO"));
                String R_P_M_FILE = SysBasic.toTranStringByObject(map1.get("R_P_M_FILE"));
                List<Map<String,Object>> list = queryJdbcTemplate.queryForList(zf_sql,TASK_NO,R_P_M_FILE);
                if(list.size()>0){
                    msg+="任务单号为"+TASK_NO+"且模板为"+R_P_M_FILE+"在运营已审核已存在数据，请沟通运营作废该任务单报告后重新生成\r\n";
                }
            }
            return new CurrResponseResolve(1).put(responseMessageParameter).put("msg",msg).getData();

        }catch (Exception e){
            log.info("/berry/prod/dispatch/dispatch/tqReportSHVerif", e);
            log.error("/berry/prod/dispatch/dispatch/tqReportSHVerif", e);

            return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
        }
    }

}
