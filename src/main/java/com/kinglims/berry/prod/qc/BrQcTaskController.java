package com.kinglims.berry.prod.qc;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.security.entity.User;
import com.kinglims.framework.common.system.workflow.handler.logic.Modulars;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowMessage;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowUtils;
import com.kinglims.framework.modular.bg.handler.ReplaceWordParams;
import com.kinglims.framework.modular.bg.handler.WordUtils;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;
import com.kinglims.framework.utils.system.SystemStaticParameter;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/qc")
@Slf4j
public class BrQcTaskController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    @Autowired
    private WorkflowUtils workflowUtils;
    
    
    @Value("${system.config.upload-paths.report-template-path}")
    private String reportTemplatePath;
    // RNA DNA 检测任务: 添加
    @RequestMapping("/qcTask/add")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject qcTaskAdd(@RequestBody JSONObject data) {
    	
    	try {
    		String QC_TYPE = data.getString("QC_TYPE");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String insertSQL = "INSERT INTO BR_DNA_RNA_QC ("
								+ "ID" // 唯一标识
								+ ",QC_TYPE" // 质检类型
								+ ",MODUAL_QC_ID" // QC入口表ID
								+ ",TQ_MX_RESULT_ID" // 关联提取后ID
								+ ",DNA_RNA_CODE" // DNA/RNA编号
								+ ",SAMPLE_ID" // 样本ID
								+ ",SAMPLE_CODE" // 样品编号
								+ ",SAMPLE_NAME" // 样品名称
								+ ",SAMPLE_TYPE" // 样品类型
								+ ",TASK_FROM" // 任务单来源
								+ ",TASK_ID" // 任务单ID
								+ ",TASK_NO" // 任务单号
								+ ",PROJECT_ID" // 关联项目ID
								+ ",PROJECT_NO" // 项目编号
								+ ",PROJECT_NAME" // 项目名称
								+ ",CONTRACT_ID" // 关联合同ID
								+ ",CONTRACT_NO" // 合同编号
								+ ",CONTRACT_NAME" // 合同名称
								+ ",QC_FLAG" // 检测状态
								+ ",SAMPLE_SEND_NO" // 送检单号
//								+ ",CREATOR" // 创建人
//								+ ",CREATTIME" // 创建时间
//								+ ",LASTUPDATOR" // 最近修改人
//								+ ",LASTUPDATETIME" // 最近修改时间
//								+ ",LOGINCOMPANY" // 账套
								+ ") "
								+ "SELECT"
			    				+ " ? AS ID" // 唯一标识
			    				+ ",? AS QC_TYPE" // 关联提取主单
								+ ",ID AS MODUAL_QC_ID" // 唯一标识
								+ ",TQ_MX_RESULT_ID" // 关联提取后ID
								+ ",DNA_RNA_CODE" // DNA/RNA编号
								+ ",SAMPLE_ID" // 样本ID
								+ ",SAMPLE_CODE" // 样品编号
								+ ",SAMPLE_NAME" // 样品名称
								+ ",SAMPLE_TYPE" // 样品类型
								+ ",TASK_FROM" // 任务单来源
								+ ",TASK_ID" // 任务单ID
								+ ",TASK_NO" // 任务单号
								+ ",PROJECT_ID" // 关联项目ID
								+ ",PROJECT_NO" // 项目编号
								+ ",PROJECT_NAME" // 项目名称
								+ ",CONTRACT_ID" // 关联合同ID
								+ ",CONTRACT_NO" // 合同编号
								+ ",CONTRACT_NAME" // 合同名称
								+ ",'检测中' AS QC_FLAG" // 检测状态
								+ ",SAMPLE_SEND_NO" // 检测状态
								+ " FROM BR_MODUAL_QC WHERE ID=? AND DNA_RNA_QC_ID IS NULL";
    		
    		String updateSQL = "UPDATE BR_MODUAL_QC SET DNA_RNA_QC_ID=?,QC_TYPE=?,OBJ_FLAG='归结' WHERE ID=? AND DNA_RNA_QC_ID IS NULL";
    		for (String MODUAL_QC : ids) {
    			
    			String RNA_QC_ID = SysBasic.getUUID();
    			
    			Object[] objs = { RNA_QC_ID, QC_TYPE, MODUAL_QC };
    			updateJdbcTemplate.update(insertSQL, objs);
    			
    			Object[] objs2 = { RNA_QC_ID, QC_TYPE, MODUAL_QC };
    			updateJdbcTemplate.update(updateSQL, objs2);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/add", e);
			log.error("/berry/prod/qc/qcTask/add", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // RNA DNA 检测任务: 批量修改
    @RequestMapping("/qcTask/editBatch")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject editBatch(@RequestBody JSONObject data) {
    	try {
    		String IDS = data.getString("IDS");
    		String[] idArray = IDS.split(",");
    		
    		// 处理日期格式
    		if (data.containsKey("QC_DATE")) {//检测日期
    			data.put("QC_DATE", data.getDate("QC_DATE") );
    		}
    		if (data.containsKey("QC_RECEIVE_DATE")) {//接收日期
    			data.put("QC_RECEIVE_DATE", data.getDate("QC_RECEIVE_DATE") );
    		}
    		
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(updateJdbcTemplate);
    		Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName("BR_DNA_RNA_QC");
    		
    		SysBasic.filterMap(data, metaMap.keySet());//过滤表不存在的字段
    		
    		for (String id : idArray) {
    			data.put("ID", id);
    			SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_DNA_RNA_QC", data);
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/editBatch", e);
			log.error("/berry/prod/qc/qcTask/editBatch", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // RNA DNA 检测任务: 删除
    @RequestMapping("/qcTask/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject qcTaskDel(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String deleteSQL = "DELETE FROM BR_DNA_RNA_QC WHERE ID=? AND QC_FLAG IN ('检测中', '退回')";
    		
    		String updateSQL = "UPDATE BR_MODUAL_QC SET DNA_RNA_QC_ID=NULL,QC_TYPE=NULL,OBJ_FLAG=NULL"
//    				+ ",QC_TYPE=NULL" // 检测类型
//    				+ ",QC_DATE=NULL" // 检测日期
//    				+ ",QC_SHANG_L=NULL" // 上样量(ul)
//    				+ ",QC_VOLUME=NULL" // 样品体积（ul）
//    				+ ",QC_XS_BS=NULL" // 稀释倍数
//    				+ ",QC_CONCENTRATION=NULL" // 检测浓度
//    				+ ",QC_SAMPLE_TOTAL=NULL" // 样品总量（ng）
//    				+ ",QC_OD260_280=NULL" // OD260/280
//    				+ ",QC_OD260_230=NULL" // OD260/230
//    				+ ",QC_RIN=NULL" // RIN值
//    				+ ",QC_RESULT=NULL" // 检测结果
//    				+ ",QC_RESULT_REMARK=NULL" // 检测结果备注
//    				+ ",QC_FLAG=NULL" // 检测状态
//    				+ ",QC_MAN=NULL" // 检测员
//    				+ ",QC_ASSIGNOR=NULL" // 指派人
//    				+ ",QC_ASSIGNOR_TIME=NULL" // 指派时间
//    				+ ",QC_REMARK=NULL" // 检测备注
//    				+ ",QC_RECEIVE_DATE=NULL" // 接收日期
//    				+ ",QC_BOARD_CODE=NULL" // 自动化板号
//    				+ ",QC_BOARD_HOLE=NULL" // 自动化孔号
//    				+ ",QC_REPROT_DR_IMG=NULL" // 普通电脉图附件
//    				+ ",QC_REPROTR_2100_IMG=NULL" // 安捷伦2100检测结果附件
//    				+ ",QC_REPROT_MQ_IMG=NULL" // 酶切电脉图附件
//    				+ ",QC_REPROT_PCR1_IMG=NULL" // 第一次扩增产物电脉图附件
//    				+ ",QC_REPROT_PCR2_IMG=NULL" // 第二次扩增产物电脉图附件
//    				+ ",QC_REPROT_FLAG=NULL" // 报告状态
//    				+ ",QC_REPROT_FQMAN=NULL" // 报告发起人
//    				+ ",QC_REPROT_FQTIME=NULL" // 报告发起时间
//    				+ ",QC_REPROT_QFMAN=NULL" // 报告签发人
//    				+ ",QC_REPROT_QFTIME=NULL" // 报告签发时间
    				+ " WHERE DNA_RNA_QC_ID=?";
    		for (String DNA_RNA_QC_ID : ids) {
    			updateJdbcTemplate.update(deleteSQL, DNA_RNA_QC_ID);
    			
    			updateJdbcTemplate.update(updateSQL, DNA_RNA_QC_ID);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/del", e);
			log.error("/berry/prod/qc/qcTask/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // RNA DNA 检测任务: 提交到待生成报告
    @RequestMapping("/qcTask/submitToDSC")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject qcTaskSubmitToDSC(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
            String updateSQL="UPDATE BR_DNA_RNA_QC SET QC_FLAG='待生成报告'"
            		+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND QC_FLAG IN ('检测中', '退回')";
            
            updateJdbcTemplate.update(updateSQL, ids.toArray());
            
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/qcTaskSubmitToDSC", e);
			log.error("/berry/prod/qc/qcTask/qcTaskSubmitToDSC", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // RNA DNA 检测任务: 生成报告
    @RequestMapping("/qcTask/submitToYSC")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject qcTaskSubmitToYSC(@RequestBody JSONObject data, @RequestHeader(name="Authorization")String token) {
    	
    	try {
    		User user = SysBasic.getUserByTokenRedis(token);
    		Date nowDate = new Date();
    		
    		List<String> ids = (List<String>) data.get("ids");
    		
    		// 验证状态
    		String selectSQL_checkFLAG = "SELECT COUNT(1) FROM BR_DNA_RNA_QC WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND QC_FLAG<>'待生成报告'";
    		int checkFLAG = queryJdbcTemplate.queryForObject(selectSQL_checkFLAG, Integer.class, ids.toArray());
    		if (checkFLAG>0) {//选择QC
    			return new CurrResponseResolve(1).put("msg", "选择的数据存在不是“待生成报告”的检测任务，请刷新列表后重新选择").put(responseMessageParameter).getData();
    		}
    		
    		// 获取所有QC信息
    		String selectSQL_qcinfo = "SELECT * FROM BR_DNA_RNA_QC WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND QC_FLAG='待生成报告'";
    		List<Map<String, Object>> qcinfoList = queryJdbcTemplate.queryForList(selectSQL_qcinfo, ids.toArray());
    		if (qcinfoList==null || qcinfoList.size()==0) {
    			return new CurrResponseResolve(1).put("msg", "选择的数据存在不是“待生成报告”的检测任务，请刷新列表后重新选择").put(responseMessageParameter).getData();
    		}
    		
    		Map<String, Object> qcinfo_0 = qcinfoList.get(0);
    		String QC_TYPE = qcinfo_0.get("QC_TYPE")+"";//质检类型
    		String CONTRACT_ID = qcinfo_0.get("CONTRACT_ID")+"";//关联合同ID
    		String CONTRACT_NO = qcinfo_0.get("CONTRACT_NO")+"";//合同编号
    		String CONTRACT_NAME = qcinfo_0.get("CONTRACT_NAME")+"";//合同名称
    		String SIGN_BUS_MAN_ID = "";//签单业务员ID
    		String SIGN_BUS_MAN = "";//签单业务员
    		String CONTRACT_MANAGER_ID = "";//合同管理员ID
    		String CONTRACT_MANAGER_NAME = "";//合同管理员姓名
    		double CONTRACT_AMOUNT = 0;//合同金额
    		double CONTRACT_AMOUNT_AR = 0;//合同应收款
    		
    		String SAMPLE_SEND_NO = "";//送检单号
    		for (Map<String, Object> qcinfo : qcinfoList) {
    			String SAMPLE_SEND_NO_1 = qcinfo.get("SAMPLE_SEND_NO")+"";
    			if ( SAMPLE_SEND_NO.indexOf(SAMPLE_SEND_NO_1) < 0 ) {
    				SAMPLE_SEND_NO += SAMPLE_SEND_NO.length()>0 ? (";"+SAMPLE_SEND_NO_1) : SAMPLE_SEND_NO_1;
    			}
    		}
    		
    		// 查询合同信息
    		String selectSQL_CONTRACT = "SELECT C.* FROM BR_CONTRACT C WHERE C.ID=?";
    		List<Map<String, Object>> contractinfoList = queryJdbcTemplate.queryForList(selectSQL_CONTRACT, CONTRACT_ID);
    		if (contractinfoList!=null && contractinfoList.size()>0) {
    			Map<String, Object> contractinfo_0 = contractinfoList.get(0);
    			SIGN_BUS_MAN_ID = contractinfo_0.get("SIGN_BUS_MAN_ID")+"";//签单业务员ID
        		SIGN_BUS_MAN = contractinfo_0.get("SIGN_BUS_MAN")+"";//签单业务员
        		CONTRACT_MANAGER_ID = contractinfo_0.get("CONTRACT_MANAGER_ID")+"";//合同管理员ID
        		CONTRACT_MANAGER_NAME = contractinfo_0.get("CONTRACT_MANAGER_NAME")+"";//合同管理员姓名
        		
        		BigDecimal CONTRACT_AMOUNT_BD = (BigDecimal) contractinfo_0.get("CONTRACT_AMOUNT");
        		CONTRACT_AMOUNT = CONTRACT_AMOUNT_BD==null? 0 : CONTRACT_AMOUNT_BD.doubleValue();//合同金额
        		CONTRACT_AMOUNT_AR = 0;//合同应收款 -- 待确认计算逻辑 -- 计算 (或) 实时核算？？？
    		}
    		
    		// 新增报告表
    		String QC_REPROT_ID = SysBasic.getUUID();
    		Map<String, Object> BR_DNA_RNA_QC_REPROT = new HashMap<String, Object>();
    		BR_DNA_RNA_QC_REPROT.put("ID", QC_REPROT_ID);//ID
    		BR_DNA_RNA_QC_REPROT.put("QC_TYPE", QC_TYPE);//质检类型
    		BR_DNA_RNA_QC_REPROT.put("SAMPLE_SEND_NO", SAMPLE_SEND_NO);//送检单号
    		BR_DNA_RNA_QC_REPROT.put("CONTRACT_ID", CONTRACT_ID);//关联合同ID
    		BR_DNA_RNA_QC_REPROT.put("CONTRACT_NO", CONTRACT_NO);//合同编号
    		BR_DNA_RNA_QC_REPROT.put("CONTRACT_NAME", CONTRACT_NAME);//合同名称
    		BR_DNA_RNA_QC_REPROT.put("SIGN_BUS_MAN_ID", SIGN_BUS_MAN_ID);//签单业务员ID
    		BR_DNA_RNA_QC_REPROT.put("SIGN_BUS_MAN", SIGN_BUS_MAN);//签单业务员
    		BR_DNA_RNA_QC_REPROT.put("CONTRACT_MANAGER_ID", CONTRACT_MANAGER_ID);//合同管理员ID
    		BR_DNA_RNA_QC_REPROT.put("CONTRACT_MANAGER_NAME", CONTRACT_MANAGER_NAME);//合同管理员姓名
    		BR_DNA_RNA_QC_REPROT.put("CONTRACT_AMOUNT", CONTRACT_AMOUNT);//合同金额
    		BR_DNA_RNA_QC_REPROT.put("CONTRACT_AMOUNT_AR", CONTRACT_AMOUNT_AR);//合同应收款
    		// BR_DNA_RNA_QC_REPROT.put("QC_REPROT_FILE", QC_REPROT_FILE);//报告附件
    		BR_DNA_RNA_QC_REPROT.put("QC_REPROT_FLAG", "已生成报告");//检测报告状态
    		BR_DNA_RNA_QC_REPROT.put("QC_CREATOR", user.getID());//创建人
    		BR_DNA_RNA_QC_REPROT.put("QC_CREATTIME", nowDate);//创建时间
    		BR_DNA_RNA_QC_REPROT.put("QC_LASTUPDATOR", user.getID());//最近修改人
    		BR_DNA_RNA_QC_REPROT.put("QC_LASTUPDATETIME", nowDate);//最近修改时间
    		// BR_DNA_RNA_QC_REPROT.put("LOGINCOMPANY", "");//账套
    		SysBasic.insertDataByTableMap(updateJdbcTemplate, "BR_DNA_RNA_QC_REPROT", BR_DNA_RNA_QC_REPROT);
    		
    		// 更新状态
            String updateSQL_QC="UPDATE BR_DNA_RNA_QC SET QC_FLAG='已生成报告', QC_REPROT_FILE='"+QC_REPROT_ID+"'"
            		+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND QC_FLAG='待生成报告'";
            updateJdbcTemplate.update(updateSQL_QC, ids.toArray());
            
	    	return new CurrResponseResolve(1).put("ID", QC_REPROT_ID).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/qcTaskSubmitToYSC", e);
			log.error("/berry/prod/qc/qcTask/qcTaskSubmitToYSC", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 批量生成
    @RequestMapping("/qcTask/qcReprot")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject qcReprot(@RequestBody JSONObject data) {
    	try {
    		List<String> reprotIds = (List<String>) data.get("ids");
    		
    		for (String reprotId : reprotIds) {
    			generateReprotFile(reprotId);
    		}
    		
    		return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    } catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/qcTaskSubmitToYSC", e);
			log.error("/berry/prod/qc/qcTask/qcTaskSubmitToYSC", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    private void generateReprotFile(String reprotId) {
    	// 生成
    	String QC_REPROT_FILE="";
    	
    	// 获取所有QC信息
		String selectSQL_qcinfo = "SELECT * FROM BR_DNA_RNA_QC WHERE QC_REPROT_FILE=?";
		List<Map<String, Object>> qcinfoList = queryJdbcTemplate.queryForList(selectSQL_qcinfo, reprotId);
		
		String TEMP_NAME="RNA";
		String TEMP_FILE="测试RNA20200724094719985.docx";
		
		 Map<String,Map<String,String>> paramsTableMap = new LinkedHashMap<String,Map<String,String>>();
         Map<String,String> paramsMap = new LinkedHashMap<String,String>();//主单参数
 		 //Map<String,Map<String,String>> picturesMap = new LinkedHashMap<String,Map<String,String>>();//图片参数
	     Map<String,Map<String,String>> fontParamsMap = new LinkedHashMap<String,Map<String,String>>();//主单字体
	     Map<String,Map<String,Map<String,String>>> fontParamsTableMap = new LinkedHashMap<String,Map<String,Map<String,String>>>();
    	
	        Map<String, Object> qcinfo_0 = qcinfoList.get(0);
	        String CONTRACT_NAME=qcinfo_0.get("CONTRACT_NAME")+"";
	         //项目及样品信息
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
			paramsMap.put("[项目名称]", qcinfo_0.get("CONTRACT_NAME")+"");
			paramsMap.put("[项目编号]", qcinfo_0.get("CONTRACT_NO")+"");
			//paramsMap.put("[样品状态]", qcinfo_0.get("PROJECT_NO")+"");
			String QC_RECEIVE_DATE = qcinfo_0.get("QC_RECEIVE_DATE")==null?"":sdf.format(qcinfo_0.get("QC_RECEIVE_DATE"));
			paramsMap.put("[收样日期]",QC_RECEIVE_DATE);
			paramsMap.put("[出具人]", qcinfo_0.get("QC_REPROT_FQMAN")+"");
			paramsMap.put("[审核人]", qcinfo_0.get("QC_REPROT_QFMAN")+"");
			
			// ------------------------------------ 以下是检测结果
			 String enterLetter="↵";
			  Map<String,String> contractCostTitleMap1=new LinkedHashMap<String,String>();
			  contractCostTitleMap1.put("生产编号","DNA_RNA_CODE");
		      contractCostTitleMap1.put("样品名称"+enterLetter,"SAMPLE_NAME");
		      Map<String,String> TextResultTableMap1=new LinkedHashMap<String,String>();
		       int i=0;
		       for(Map<String, Object> qcinfo:qcinfoList){
		           i++;
		           for(String dual : contractCostTitleMap1.keySet()){
		        	   TextResultTableMap1.put(i+"-"+dual,SysBasic.toTranStringByObject(qcinfo.get(contractCostTitleMap1.get(dual))));
		           }
		       }
		       
		       WordUtils.cencleLastEnter(TextResultTableMap1,enterLetter);
		       paramsTableMap.put("[检测结果]", TextResultTableMap1);
		       
		       // 合同模板 ------------------------------------
	   			
		       Map<String,String> maps = new LinkedHashMap<String, String>();
		    	// 合同模板 ------------------------------------
		    	maps.put("[项目编号]", "内容");
		    	maps.put("[项目名称]", "内容");
		    	maps.put("[收样日期]", "内容");
		    	//maps.put("[完成日期]", "内容");
		    	maps.put("[出具人]", "内容");
		    	maps.put("[审核人]", "内容");
		    	//maps.put("[样本数量]", "内容");
		    	//maps.put("[样本状态]", "内容");   
		    	
		    	
		    	maps.put("[检测结果]", "列表");
		    	//maps.put("[结果汇总]", "列表");
				// 合同模板 ------------------------------------
                ReplaceWordParams replaceWordParams = new ReplaceWordParams();//模板对象
		    	
		    	//replaceWordParams.setPicturesMap(picturesMap);//图片
                replaceWordParams.setMaps(maps);//渲染方式
		    	replaceWordParams.setFontParamsMap(fontParamsMap);//字体
		    	replaceWordParams.setFontParamsTableMap(fontParamsTableMap);//表格字体
		    	replaceWordParams.setParamsMap(paramsMap);//主单参数
		    	replaceWordParams.setParamsTableMap(paramsTableMap);//表格
		    	replaceWordParams.setNotDetectedTableMap(new LinkedHashMap<String,String>());//未检出情况, 暂时不需要用到, 但需要传参
		    	//replaceWordParams.setMaps(maps);//渲染方式
		    	
		    	QC_REPROT_FILE =TEMP_NAME+"-"+CONTRACT_NAME+"-"+SysBasic.getNowTime(SysBasic.SSSDateTimeFormat);//报告文件名
		    	QC_REPROT_FILE = QC_REPROT_FILE+".docx";//报告文件名
		    	String returnValue = WordUtils.replaceWord(replaceWordParams,
    	        		reportTemplatePath + SysBasic.toTranStringByObject(TEMP_FILE), 
    	        		SystemStaticParameter.systemTempFilePath, QC_REPROT_FILE
	        		);
		    	String msg="";
		    	if("fail".equals(returnValue)) {
		    		  msg = "生成失败";
		    	}else{
		    		msg = "生成成功";
		    	}
    	// 更新报告文件
        String updateSQL_QC_REPROT="UPDATE BR_DNA_RNA_QC_REPROT SET QC_REPROT_FILE=? WHERE ID=?";
        updateJdbcTemplate.update(updateSQL_QC_REPROT, QC_REPROT_FILE, reprotId);
    }
    
    // RNA DNA 检测任务: 删除报告
    @RequestMapping("/qcTask/qcReprotDelete")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject qcTaskQcReprotDelete(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
            String deleteSQL_QC_REPROT = "DELETE FROM BR_DNA_RNA_QC_REPROT WHERE ID=? AND QC_REPROT_FLAG IN ('已生成报告','项目退回','客户退回')";
    		
            String updateSQL_QC = "UPDATE BR_DNA_RNA_QC SET QC_FLAG='待生成报告', QC_REPROT_FILE=NULL WHERE QC_REPROT_FILE=?";
            
            for (String id : ids) {
                int i = updateJdbcTemplate.update(deleteSQL_QC_REPROT, id);
                if (i > 0) {
                	updateJdbcTemplate.update(updateSQL_QC, id);
                }
            }
            
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/qcTaskQcReprotDelete", e);
			log.error("/berry/prod/qc/qcTask/qcTaskQcReprotDelete", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // RNA DNA 检测任务: 提交到待发送
    @RequestMapping("/qcTask/submitToDFS")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject qcTaskSubmitToDFS(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
            String updateSQL_QC_REPROT = "UPDATE BR_DNA_RNA_QC_REPROT SET QC_REPROT_FLAG='待发送'"
            						   + " WHERE ID=? AND QC_REPROT_FLAG='已生成报告'";
    		
            String updateSQL_QC = "UPDATE BR_DNA_RNA_QC SET QC_FLAG='待发送' WHERE QC_REPROT_FILE=?";
            
            for (String id : ids) {
                int i = updateJdbcTemplate.update(updateSQL_QC_REPROT, id);
                if (i > 0) {
                	updateJdbcTemplate.update(updateSQL_QC, id);
                }
            }
            
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/qcTaskSubmitToDFS", e);
			log.error("/berry/prod/qc/qcTask/qcTaskSubmitToDFS", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // RNA DNA 检测任务: 退回生产检测
    @RequestMapping("/qcTask/backToYSC")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject qcTaskBackToYSC(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String BACK_REMARK_PROJECT = data.getString("BACK_REMARK_PROJECT");
    		
            String updateSQL="UPDATE BR_DNA_RNA_QC_REPROT SET QC_REPROT_FLAG='项目退回', BACK_REMARK_PROJECT=? WHERE ID=? AND QC_REPROT_FLAG='待发送'";
            
            for (String id : ids) {
            	updateJdbcTemplate.update(updateSQL, BACK_REMARK_PROJECT, id);
            }
            
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/qcTaskBackToYSC", e);
			log.error("/berry/prod/qc/qcTask/qcTaskBackToYSC", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // RNA DNA 检测任务: 提交到待反馈
    @RequestMapping("/qcTask/submitToDFK")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject qcTaskSubmitToDFK(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");

            String updateSQL_QC_REPROT = "UPDATE BR_DNA_RNA_QC_REPROT SET QC_REPROT_FLAG='待反馈' WHERE ID=? AND QC_REPROT_FLAG='待发送'";
            String updateSQL_QC = "UPDATE BR_DNA_RNA_QC SET QC_FLAG='待反馈' WHERE QC_REPROT_FILE=?";
            
            for (String id : ids) {
            	// 更新报告状态
            	int i = updateJdbcTemplate.update(updateSQL_QC_REPROT, id);
            	if (i > 0) {
            		// 更新核酸检测状态
            		updateJdbcTemplate.update(updateSQL_QC, id);
            	}
            }
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/qcTaskSubmitToDFK", e);
			log.error("/berry/prod/qc/qcTask/qcTaskSubmitToDFK", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // RNA DNA 检测任务: 提交到已反馈
    @RequestMapping("/qcTask/submitToYFK")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject qcTaskSubmitToYFK(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
            String updateSQL_QC_REPROT = "UPDATE BR_DNA_RNA_QC_REPROT SET QC_REPROT_FLAG='已反馈' WHERE ID=? AND QC_REPROT_FLAG='待反馈'";
            String updateSQL_QC = "UPDATE BR_DNA_RNA_QC SET QC_FLAG='已反馈' WHERE QC_REPROT_FILE=?";
            
            for (String id : ids) {
            	// 更新报告状态
            	int i = updateJdbcTemplate.update(updateSQL_QC_REPROT, id);
            	if (i > 0) {
            		// 更新核酸检测状态
            		updateJdbcTemplate.update(updateSQL_QC, id);
            	}
            }
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/qcTaskSubmitToYFK", e);
			log.error("/berry/prod/qc/qcTask/qcTaskSubmitToYFK", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // RNA DNA 检测任务: 提交
    @RequestMapping("/qcTask/submitFlow")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject qcTaskSubmitFlow(@RequestBody JSONObject data) {
    	
    	try {
    		int code=1;
    		Object[] objects=SysBasic.toTranObjectsByObject(data.get("ids"));
    		
        	JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(updateJdbcTemplate);
    		// 入口表表名
        	String BR_MODUAL_tableName = "BR_MODUAL_QC";
    		// 查询入口表结构
    	    Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName(BR_MODUAL_tableName);
    	    
    	    //查询核酸QC信息
    		String sqlcode="SELECT * FROM BR_DNA_RNA_QC WHERE QC_REPROT_FILE IN ("
    							+ "SELECT ID FROM BR_DNA_RNA_QC_REPROT WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(objects.length)+") AND QC_REPROT_FLAG='已反馈'"
    						+ ")";
    	    List<Map<String,Object>> listQC = jdbcTemplateUtils.queryTableListMapBySqlcode(sqlcode, objects);
    	    
    	    List<String> submitListIds = new ArrayList<String>();
    	    for(Map<String,Object> resultMap1 : listQC) {
    	    	String ID = (String) resultMap1.get("ID");
    	    	String MODUAL_QC_ID = (String) resultMap1.get("MODUAL_QC_ID");
    	    	// 移除 ID 避免覆盖
    	    	resultMap1.remove("ID");
	    		resultMap1.remove("MODUAL_QC_ID");
	    		// 重置ID
//	    		resultMap1.put("ID", MODUAL_QC_ID);
	    		
	    		Map<String,Object> resultMap = jdbcTemplateUtils.queryTableMapBySqlcode("SELECT * FROM "+BR_MODUAL_tableName+" WHERE ID=?", MODUAL_QC_ID);
	    		resultMap.putAll(resultMap1);
		    	
		    	// 判断是否变更新方案
		    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
		    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
		    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
		    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
		    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
		    	}
		    	
	    		//根据入口表字段，过滤查询结果多余的字段
	    		SysBasic.filterMap(resultMap, metaMap.keySet());
	    		int u = SysBasic.updateDataByTableMap(updateJdbcTemplate, BR_MODUAL_tableName, resultMap);
	    		if (u > 0) {
	    			// 记录需要提交流程的业务数据ID
		    		submitListIds.add(MODUAL_QC_ID);
	    		}
    	    }
    	    if(submitListIds.size() > 0) {
    	    	WorkflowMessage wflowMsg = workflowUtils.set(updateJdbcTemplate,updateJdbcTemplate).submit(Modulars.QC,submitListIds);
                if(wflowMsg.isB()){
                    sqlcode="UPDATE BR_DNA_RNA_QC SET QC_FLAG='已提交方案' WHERE QC_REPROT_FILE IN ("
                    				+ "SELECT ID FROM BR_DNA_RNA_QC_REPROT WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(objects.length)+") AND QC_REPROT_FLAG='已反馈'"
                    		+")";
                    int u=updateJdbcTemplate.update(sqlcode, objects);
                    sqlcode="UPDATE BR_DNA_RNA_QC_REPROT SET QC_REPROT_FLAG='已提交方案' WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(objects.length)+") AND QC_REPROT_FLAG='已反馈'";
                    u=updateJdbcTemplate.update(sqlcode, objects);
                    if(u>0){
                        code=1;
                    }else{
                        code=-1;
                        SysBasic.rollBack();
                    }
                }else{
                    code=-1;
                    SysBasic.rollBack();
                }
    	    }else {
    	    	SysBasic.rollBack();
    	    }
	    	return new CurrResponseResolve(code).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/qc/qcTask/submitFlow", e);
			log.error("/berry/prod/qc/qcTask/submitFlow", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
