package com.kinglims.berry.prod.lib;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/bio/lib/modular")
@Slf4j
public class BioLibIndexController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;

    @Autowired
    private ResponseMessageParameter responseMessageParameter;


    /**
     * 便利index
     * @param map
     * @return
     */
    @RequestMapping(value = "/listIndex", produces = "application/json;charset=UTF-8")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject listIndex(@RequestBody Map<String,Object> map,@RequestHeader(name="Authorization")String token) {
        int code=0;

        //文库类型
        String INDEX_TYPE = SysBasic.toTranStringByObject(map.get("INDEX_TYPE"));
        //index个数
        String INDEX_NUMBER = SysBasic.toTranStringByObject(map.get("INDEX_NUMBER"));
        //参数1、2、3
        String KEY1 = SysBasic.toTranStringByObject(map.get("KEY1"));
        String VALUE1 = SysBasic.toTranStringByObject(map.get("VALUE1"));

        String KEY2 = SysBasic.toTranStringByObject(map.get("KEY2"));
        String VALUE2 = SysBasic.toTranStringByObject(map.get("VALUE2"));

        String KEY3 = SysBasic.toTranStringByObject(map.get("KEY3"));
        String VALUE3 = SysBasic.toTranStringByObject(map.get("VALUE3"));

        if(!SysBasic.isNumeric(INDEX_NUMBER)){
            code = -1;
            String message = "index个数【INDEX_NUMBER】请输入正整数！";
            return new CurrResponseResolve(code).put(responseMessageParameter).put("message",message).getData();
        }
        try{
            String sql = "";
            if(!"".equals(KEY1)){
                sql += " and "+ KEY1 +" = '" +VALUE1+ "' ";
            }
            if(!"".equals(KEY2)){
                sql += " and "+ KEY2 +" = '" +VALUE2+ "' ";
            }
            if(!"".equals(KEY3)){
                sql += " and "+ KEY3 +" = '" +VALUE3+ "' ";
            }
            String sqlcode ="select * from BIO_LIB_INDEX " +
                    " where INDEX_STATUS = '正常' AND INDEX_TYPE = ?" +
                    sql +
                    " order by SERIAL_NUMBER,ORDERBY,INDEX_NAME ";

            //                " order by SERIAL_NUMBER,INDEX_NAME ";

            List<Map<String, Object>> listIndex = queryJdbcTemplate.queryForList(sqlcode, INDEX_TYPE);
            if(listIndex.size()>0){
                int INDEXNUMBER = SysBasic.toTranIntegerByObject(map.get("INDEX_NUMBER"));
                if(INDEXNUMBER<=0){
                    code = -1;
                    String message = "index个数【INDEX_NUMBER】请输入正整数！";
                    return new CurrResponseResolve(code).put(responseMessageParameter).put("message",message).getData();
                }
                List<Map<String, Object>> returnIndex = new ArrayList<>();
                int no = 0;
                int i = 1;
                while (true){
                    if(no==listIndex.size()){
                        no=0;
                    }
                    Map<String, Object> indexMap = listIndex.get(no);
                    Map<String, Object> updateMap = new HashMap<>();
                    String ID = SysBasic.toTranStringByObject(indexMap.get("ID"));
                    int SERIAL_NUMBER = SysBasic.toTranIntegerByObject(indexMap.get("SERIAL_NUMBER"));
                    updateMap.put("ID",ID);
                    updateMap.put("SERIAL_NUMBER",SERIAL_NUMBER+1);
                    int u=SysBasic.updateDataByTableMap(updateJdbcTemplate,"BIO_LIB_INDEX",updateMap);
                    if(u>0){
                        code=1;
                    }else{
                        code=-1;
                        SysBasic.rollBack();
                        return new CurrResponseResolve(code).put(responseMessageParameter).put("message","index修改失败").getData();
                    }
                    indexMap.put("NO",i);
                    returnIndex.add(indexMap);
                    no++;
                    if(i==INDEXNUMBER){
                        break;
                    }
                    i++;
                }
                //更新index
                code = 1;
                return new CurrResponseResolve(code).put(responseMessageParameter).put("INDEX",returnIndex).getData();

            }else{
                code = -1;
                String message = "文库类型【"+INDEX_TYPE+"】未在index维护库中找到！";
                return new CurrResponseResolve(code).put(responseMessageParameter).put("message",message).getData();
            }
        }catch (Exception e){
            e.printStackTrace();
            code = -1;
            String message = e.getMessage();
            return new CurrResponseResolve(code).put(responseMessageParameter).put("message",message).getData();

        }


    }

}
