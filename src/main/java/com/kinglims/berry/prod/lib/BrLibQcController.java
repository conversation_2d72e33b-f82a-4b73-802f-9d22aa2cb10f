package com.kinglims.berry.prod.lib;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.system.workflow.handler.logic.Modulars;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowMessage;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowUtils;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/lib/libQC")
@Slf4j
public class BrLibQcController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    @Autowired
    private WorkflowUtils workflowUtils;
    
    // 文库QC: 选择添加 
    @RequestMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject add(@RequestBody JSONObject data) {
    	
    	try {
    		String type = data.getString("type");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selRKB = "SELECT LIB_QC_ID FROM BR_MODUAL_LIBQC WHERE ID=?";
    		
    		String insertSQL = "INSERT INTO BR_LIB_QC ("
    				+ "ID" //唯一标识
    				+ ",LINK_ID" //关联入口表ID
    				+ ",TASK_ID" //关联任务单主表ID
    				+ ",TASK_MX_ID" //关联任务单明细ID
    				+ ",TASK_NO" //任务单编号
    				+ ",TASK_NAME" //任务单名称
    				+ ",TASK_TYPE" //任务单类型
    				+ ",EXEC_SAMLL" //执行小组
    				+ ",PROJECT_ID" //关联项目ID
    				+ ",PROJECT_NO" //项目编号
    				+ ",PROJECT_NAME" //项目名称
    				+ ",SAMPLE_ID" //关联样品ID
    				+ ",SAMPLE_CODE" //样品编号
    				+ ",SAMPLE_NAME" //样品名称
    				+ ",LIB_TYPE" //文库类型
    				+ ",LIB_ID" //关联文库ID
    				+ ",LIB_CODE" //文库名称
    				+ ",LIB_QC_TYPE" //文库质控类型
//    				+ ",LIB_QC_CREATOR" //文库QC创建人
//    				+ ",LIB_QC_CREATTIME" //文库QC创建时间
//    				+ ",LIB_QC_LASTUPDATOR" //文库QC最近修改人
//    				+ ",LIB_QC_LASTUPDATETIME" //文库QC最近修改时间
//    				+ ",LOGINCOMPANY" //账套
    				+ ") "
    				+ "SELECT"
    				+ "? AS ID" //唯一标识
    				+ ",ID AS LINK_ID" //关联入口表ID
    				+ ",TASK_ID" //关联任务单主表ID
    				+ ",TASK_MX_ID" //关联任务单明细ID
    				+ ",TASK_NO" //任务单编号
    				+ ",TASK_NAME" //任务单名称
    				+ ",TASK_TYPE" //任务单类型
    				+ ",EXEC_SAMLL" //执行小组
    				+ ",PROJECT_ID" //关联项目ID
    				+ ",PROJECT_NO" //项目编号
    				+ ",PROJECT_NAME" //项目名称
    				+ ",SAMPLE_ID" //关联样品ID
    				+ ",SAMPLE_CODE" //样品编号
    				+ ",SAMPLE_NAME" //样品名称
    				+ ",LIB_TYPE" //文库类型
    				+ ",LIB_ID" //关联文库ID
    				+ ",LIB_CODE" //文库名称
    				+ ",LIB_QC_TYPE" //文库质控类型
    				+ " FROM BR_MODUAL_LIBQC WHERE ID=?";//创建QC表记录
    		
    		String updateRKB = "UPDATE BR_MODUAL_LIBQC SET LIB_QC_ID=?,OBJ_FLAG='归结' WHERE ID=? AND LIB_QC_ID IS NULL";//更新入口表关联关系
    		
    		String updateRKB_2100 = "UPDATE BR_MODUAL_LIBQC SET QC_2100_FLAG='已接收' WHERE ID=?";
    		String updateQC_2100 = "UPDATE BR_LIB_QC SET QC_2100_FLAG='进行中' WHERE ID=?";
    		
    		String updateRKB_QPCR = "UPDATE BR_MODUAL_LIBQC SET QC_QPCR_FLAG='已接收' WHERE ID=?";
    		String updateQC_QPCR = "UPDATE BR_LIB_QC SET QC_QPCR_FLAG='进行中' WHERE ID=?";
    		
    		String updateRKB_QUBIT = "UPDATE BR_MODUAL_LIBQC SET QC_QUBIT_FLAG='已接收' WHERE ID=?";
    		String updateQC_QUBIT = "UPDATE BR_LIB_QC SET QC_QUBIT_FLAG='进行中' WHERE ID=?";
    		
	    	for (String id : ids) {
	    		String libQcID = queryJdbcTemplate.queryForObject(selRKB, String.class, id);
	    		libQcID = libQcID==null ? "" : libQcID.trim();
	    		int nextFlag = 0;
	    		if (libQcID.length()==0) {//新增
	    			libQcID = SysBasic.getUUID();
	    			nextFlag = updateJdbcTemplate.update(updateRKB, libQcID, id);//更新入口表关联关系
	    			if (nextFlag > 0) {
	    				updateJdbcTemplate.update(insertSQL, libQcID, id);//创建QC表记录
	    			}
	    		} else {
	    			 nextFlag = 1;
	    		}
	    		
	    		if ("2100".equals(type) && nextFlag > 0) {
	    			updateJdbcTemplate.update(updateRKB_2100, id);
	    			updateJdbcTemplate.update(updateQC_2100, libQcID);
	    		}
	    		if ("QPCR".equals(type) && nextFlag > 0) {
	    			updateJdbcTemplate.update(updateRKB_QPCR, id);
	    			updateJdbcTemplate.update(updateQC_QPCR, libQcID);
	    		}
	    		if ("QUBIT".equals(type) && nextFlag > 0) {
	    			updateJdbcTemplate.update(updateRKB_QUBIT, id);
	    			updateJdbcTemplate.update(updateQC_QUBIT, libQcID);
	    		}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/libQC/add", e);
			log.error("/berry/prod/lib/libQC/add", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 文库QC: 提交
    @RequestMapping("/commit")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commit(@RequestBody JSONObject data) {
    	
    	try {
    		String type = data.getString("type");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		List<String> submitListIds=new ArrayList<String>();
    		List<String> submitIds=new ArrayList<String>();
    		if ("2100".equals(type) || "QPCR".equals(type) || "QUBIT".equals(type)) {
    			
	    		String updateQC_2100 = "UPDATE BR_LIB_QC SET QC_2100_FLAG='已提交' WHERE ID=? AND QC_2100_FLAG='进行中'";
	    		String updateMQC_2100 = "UPDATE BR_MODUAL_LIBQC SET QC_2100_FLAG='已提交' WHERE LIB_QC_ID=? AND QC_2100_FLAG='已接收'";
	    		
	    		String updateQC_QPCR = "UPDATE BR_LIB_QC SET QC_QPCR_FLAG='已提交' WHERE ID=? AND QC_QPCR_FLAG='进行中'";
	    		String updateMQC_QPCR = "UPDATE BR_MODUAL_LIBQC SET QC_QPCR_FLAG='已提交' WHERE LIB_QC_ID=? AND QC_QPCR_FLAG='已接收'";
	    		
	    		String updateQC_QUBIT = "UPDATE BR_LIB_QC SET QC_QUBIT_FLAG='已提交' WHERE ID=? AND QC_QUBIT_FLAG='进行中'";
	    		String updateMQC_QUBIT = "UPDATE BR_MODUAL_LIBQC SET QC_QUBIT_FLAG='已提交' WHERE LIB_QC_ID=? AND QC_QUBIT_FLAG='已接收'";
	    		
	    		String selectM_FLAG = "SELECT ID,LIB_QC_ID"
	    				+",(CASE WHEN LIB_NEXT LIKE '%2100%' THEN 1 ELSE 0 END) AS NEXT_2100"
	    				+",QC_2100_FLAG AS FLAG_2100"
	    				+",(CASE WHEN LIB_NEXT LIKE '%QPCR%' THEN 1 ELSE 0 END) AS NEXT_QPCR"
	    				+",QC_QPCR_FLAG AS FLAG_QPCR"
	    				+",(CASE WHEN LIB_NEXT LIKE '%QUBIT%' THEN 1 ELSE 0 END) AS NEXT_QUBIT"
	    				+",QC_QUBIT_FLAG AS FLAG_QUBIT"
	    				+" FROM BR_MODUAL_LIBQC WHERE LIB_QC_ID=?";
	    		
	        	JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(updateJdbcTemplate);
	    		// 入口表表名
	        	String BR_MODUAL_tableName = "BR_MODUAL_LIBQC";
	            // 查询入口表结构
	            Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName(BR_MODUAL_tableName);
	    		
	    		for (String id : ids) {
		    		if ("2100".equals(type)) {
		    			int i = updateJdbcTemplate.update(updateQC_2100, id);
		    			if (i > 0) {
		    				updateJdbcTemplate.update(updateMQC_2100, id);
		    			}
		    		}
		    		if ("QPCR".equals(type)) {
		    			int i = updateJdbcTemplate.update(updateQC_QPCR, id);
		    			if (i > 0) {
		    				updateJdbcTemplate.update(updateMQC_QPCR, id);
		    			}
		    		}
		    		if ("QUBIT".equals(type)) {
		    			int i = updateJdbcTemplate.update(updateQC_QUBIT, id);
		    			if (i > 0) {
		    				updateJdbcTemplate.update(updateMQC_QUBIT, id);
		    			}
		    		}
		    		
		    		//提交流程判断逻辑
		    		Map<String, Object> map = updateJdbcTemplate.queryForMap(selectM_FLAG, id);
//		    		String ID = map.get("ID").toString();
		    		BigDecimal bd_NEXT_2100 = (BigDecimal) map.get("NEXT_2100");
		    		int NEXT_2100 = bd_NEXT_2100.intValue();
		    		String FLAG_2100 = map.get("FLAG_2100")+"";

		    		BigDecimal bd_NEXT_QPCR = (BigDecimal) map.get("NEXT_QPCR");
		    		int NEXT_QPCR = bd_NEXT_QPCR.intValue();
		    		String FLAG_QPCR = map.get("FLAG_QPCR")+"";
		    		
		    		BigDecimal bd_NEXT_QUBIT = (BigDecimal) map.get("NEXT_QUBIT");
		    		int NEXT_QUBIT = bd_NEXT_QUBIT.intValue();
		    		String FLAG_QUBIT = map.get("FLAG_QUBIT")+"";
		    		if (
		    			((NEXT_2100==1 && "已提交".equals(FLAG_2100)) || NEXT_2100==0)
		    		 && ((NEXT_QPCR==1 && "已提交".equals(FLAG_QPCR)) || NEXT_QPCR==0)
		    		 && ((NEXT_QUBIT==1 && "已提交".equals(FLAG_QUBIT)) || NEXT_QUBIT==0)
		    		) {
		    			// 全量更新业务数据到入口表
		    			Map<String,Object> resultMap1 = jdbcTemplateUtils.queryTableMapBySqlcode("select * from BR_LIB_QC where ID=?", id);
		    			String ID = (String) resultMap1.get("ID");
		    			String LINK_ID = (String) resultMap1.get("LINK_ID");
		    			// 移除 ID 避免覆盖
			    		resultMap1.remove("ID");
			    		resultMap1.remove("LINK_ID");
			    		resultMap1.remove("QC_2100_FLAG");
			    		resultMap1.remove("QC_QPCR_FLAG");
			    		resultMap1.remove("QC_QUBIT_FLAG");
			    		// 重置ID
//			    		resultMap1.put("ID", LINK_ID);
			    		
			    		Map<String,Object> resultMap = jdbcTemplateUtils.queryTableMapBySqlcode("select * from "+BR_MODUAL_tableName+" where ID=?", LINK_ID);
			    		resultMap.putAll(resultMap1);
				    	
				    	// 判断是否变更新方案
				    	Object FLOW_PLAN_ID_NEW = resultMap.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
				    	Object FLOW_PLAN_ID = resultMap.get("FLOW_PLAN_ID");//模块表:原方案ID
				    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
				    		resultMap.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
				    		resultMap.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
				    	}
				    	
			    		//根据入口表字段，过滤查询结果多余的字段
			    		SysBasic.filterMap(resultMap, metaMap.keySet());
			    		int u = SysBasic.updateDataByTableMap(updateJdbcTemplate, BR_MODUAL_tableName, resultMap);
			    		if (u > 0) {
			    			// 记录需要提交流程的业务数据ID
				    		submitListIds.add(LINK_ID);
				    		submitIds.add(ID);
			    		}
		    		} else {
		    		}
	    		}
    		}
    		// 提交流程代码
    		if (submitListIds.size() > 0) {
    			WorkflowMessage wflowMsg = workflowUtils.set(updateJdbcTemplate,updateJdbcTemplate).submit(Modulars.LIBQC,submitListIds);
	            if(wflowMsg.isB()){
	            	String sqlcode="update BR_LIB_QC set"
	            			+ " QC_2100_FLAG=(CASE WHEN QC_2100_FLAG='已提交' THEN '已提交方案' ELSE QC_2100_FLAG END)"
	            			+ ",QC_QPCR_FLAG=(CASE WHEN QC_QPCR_FLAG='已提交' THEN '已提交方案' ELSE QC_QPCR_FLAG END)"
	            			+ ",QC_QUBIT_FLAG=(CASE WHEN QC_QUBIT_FLAG='已提交' THEN '已提交方案' ELSE QC_QUBIT_FLAG END)"
	            			+ " where ID in("+SysBasic.getQuestionMarkBySzie(submitIds.size())+")";
                    updateJdbcTemplate.update(sqlcode,submitIds.toArray());
	            }else {
	            	throw new RuntimeException();
	            }
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/libQC/commit", e);
			log.error("/berry/prod/lib/libQC/commit", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 撤回方案: LibQC
    @RequestMapping("/revokeLibQCFlow")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeLibQCFlow(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String sqlUpdate = "UPDATE BR_LIB_QC SET"
        			+ " QC_2100_FLAG=(CASE WHEN QC_2100_FLAG='已提交方案' THEN '进行中' ELSE QC_2100_FLAG END)"
        			+ ",QC_QPCR_FLAG=(CASE WHEN QC_QPCR_FLAG='已提交方案' THEN '进行中' ELSE QC_QPCR_FLAG END)"
        			+ ",QC_QUBIT_FLAG=(CASE WHEN QC_QUBIT_FLAG='已提交方案' THEN '进行中' ELSE QC_QUBIT_FLAG END)"
        			+ " WHERE ID=?";
    		String sqlUpdateM = "UPDATE BR_MODUAL_LIBQC SET"
        			+ " QC_2100_FLAG=(CASE WHEN QC_2100_FLAG='已提交' THEN '已接收' ELSE QC_2100_FLAG END)"
        			+ ",QC_QPCR_FLAG=(CASE WHEN QC_QPCR_FLAG='已提交' THEN '已接收' ELSE QC_QPCR_FLAG END)"
        			+ ",QC_QUBIT_FLAG=(CASE WHEN QC_QUBIT_FLAG='已提交' THEN '已接收' ELSE QC_QUBIT_FLAG END)"
        			+ " WHERE LIB_QC_ID=?";
    		String sqlSELECT_LINKID = "SELECT LINK_ID FROM BR_LIB_QC WHERE ID=?";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(sqlUpdate, id);
    			if (i > 0) {
    				updateJdbcTemplate.update(sqlUpdateM, id);
    				// 查询提取明细 LINK_ID
    				List<String> linkIdList = updateJdbcTemplate.queryForList(sqlSELECT_LINKID, String.class, id);
    				WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.LIBQC, linkIdList);
    				if (!wflowMsg.isB()) {
    					SysBasic.rollBack();
    					break;
    				}
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/libQC/revokeLibQCFlow", e);
			log.error("/berry/prod/lib/libQC/revokeLibQCFlow", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 文库QC: 删除
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {
    	
    	try {
    		String type = data.getString("type");
    		List<String> ids = (List<String>) data.get("ids");

    		String updateQC_2100 = "UPDATE BR_LIB_QC SET QC_2100_FLAG=NULL"
			    				+ ",QC2100_2100_FILE=NULL" //2100文件
			    				+ ",QC2100_QC_REPORT=NULL" //QC报告
			    				+ ",QC2100_JT_FILE1=NULL" //胶图文件1
			    				+ ",QC2100_JT_FILE2=NULL" //胶图文件2
			    				+ ",QC2100_RUSULT=NULL" //检测结果
			    				+ ",QC2100_REMARK=NULL" //检测备注
			    				+ ",QC2100_MAN=NULL" //实验员
			    				+ ",QC2100_ASSIGNOR=NULL" //指派人
			    				+ ",QC2100_BOARD96_ID=NULL" //关联板ID
			    				+ ",QC2100_BOARD96_CODE=NULL" //96孔板板号
			    				+ ",QC2100_BOARD96_HOLE=NULL" //96孔板孔号
			    				+ ",QC2100_IS_BOARD=NULL" //是否拼板
			    				+ ",QC2100_MERND=NULL" //摩尔浓度(nM)
			    				+ ",QC2100_ZLND=NULL" //质量浓度(ng/ul)
			    				+ ",QC2100_PJ_SIZE=NULL" //文库平均大小(bp)
			    				+ ",QC2100_FROM_SIZE=NULL" //from(bp)
			    				+ ",QC2100_TO_SIZE=NULL" //to(bp)
			    				+ ",QC2100_TEST_DATE=NULL" //检测日期
    							+ " WHERE ID=? AND QC_2100_FLAG='进行中'";
    		String updateRKB_2100 = "UPDATE BR_MODUAL_LIBQC SET QC_2100_FLAG='未接收'"// 2100接收状态
			    				+ ",QC2100_2100_FILE=NULL" //2100文件
			    				+ ",QC2100_QC_REPORT=NULL" //QC报告
			    				+ ",QC2100_JT_FILE1=NULL" //胶图文件1
			    				+ ",QC2100_JT_FILE2=NULL" //胶图文件2
			    				+ ",QC2100_RUSULT=NULL" //检测结果
			    				+ ",QC2100_REMARK=NULL" //检测备注
			    				+ ",QC2100_MAN=NULL" //实验员
			    				+ ",QC2100_ASSIGNOR=NULL" //指派人
			    				+ ",QC2100_BOARD96_ID=NULL" //关联板ID
			    				+ ",QC2100_BOARD96_CODE=NULL" //96孔板板号
			    				+ ",QC2100_BOARD96_HOLE=NULL" //96孔板孔号
			    				+ ",QC2100_IS_BOARD=NULL" //是否拼板
			    				+ ",QC2100_MERND=NULL" //摩尔浓度(nM)
			    				+ ",QC2100_ZLND=NULL" //质量浓度(ng/ul)
			    				+ ",QC2100_PJ_SIZE=NULL" //文库平均大小(bp)
			    				+ ",QC2100_FROM_SIZE=NULL" //from(bp)
			    				+ ",QC2100_TO_SIZE=NULL" //to(bp)
			    				+ ",QC2100_TEST_DATE=NULL" //检测日期
			    				+ " WHERE LIB_QC_ID=?";
    		
    		String updateQC_QPCR = "UPDATE BR_LIB_QC SET QC_QPCR_FLAG=NULL"
			    				+ ",QPCR_QC_REPROT=NULL" //QC报告
			    				+ ",QCPR_JT_FILE1=NULL" //胶图文件1
			    				+ ",QCPR_JT_FILE2=NULL" //胶图文件2
			    				+ ",QPCR_RUSULT=NULL" //检测结果
			    				+ ",QPCR_REMARK=NULL" //检测备注
			    				+ ",QPCR_MAN=NULL" //实验员
			    				+ ",QPCR_ASSIGNOR=NULL" //指派人
			    				+ ",QPCR_BOARD96_ID=NULL" //关联板ID
			    				+ ",QPCR_BOARD96_CODE=NULL" //96孔板板号
			    				+ ",QPCR_BOARD96_HOLE=NULL" //96孔板孔号
			    				+ ",QPCR_IS_BOARD=NULL" //是否拼板
			    				+ ",QPCR_ZHND=NULL" //转换后摩尔浓度（nM）
			    				+ ",QPCR_MERND=NULL" //摩尔浓度(nM)
			    				+ ",QPCR_ZLND=NULL" //质量浓度(ng/ul)
			    				+ ",QPCR_PJ_SIZE=NULL" //文库平均大小(bp)
			    				+ ",QPCR_TEST_DATE=NULL" //检测日期
			    				+ " WHERE ID=? AND QC_QPCR_FLAG='进行中'";
    		String updateRKB_QPCR = "UPDATE BR_MODUAL_LIBQC SET QC_QPCR_FLAG='未接收'"// QPCR接收状态
			    				+ ",QPCR_QC_REPROT=NULL" //QC报告
			    				+ ",QCPR_JT_FILE1=NULL" //胶图文件1
			    				+ ",QCPR_JT_FILE2=NULL" //胶图文件2
			    				+ ",QPCR_RUSULT=NULL" //检测结果
			    				+ ",QPCR_REMARK=NULL" //检测备注
			    				+ ",QPCR_MAN=NULL" //实验员
			    				+ ",QPCR_ASSIGNOR=NULL" //指派人
			    				+ ",QPCR_BOARD96_ID=NULL" //关联板ID
			    				+ ",QPCR_BOARD96_CODE=NULL" //96孔板板号
			    				+ ",QPCR_BOARD96_HOLE=NULL" //96孔板孔号
			    				+ ",QPCR_IS_BOARD=NULL" //是否拼板
			    				+ ",QPCR_ZHND=NULL" //转换后摩尔浓度（nM）
			    				+ ",QPCR_MERND=NULL" //摩尔浓度(nM)
			    				+ ",QPCR_ZLND=NULL" //质量浓度(ng/ul)
			    				+ ",QPCR_PJ_SIZE=NULL" //文库平均大小(bp)
			    				+ ",QPCR_TEST_DATE=NULL" //检测日期
			    				+ " WHERE LIB_QC_ID=?";
    		
    		String updateQC_QUBIT = "UPDATE BR_LIB_QC SET QC_QUBIT_FLAG=NULL"
			    				+ ",QUBIT_JT_FILE1=NULL" //胶图文件1
			    				+ ",QUBIT_JT_FILE2=NULL" //胶图文件2
			    				+ ",QUBIT_RUSULT=NULL" //检测结果
			    				+ ",QUBIT_REMARK=NULL" //检测备注
			    				+ ",QUBIT_MAN=NULL" //实验员
			    				+ ",QUBIT_ASSIGNOR=NULL" //指派人
			    				+ ",QUBIT_BOARD96_ID=NULL" //关联板ID
			    				+ ",QUBIT_BOARD96_CODE=NULL" //96孔板板号
			    				+ ",QUBIT_BOARD96_HOLE=NULL" //96孔板孔号
			    				+ ",QUBIT_IS_BOARD=NULL" //是否拼板
			    				+ ",QUBIT_ZHND=NULL" //转换后摩尔浓度（nM）
			    				+ ",QUBIT_MERND=NULL" //摩尔浓度(nM)
			    				+ ",QUBIT_ZLND=NULL" //质量浓度(ng/ul)
			    				+ ",QUBIT_TEST_DATE=NULL" //检测日期
			    				+ " WHERE ID=? AND QC_QUBIT_FLAG='进行中'";
    		String updateRKB_QUBIT = "UPDATE BR_MODUAL_LIBQC SET QC_QUBIT_FLAG='未接收'"// QUBIT接收状态
			    				+ ",QUBIT_JT_FILE1=NULL" //胶图文件1
			    				+ ",QUBIT_JT_FILE2=NULL" //胶图文件2
			    				+ ",QUBIT_RUSULT=NULL" //检测结果
			    				+ ",QUBIT_REMARK=NULL" //检测备注
			    				+ ",QUBIT_MAN=NULL" //实验员
			    				+ ",QUBIT_ASSIGNOR=NULL" //指派人
			    				+ ",QUBIT_BOARD96_ID=NULL" //关联板ID
			    				+ ",QUBIT_BOARD96_CODE=NULL" //96孔板板号
			    				+ ",QUBIT_BOARD96_HOLE=NULL" //96孔板孔号
			    				+ ",QUBIT_IS_BOARD=NULL" //是否拼板
			    				+ ",QUBIT_ZHND=NULL" //转换后摩尔浓度（nM）
			    				+ ",QUBIT_MERND=NULL" //摩尔浓度(nM)
			    				+ ",QUBIT_ZLND=NULL" //质量浓度(ng/ul)
			    				+ ",QUBIT_TEST_DATE=NULL" //检测日期
			    				+ " WHERE LIB_QC_ID=?";
    		
    		String delSQL = "DELETE FROM BR_LIB_QC WHERE ID=? AND QC_2100_FLAG IS NULL AND QC_QPCR_FLAG IS NULL AND QC_QUBIT_FLAG IS NULL";
    		String updateRKB = "UPDATE BR_MODUAL_LIBQC SET LIB_QC_ID=NULL,OBJ_FLAG=NULL"
		    				+ ",LIB_QC_CREATOR=NULL" //文库QC创建人
		    				+ ",LIB_QC_CREATTIME=NULL" //文库QC创建时间
		    				+ ",LIB_QC_LASTUPDATOR=NULL" //文库QC最近修改人
		    				+ ",LIB_QC_LASTUPDATETIME=NULL" //文库QC最近修改时间
		    				+ " WHERE LIB_QC_ID=?";
    		if ("2100".equals(type) || "QPCR".equals(type) || "QUBIT".equals(type)) {
	    		for (String id : ids) {
		    		if ("2100".equals(type)) {// 删除2100信息
		    			int i = updateJdbcTemplate.update(updateQC_2100, id);
		    			if (i>0) {
		    				updateJdbcTemplate.update(updateRKB_2100, id);
		    			}
		    		}
		    		if ("QPCR".equals(type)) {// 删除QPCR信息
		    			int i = updateJdbcTemplate.update(updateQC_QPCR, id);
		    			if (i>0) {
		    				updateJdbcTemplate.update(updateRKB_QPCR, id);
		    			}
		    		}
		    		if ("QUBIT".equals(type)) {// 删除QUBIT信息
		    			int i = updateJdbcTemplate.update(updateQC_QUBIT, id);
		    			if (i>0) {
		    				updateJdbcTemplate.update(updateRKB_QUBIT, id);
		    			}
		    		}
	    			
	    			int i = updateJdbcTemplate.update(delSQL, id);// 检查2100/QPCR/QUBIT状态，满足条件删除QC表
	    			if (i > 0) {
	    				updateJdbcTemplate.update(updateRKB, id);// 清空入口表关联信息
	    			}
	    		}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/libQC/del", e);
			log.error("/berry/prod/lib/libQC/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
