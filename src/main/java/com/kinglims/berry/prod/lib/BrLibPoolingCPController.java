package com.kinglims.berry.prod.lib;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/lib/pooling/CP")
@Slf4j
public class BrLibPoolingCPController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 选择添加明细
    @RequestMapping("/addMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject addMX(@RequestBody JSONObject data) {
    	
    	try {
    		String POOL_LIB_ID = data.getString("POOL_LIB_ID");//混库主单ID
    		List<String> ids = (List<String>) data.get("ids");
    		
    		// 验证主表单状态，判断是否可以添加明细
    		String selectSQL = "SELECT COUNT(1) FROM BR_CAPTURE_POOLING_INFO WHERE ID=? AND CAP_POOL_LIB_FLAG='Pooling'";
    		// 添加明细数据
    		String insertSQL = "INSERT INTO BR_CAPTURE_POOLING_MX_INFO ("
    				+ "ID" //唯一标识
    				+ ",CAP_LIB_ID" //关联预文库混库ID
    				+ ",LINK_ID" //关联入口表ID
    				+ ",SAMPLE_ID" //关联样品ID
    				+ ",SAMPLE_CODE" //样品编号
    				+ ",SAMPLE_NAME" //样品名称
    				+ ",LIB_READY_ID" //关联预文库名称
    				+ ",LIB_READY_CODE" //预文库名称
    				+ ",LIB_BARCODE_CODE" //Barcode号
    				+ ",CAP_ZL_MD" //质量浓度(ng/uL）
    				+ ",CAP_POOL_VOLUME" //参考混合休积(uL)
    				+ ") "
    				+ "SELECT"
    				+ "? AS ID" //唯一标识
    				+ ",? AS CAP_LIB_ID" //关联预文库混库ID
    				+ ",ID AS LINK_ID" //关联入口表ID
    				+ ",SAMPLE_ID" //关联样品ID
    				+ ",SAMPLE_CODE" //样品编号
    				+ ",SAMPLE_NAME" //样品名称
    				+ ",LIB_READY_ID" //关联预文库名称
    				+ ",LIB_READY_CODE" //预文库名称
    				+ ",LIB_BARCODE_CODE" //Barcode号
    				+ ",CAP_ZL_MD" //质量浓度(ng/uL）
    				+ ",CAP_POOL_VOLUME" //参考混合休积(uL)
    				+ " FROM BR_MODUAL_CP WHERE ID=?";
    		// 更新入口表关联信息
    		String updateSQL = "UPDATE BR_MODUAL_CP SET CAP_ID=?,CAP_MX_ID=?,OBJ_FLAG='归结' WHERE ID=? AND CAP_ID IS NULL";
    		
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, POOL_LIB_ID);
    		if (count == 1) {
	    		for (String id : ids) {
	    			String POOL_LIB_MX_ID = SysBasic.getUUID();
	    			int i = updateJdbcTemplate.update(updateSQL, POOL_LIB_ID, POOL_LIB_MX_ID, id);
	    			if (i > 0) {
	    				updateJdbcTemplate.update(insertSQL, POOL_LIB_MX_ID, POOL_LIB_ID, id);
	    			}
	    		}
    		} else {
    			return new CurrResponseResolve(-1).put("errMsg","状态不是Pooling不能添加明细").put(responseMessageParameter).getData();
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/CP/addMX", e);
			log.error("/berry/prod/lib/pooling/CP/addMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除明细
    @RequestMapping("/delMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delMX(@RequestBody JSONObject data) {
    	
    	try {
    		String POOL_LIB_ID = data.getString("POOL_LIB_ID");//混库主单ID
    		List<String> ids = (List<String>) data.get("ids");
    		
    		// 验证主表单状态，判断是否可以添加明细
    		String selectSQL = "SELECT COUNT(1) FROM BR_CAPTURE_POOLING_INFO WHERE ID=? AND CAP_POOL_LIB_FLAG='Pooling'";
    		// 删除明细数据
    		String delSQL = "DELETE FROM BR_CAPTURE_POOLING_MX_INFO WHERE ID=?";
    		// 更新入口表关联信息
    		String updateSQL = "UPDATE BR_MODUAL_CP SET CAP_ID=NULL,CAP_MX_ID=NULL,OBJ_FLAG=NULL WHERE CAP_MX_ID=?";
    		
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, POOL_LIB_ID);
    		if (count == 1) {
	    		for (String id : ids) {
	    			updateJdbcTemplate.update(delSQL, id);
	    			updateJdbcTemplate.update(updateSQL, id);
	    		}
    		} else {
    			return new CurrResponseResolve(-1).put("errMsg","状态不是Pooling不能删除明细").put(responseMessageParameter).getData();
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/CP/delMX", e);
			log.error("/berry/prod/lib/pooling/CP/delMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String delSQL_M = "DELETE FROM BR_CAPTURE_POOLING_INFO WHERE ID=? AND CAP_POOL_LIB_FLAG='Pooling'";
    		String delSQL_D = "DELETE FROM BR_CAPTURE_POOLING_MX_INFO WHERE CAP_LIB_ID=?";
    		String updateSQL_RKB = "UPDATE BR_MODUAL_CP SET CAP_ID=NULL,CAP_MX_ID=NULL,OBJ_FLAG=NULL WHERE CAP_ID=?";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(delSQL_M, id);
    			if (i > 0) {
    				updateJdbcTemplate.update(delSQL_D, id);
    				updateJdbcTemplate.update(updateSQL_RKB, id);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/CP/del", e);
			log.error("/berry/prod/lib/pooling/CP/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提交到审核
    @RequestMapping("/commitToApprove")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commitToApprove(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_CAPTURE_POOLING_INFO SET CAP_POOL_LIB_FLAG='待审核' WHERE ID=? AND CAP_POOL_LIB_FLAG='Pooling'";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(updateSQL, id);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/TGS/commitToApprove", e);
			log.error("/berry/prod/lib/pooling/TGS/commitToApprove", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
