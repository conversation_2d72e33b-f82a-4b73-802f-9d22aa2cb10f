package com.kinglims.berry.prod.lib;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/lib/pooling/TGS")
@Slf4j
public class BrLibPoolingTGSController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 选择添加明细
    @RequestMapping("/addMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject addMX(@RequestBody JSONObject data) {
    	
    	try {
    		String TPOOL_LIB_ID = data.getString("TPOOL_LIB_ID");//混库主单ID
    		List<String> ids = (List<String>) data.get("ids");
    		
    		// 1.自建库; 2.外来文库; 3.加测; 4.重上机; 5.平衡文库; 6.研发文库; 7.包LANE
    		int addIndexFlag = data.getInteger("addIndexFlag");
    		
    		String LIB_PH_FLAG = "N";//平衡文库标识
    		
    		// 验证主表单状态，判断是否可以添加明细
    		String selectSQL = "SELECT COUNT(1) FROM BR_TPOOLING_INFO WHERE ID=? AND TPOOL_LIB_FLAG='Pooling'";
    		// 添加明细数据
    		String insertSQL = "INSERT INTO BR_TPOOLING_MX_INFO ("
    				+ "ID" //唯一标识
    				+ ",TPOOL_LIB_ID" //关联混库ID
    				+ ",LINK_ID" //关联入口表ID
    				+ ",TASK_ID" //关联任务单主表ID
    				+ ",TASK_MX_ID" //关联任务单明细ID
    				+ ",TASK_NO" //任务单编号
    				+ ",TASK_NAME" //任务单名称
    				+ ",TASK_TYPE" //任务单类型
    				+ ",EXEC_SAMLL" //执行小组
    				+ ",PROJECT_ID" //关联项目ID
    				+ ",PROJECT_NO" //项目编号
    				+ ",PROJECT_NAME" //项目名称
    				+ ",SAMPLE_ID" //关联样品ID
    				+ ",SAMPLE_CODE" //样品编号
    				+ ",SAMPLE_NAME" //样品名称
    				+ ",LIB_TYPE" //文库类型
    				+ ",LIB_READY_ID" //关联预文库名称
    				+ ",LIB_READY_CODE" //预文库名称
    				+ ",LIB_ID" //关联终文库名称
    				+ ",LIB_CODE" //文库名称
    				+ ",LIB_READY_BARCORD" //预文库barcord
    				+ ",LIB_INDEX_CODE" //文库index号
    				+ ",LIB_RESULT" //文库结果
    				+ ",LIB_MAN" //实验员
    				+ ",LIB_ASSIGNOR" //指派人
    				+ ",LIB_REAMARK" //文库备注
    				+ ",LIB_BOARD96_ID" //96孔板板ID
    				+ ",LIB_BOARD96_CODE" //96孔板板号
    				+ ",LIB_BOARD96_HOLE" //96孔板孔号
    				+ ",LIB_IS_BOARD" //是否拼板
    				+ ",START_DATETIME" //建库开始时间
    				+ ",CLOSING_DATETIME" //建库结束时间
    				+ ",LIB_VOLUME" //文库体积(ul)
    				+ ",LIB_SAMPLE_USENUM" //样品使用量(ng)
    				+ ",LIB_INSERT_SIZE" //INSERT SIZE
    				+ ",LIB_PH_FLAG" //平衡文库标识
    				+ ",RUN_PLUS_FLAG" //加测标识
					+ ",RERUN_FLAG" //重上机标识
					+ ",LIB_R_D_FLAG" //研发文库标识
					+ ",B_LANE_FLAG" //包LANE标识
					+ ",B_LANE_BATCH_NO" //包LANE批次号
    				+ ") "
    				+ "SELECT"
    				+ "? AS ID" //唯一标识
    				+ ",? AS TPOOL_LIB_ID" //关联预文库混库ID
    				+ ",ID AS LINK_ID" //关联入口表ID
    				+ ",TASK_ID" //关联任务单主表ID
    				+ ",TASK_MX_ID" //关联任务单明细ID
    				+ ",TASK_NO" //任务单编号
    				+ ",TASK_NAME" //任务单名称
    				+ ",TASK_TYPE" //任务单类型
    				+ ",EXEC_SAMLL" //执行小组
    				+ ",PROJECT_ID" //关联项目ID
    				+ ",PROJECT_NO" //项目编号
    				+ ",PROJECT_NAME" //项目名称
    				+ ",SAMPLE_ID" //关联样品ID
    				+ ",SAMPLE_CODE" //样品编号
    				+ ",SAMPLE_NAME" //样品名称
    				+ ",LIB_TYPE" //文库类型
    				+ ",LIB_READY_ID" //关联预文库名称
    				+ ",LIB_READY_CODE" //预文库名称
    				+ ",LIB_ID" //关联终文库名称
    				+ ",LIB_CODE" //文库名称（或终文库）
    				+ ",LIB_READY_BARCORD" //预文库barcord
    				+ ",LIB_INDEX_CODE" //终文库index
    				+ ",LIB_RESULT" //文库结果
    				+ ",LIB_MAN" //实验员
    				+ ",LIB_ASSIGNOR" //指派人
    				+ ",LIB_REAMARK" //文库备注
    				+ ",LIB_BOARD96_ID" //96孔板板ID
    				+ ",LIB_BOARD96_CODE" //96孔板板号
    				+ ",LIB_BOARD96_HOLE" //96孔板孔号
    				+ ",LIB_IS_BOARD" //是否拼板
    				+ ",START_DATETIME" //建库开始时间
    				+ ",CLOSING_DATETIME" //建库结束时间
    				+ ",LIB_VOLUME" //文库体积(ul)
    				+ ",LIB_SAMPLE_USENUM" //样品使用量(ng)
    				+ ",LIB_INSERT_SIZE" //INSERT SIZE
    				+ ",? AS LIB_PH_FLAG" //平衡文库标识
    				+ ",RUN_PLUS_FLAG" //加测标识
					+ ",RERUN_FLAG" //重上机标识
					+ ",LIB_R_D_FLAG" //研发文库标识
					+ ",B_LANE_FLAG" //包LANE标识
					+ ",B_LANE_BATCH_NO" //包LANE批次号
    				+ " FROM BR_MODUAL_BH_T WHERE ID=?";
    		// 更新入口表关联信息
    		String updateSQL = "UPDATE BR_MODUAL_BH_T SET TPOOL_ID=?,TPOOL_MX_ID=?,OBJ_FLAG='归结' WHERE ID=? AND TPOOL_ID IS NULL";
    		
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, TPOOL_LIB_ID);
    		if (count == 1) {
        		// ----------------- 平衡文库预处理
        		// 判断是否平衡文库
        		if (addIndexFlag == 5) {
        			LIB_PH_FLAG = "Y";
        			// 平衡文库需要新复制一份出来,并且记录ID覆盖原提交的ID集合
//        			JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(queryJdbcTemplate);
//        			Map<String, Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_BH_T");
        			List<String> newIds = new ArrayList<String>();
        			for (String id : ids) {
        				Map<String, Object> dataMap = queryJdbcTemplate.queryForMap("SELECT * FROM BR_MODUAL_BH_T WHERE ID=?", id);
        				// 移除标识
        				dataMap.remove("TPOOL_IGNORE_FLAG"); //POOL文库选择忽略标识
        				dataMap.remove("LIB_BALANCE_FLAG");//平衡文库标识
        				dataMap.remove("RUN_PLUS_FLAG");//加测标识
        				dataMap.remove("RERUN_FLAG");//重上机标识
        				dataMap.remove("LIB_R_D_FLAG");//研发文库标识
        				dataMap.remove("B_LANE_FLAG");//包LANE标识
        				dataMap.remove("B_LANE_BATCH_NO");//包LANE批次号
        				// 移除关联字段
        				dataMap.remove("TPOOL_ID");
        				dataMap.remove("TPOOL_MX_ID");
        				// 重置ID字段
        				String newId = SysBasic.getUUID();
        				dataMap.remove("ID");
        				dataMap.put("ID", newId);
        				newIds.add(newId);
        				
        				// 复制新行
//        				SysBasic.filterMap(dataMap, metaMap.keySet());//过滤表不存在的字段
        				SysBasic.insertDataByTableMap(updateJdbcTemplate, "BR_MODUAL_BH_T", dataMap);
        			}
        			// ids 指向新生成的ID
        			ids = newIds;
        		}
//        		// 判断是否包LANE
//        		else if (addIndexFlag == 7) {
//        		}
        		
	    		for (String id : ids) {
	    			String POOL_LIB_MX_ID = SysBasic.getUUID();
	    			int i = updateJdbcTemplate.update(updateSQL, TPOOL_LIB_ID, POOL_LIB_MX_ID, id);
	    			if (i > 0) {
	    				updateJdbcTemplate.update(insertSQL, POOL_LIB_MX_ID, TPOOL_LIB_ID, LIB_PH_FLAG, id);
	    			}
	    		}
    		} else {
    			return new CurrResponseResolve(-1).put("errMsg","状态不是Pooling不能添加明细").put(responseMessageParameter).getData();
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/TGS/addMX", e);
			log.error("/berry/prod/lib/pooling/TGS/addMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 添加明细: 移至暂不排 / 恢复至待排
    @RequestMapping("/addMXToIgnore")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject addMXToIgnore(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String tpoolIgnore = data.getString("tpoolIgnore");
    		if ( "1".equals(tpoolIgnore)
    			|| "2".equals(tpoolIgnore)
    			|| "3".equals(tpoolIgnore)
    			|| "4".equals(tpoolIgnore)
    			|| "5".equals(tpoolIgnore)
    			|| "6".equals(tpoolIgnore)
    			|| "7".equals(tpoolIgnore)
    		) {
    			tpoolIgnore = "Y";
    		} else {
    			tpoolIgnore = "N";
    		}
    		
    		String updateSQL = "UPDATE BR_MODUAL_BH_T SET TPOOL_IGNORE_FLAG='"+tpoolIgnore+"'"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND TPOOL_ID=NULL";
	    	
    		updateJdbcTemplate.update(updateSQL, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/TGS/addMXToIgnore", e);
			log.error("/berry/prod/lib/pooling/TGS/addMXToIgnore", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除明细
    @RequestMapping("/delMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delMX(@RequestBody JSONObject data) {
    	
    	try {
    		String TPOOL_LIB_ID = data.getString("TPOOL_LIB_ID");//混库主单ID
    		List<String> ids = (List<String>) data.get("ids");
    		
    		// 验证主表单状态，判断是否可以添加明细
    		String selectSQL_M = "SELECT COUNT(1) FROM BR_TPOOLING_INFO WHERE ID=? AND TPOOL_LIB_FLAG='Pooling'";
    		// 平衡文库处理: 删除入口表
    		String delSQL_RK_PH = "DELETE FROM BR_MODUAL_BH_T WHERE TPOOL_MX_ID IN ("
    				+ "SELECT ID FROM BR_TPOOLING_MX_INFO WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND LIB_PH_FLAG='Y'"
    				+ ")";
    		// 添加明细数据
    		String delSQL_MX = "DELETE FROM BR_TPOOLING_MX_INFO WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新入口表关联信息
    		String updateSQL_RK = "UPDATE BR_MODUAL_BH_T SET TPOOL_ID=NULL,TPOOL_MX_ID=NULL,OBJ_FLAG=NULL WHERE TPOOL_MX_ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		
    		int count = queryJdbcTemplate.queryForObject(selectSQL_M, Integer.class, TPOOL_LIB_ID);
    		if (count == 1) {
	    		updateJdbcTemplate.update(delSQL_RK_PH, ids.toArray());
	    		updateJdbcTemplate.update(delSQL_MX, ids.toArray());
	    		updateJdbcTemplate.update(updateSQL_RK, ids.toArray());
    		} else {
    			return new CurrResponseResolve(-1).put("errMsg","状态不是Pooling不能删除明细").put(responseMessageParameter).getData();
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/TGS/delMX", e);
			log.error("/berry/prod/lib/pooling/TGS/delMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		//删除主表
    		String delSQL_M = "DELETE FROM BR_TPOOLING_INFO WHERE ID=? AND TPOOL_LIB_FLAG='Pooling'";
    		//平衡文库处理: 删除入口表
    		String delSQL_RK_PH = "DELETE FROM BR_MODUAL_BH_T WHERE TPOOL_MX_ID IN ("
    				+ "SELECT ID FROM BR_TPOOLING_MX_INFO WHERE TPOOL_LIB_ID=? AND LIB_PH_FLAG='Y'"
    				+ ")";
    		//删除明细表
    		String delSQL_MX = "DELETE FROM BR_TPOOLING_MX_INFO WHERE TPOOL_LIB_ID=?";
    		//清空入口表关联关系
    		String updateSQL_RK = "UPDATE BR_MODUAL_BH_T SET TPOOL_ID=NULL,TPOOL_MX_ID=NULL,OBJ_FLAG=NULL WHERE TPOOL_ID=?";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(delSQL_M, id);
    			if (i > 0) {
    				updateJdbcTemplate.update(delSQL_RK_PH, id);
    				updateJdbcTemplate.update(delSQL_MX, id);
    				updateJdbcTemplate.update(updateSQL_RK, id);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/TGS/del", e);
			log.error("/berry/prod/lib/pooling/TGS/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提交到审核
    @RequestMapping("/commitToApprove")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commitToApprove(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_TPOOLING_INFO SET TPOOL_LIB_FLAG='待审核' WHERE ID=? AND TPOOL_LIB_FLAG='Pooling'";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(updateSQL, id);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/TGS/commitToApprove", e);
			log.error("/berry/prod/lib/pooling/TGS/commitToApprove", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 添加/移除: 平衡文库
    @RequestMapping("/balanceLib")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject balanceLib(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String flag = data.getString("flag");
    		if ( !"N".equals(flag) && !"Y".equals(flag) ) {
    			flag = "N";
    		}
    		
    		String updateSQL = "UPDATE BR_MODUAL_BH_T SET LIB_BALANCE_FLAG='"+flag+"'"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
	    	
    		updateJdbcTemplate.update(updateSQL, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/TGS/balanceLib", e);
			log.error("/berry/prod/lib/pooling/TGS/balanceLib", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 获取研发文库导入模板
    @RequestMapping("/RD/importTemplate")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject getRDImportTemplate(@RequestBody JSONObject data) {
    	
    	try {
    		String templateName = data.getString("templateName");

    		String selectSQL = "select TEMPLATE_FILE from RECORD_TEMPLATE where TEMPLATE_CLASS='导入模板' and TEMPLATE_STATUS='启用' and TEMPLATE_NAME=?";
    		
    		List<String> templateFileList = queryJdbcTemplate.queryForList(selectSQL, String.class, templateName);
    		
    		if (templateFileList!=null && templateFileList.size()>0) {
        		
    	    	return new CurrResponseResolve(1).put("templateFile", templateFileList.get(0)).put(responseMessageParameter).getData();
    			
    		} else {
        		
    	    	return new CurrResponseResolve(0).put(responseMessageParameter).getData();
    		}
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/TGS/RD/importTemplate", e);
			log.error("/berry/prod/lib/pooling/TGS/RD/importTemplate", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 研发文库删除
    @RequestMapping("/RD/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delRDLib(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String deleteSQL = "delete from BR_MODUAL_BH_T where ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")"
    						 + " and LIB_R_D_FLAG='Y' and TPOOL_ID IS NULL";
    		
    		updateJdbcTemplate.update(deleteSQL, ids.toArray());
    		
    	    return new CurrResponseResolve(1).put(responseMessageParameter).getData();
    		
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/pooling/TGS/RD/del", e);
			log.error("/berry/prod/lib/pooling/TGS/RD/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 研发文库导入
    @RequestMapping("/RD/import")
    @Transactional(rollbackFor=Exception.class)
    public JSONObject importRDLib(@RequestBody Map<String,Object> map) {

        int code=0;
        String message = "";
        
        String tableName = "BR_MODUAL_BH_T";
        String[] titleNameArray = { "项目编号", "任单编号", "样品编号", "样品名称", 
        							"文库名称", "子文库名称", "矫正后摩尔浓度(nM)", "片段长度(bp)", 
        							"数据量", "INDEX", "INDEX序列", "文库类型" 
        						};
        String[] fieldNameArray = { "PROJECT_NO", "TASK_NO", "SAMPLE_CODE", "SAMPLE_NAME", 
        							 "LIB_CODE", "LIB_NAME", "TPOOL_LIB_MD", "TPOOL_LIB_SIZE", 
        							 "DATA_NUM", "LIB_INDEX_CODE", "LIB_INDEX_SEQ", "LIB_TYPE" 
        						};
        String[] fieldTypeArray = { "string", "string", "string", "string", 
        							 "string", "string", "number", "number", 
        							 "number", "string", "string", "string" 
        						};

        String infoValue = SysBasic.toTranStringByObject(map.get("info"));
        infoValue = infoValue.replaceAll("\r", "");//清除 \r
        String[] splitiInfoValueArray = infoValue.split("\n");    //根据换行符\n区分一条数据
        
        List<Map<String, Object>> dataMapList = new ArrayList<Map<String, Object>>();
        
        for(int i=0;i<splitiInfoValueArray.length;i++){
        	String splitiInfoValue = splitiInfoValueArray[i];
        	splitiInfoValue = splitiInfoValue==null?"":splitiInfoValue.trim();
        	if (splitiInfoValue.length()==0) {//空行忽略
                continue;
        	}
            String[] splitCol = splitiInfoValue.split("\t");     //按空格划分
            int num = splitCol.length;
            if(num < 12){
                message += "第"+(i+1)+"行列数不足;";
                continue;
            }
            
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("ID", SysBasic.getUUID());
            dataMap.put("LIB_R_D_FLAG", "Y");
            
            for (int z=0; z<fieldNameArray.length; z++) {
            	
            	String colVal = splitCol[z];
            	colVal = colVal==null?"":colVal.trim();
            	
            	// 数据格式预处理
            	if ("number".equalsIgnoreCase(fieldTypeArray[z])) {
            		colVal = colVal.length()==0?"0" : colVal;
            	}
            	
            	// 数据存入值
            	if ("number".equalsIgnoreCase(fieldTypeArray[z]) && !SysBasic.isNumeric(colVal)) {
            		message += "第"+(i+1)+"行"+titleNameArray[z]+"不是数字类型;";
            	} else if ("number".equalsIgnoreCase(fieldTypeArray[z])) {
            		dataMap.put( fieldNameArray[z] , Double.valueOf(colVal) );
            	} else {
            		dataMap.put( fieldNameArray[z] , colVal);
            	}
            }
            
            dataMapList.add(dataMap);
        }
        
        if (message!=null && message.length()>0) {
        	return new CurrResponseResolve(0).put(responseMessageParameter).put("message",message).getData();
        }
        
        for (Map<String, Object> dataMap : dataMapList) {
        	code = SysBasic.insertDataByTableMap(updateJdbcTemplate, tableName, dataMap);
        	if (code<1) {
        		break;
        	}
        }
        if(code==1){
        	message = "导入成功";
        }else{
        	message = "导入失败";
            code = -1;
            SysBasic.rollBack();
        }
        return new CurrResponseResolve(code).put(responseMessageParameter).put("message",message).getData();
    }
}
