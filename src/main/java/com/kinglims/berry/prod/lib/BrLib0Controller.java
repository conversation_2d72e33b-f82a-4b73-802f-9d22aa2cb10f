package com.kinglims.berry.prod.lib;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/lib")
@Slf4j
public class BrLib0Controller {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 批量修改
    @RequestMapping("/editBatch")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject editBatch(@RequestBody JSONObject data) {
    	
    	try {
    		String IDS = data.getString("IDS");
    		String[] idArray = IDS.split(",");
    		
    		// 处理日期时间格式
    		if (data.containsKey("START_DATETIME")) {//建库开始时间
    			data.put("START_DATETIME", data.getDate("START_DATETIME") );
    		}
    		if (data.containsKey("CLOSING_DATETIME")) {//建库结束时间
    			data.put("CLOSING_DATETIME", data.getDate("CLOSING_DATETIME") );
    		}
    		
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(updateJdbcTemplate);
    		Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName("BR_LIB_INFO");
    		
    		SysBasic.filterMap(data, metaMap.keySet());//过滤表不存在的字段
    		
    		for (String id : idArray) {
    			data.put("ID", id);
    			SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_LIB_INFO", data);
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/editBatch", e);
			log.error("/berry/prod/lib/editBatch", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 批量改派
    @RequestMapping("/editAssign")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject editAssign(@RequestBody JSONObject data) {
    	
    	try {
    		String IDS = data.getString("IDS");
    		String[] ids = IDS.split(",");
    		
    		String LIB_MAN_ID = data.getString("LIB_MAN_ID");
    		String LIB_MAN = data.getString("LIB_MAN");
    		String LIB_ASSIGNOR_ID = data.getString("LIB_ASSIGNOR_ID");
    		String LIB_ASSIGNOR = data.getString("LIB_ASSIGNOR");
    		
    		String updateSQL = "UPDATE BR_LIB_INFO"
    				+ " SET LIB_MAN_ID=?, LIB_MAN=?, LIB_ASSIGNOR_ID=?, LIB_ASSIGNOR=?"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.length)+") AND LIB_FLAG='构建中'";
    		
    		Object[] params = new Object[ ids.length + 4 ];
    		params[0] = LIB_MAN_ID;
    		params[1] = LIB_MAN;
    		params[2] = LIB_ASSIGNOR_ID;
    		params[3] = LIB_ASSIGNOR;
    		for (int i = 0; i < ids.length; i++) {
    			params[ 4 + i ] = ids[i];
    		}
    		updateJdbcTemplate.update(updateSQL, params);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/editBatch", e);
			log.error("/berry/prod/lib/editBatch", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
