package com.kinglims.berry.prod.lib;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/lib/libTGS")
@Slf4j
public class BrLib5TGSController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // TGS文库: 选择添加 
    @RequestMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject add(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String IDS = data.getString("IDS");
    		if ( IDS != null && IDS.length() > 0 ) {
    			String[] idsArray = IDS.split(",");
    			ids = new ArrayList<String>();
    			for (String id : idsArray) {
    				ids.add( id );
    			}
    		}
    		
    		String LIB_MAN_ID = data.getString("LIB_MAN_ID");
    		String LIB_MAN = data.getString("LIB_MAN");
    		String LIB_ASSIGNOR_ID = data.getString("LIB_ASSIGNOR_ID");
    		String LIB_ASSIGNOR = data.getString("LIB_ASSIGNOR");
    		
    		String insertSQL = "INSERT INTO BR_LIB_INFO ("
    				+ "ID" //唯一标识
    				+ ",LINK_ID" //关联入口表ID
    				+ ",TASK_ID" //关联任务单主表ID
    				+ ",TASK_MX_ID" //关联任务单明细ID
    				+ ",TASK_NO" //任务单编号
    				+ ",TASK_NAME" //任务单名称
    				+ ",TASK_TYPE" //任务单类型
    				+ ",EXEC_SAMLL" //执行小组
    				+ ",PROJECT_ID" //关联项目ID
    				+ ",PROJECT_NO" //项目编号
    				+ ",PROJECT_NAME" //项目名称
    				+ ",SAMPLE_ID" //关联样品ID
    				+ ",SAMPLE_CODE" //样品编号
    				+ ",SAMPLE_NAME" //样品名称
    				+ ",DNA_RNA_ID" //关联核酸QC ID
    				+ ",DNA_RNA_CODE" //核酸编号
    				+ ",LIB_CODE" //文库名称
    				+ ",LIB_TYPE" //文库类型
//    				+ ",LIB_BARCODE_CODE" //barcode号
    				+ ",LIB_RESULT" //文库结果
    				+ ",LIB_REAMARK" //文库备注
    				+ ",LIB_TYPE_SYS" //系统文库类型识别
    				+ ",LIB_NEXT" //下一步质控
    				+ ",LIB_BOARD96_ID" //关联96孔板ID
    				+ ",LIB_BOARD96_CODE" //96孔板板号
    				+ ",LIB_BOARD96_HOLE" //96孔板孔号
    				+ ",LIB_IS_BOARD" //是否拼板
//    				+ ",SEQ_PLATFORM" //测序平台
//    				+ ",LIB_INDEX_CODE" //index号
//    				+ ",LIB_ASSIGNOR" //指派人
//    				+ ",LIB_CODE_Y" //终文库名称
    				+ ",LIB_FLAG" //文库状态
//    				+ ",DATA_NUM" //数据量(G)
    				+ ",START_DATETIME" //建库开始时间
    				+ ",CLOSING_DATETIME" //建库结束时间
    				+ ",LIB_SAMPLE_USENUM" //样品使用量(ng)
//    				+ ",LIB_VOLUME" //文库体积(ul)(没有该字段)
//    				+ ",LIB_QJ_SIZE" //文库切胶长度
//    				+ ",LIB_SIZE" //文库大小(bp)
//    				+ ",LIB_INSERT_SIZE" //INSERT SIZE
//    				+ ",LIB_PART_SIZE" //片段长度(bp)
//    				+ ",LIB_T_FROM_SIZE" //from(bp)
//    				+ ",LIB_T_TO_SIZE" //to(bp)
//    				+ ",LIB_QUBIT_ND" //qubit浓度
//    				+ ",LIB_T_HS_ND" //换算浓度
//    				+ ",LIB_ZL" //文库总量
//    				+ ",LIB_CREATOR" //文库构建-创建人
//    				+ ",LIB_CREATTIME" //文库构建-创建时间
//    				+ ",LIB_LASTUPDATOR" //文库构建-最近修改人
//    				+ ",LIB_LASTUPDATETIME" //文库构建-最近修改时间
//    				+ ",LOGINCOMPANY" //账套
    				+ ",LIB_MAN_ID" //实验员ID
    				+ ",LIB_MAN" //实验员
    				+ ",LIB_ASSIGNOR_ID" //分派员ID
    				+ ",LIB_ASSIGNOR" //分派员
    				+ ") "
    				+ "SELECT"
    				+ "? AS ID" //唯一标识
    				+ ",ID AS LINK_ID" //关联入口表ID
    				+ ",TASK_ID" //关联任务单主表ID
    				+ ",TASK_MX_ID" //关联任务单明细ID
    				+ ",TASK_NO" //任务单编号
    				+ ",TASK_NAME" //任务单名称
    				+ ",TASK_TYPE" //任务单类型
    				+ ",EXEC_SAMLL" //执行小组
    				+ ",PROJECT_ID" //关联项目ID
    				+ ",PROJECT_NO" //项目编号
    				+ ",PROJECT_NAME" //项目名称
    				+ ",SAMPLE_ID" //关联样品ID
    				+ ",SAMPLE_CODE" //样品编号
    				+ ",SAMPLE_NAME" //样品名称
    				+ ",DNA_RNA_ID" //关联核酸QC ID
    				+ ",DNA_RNA_CODE" //核酸编号
    				+ ",LIB_CODE" //文库名称
    				+ ",LIB_TYPE" //文库类型
//    				+ ",LIB_BARCODE_CODE" //barcode号(没有改字段)
    				+ ",LIB_RESULT" //文库结果
    				+ ",LIB_REAMARK" //文库备注
    				+ ",'TGS文库' AS LIB_TYPE_SYS" //系统文库类型识别（预文库/终文库/捕获预文库/Capture Pooing终文库/NGS文库/TGS文库/bionano文库/pooling文库）
    				+ ",LIB_NEXT" //下一步质控
    				+ ",LIB_BOARD96_ID" //关联96孔板ID
    				+ ",LIB_BOARD96_CODE" //96孔板板号
    				+ ",LIB_BOARD96_HOLE" //96孔板孔号
    				+ ",LIB_IS_BOARD" //是否拼板
    				+ ",'构建中' AS LIB_FLAG" //文库状态
    				+ ",START_DATETIME" //建库开始时间
    				+ ",CLOSING_DATETIME" //建库结束时间
    				+ ",LIB_SAMPLE_USENUM" //样品使用量(ng)
//    				+ ",LIB_VOLUME" //文库体积(ul)(没有该字段)
//    				+ ",LIB_SIZE" //文库大小(bp)(没有该字段)
//    				+ ",LIB_INSERT_SIZE" //INSERT SIZE(没有该字段)
//    				+ ",LIB_QUBIT_ND" //qubit浓度(没有该字段)
//    				+ ",LIB_ZL" //文库总量(没有该字段)
//    				+ ",LIB_CREATOR" //文库构建-创建人
//    				+ ",LIB_CREATTIME" //文库构建-创建时间
//    				+ ",LIB_LASTUPDATOR" //文库构建-最近修改人
//    				+ ",LIB_LASTUPDATETIME" //文库构建-最近修改时间
//    				+ ",'' AS LOGINCOMPANY" //账套
//    				+ ",FLOW_PLAN_ID" //流程唯一标识
    				+ ",?" //实验员ID
    				+ ",?" //实验员
    				+ ",?" //分派员ID
    				+ ",?" //分派员
    				+ " FROM BR_MODUAL_LIB_T WHERE ID=?";
    		String updateSQL = "UPDATE BR_MODUAL_LIB_T SET LIB_T_ID=?,OBJ_FLAG='归结' WHERE ID=? AND LIB_T_ID IS NULL";
    		for (String id : ids) {
    			String libId = SysBasic.getUUID();
    			int i = updateJdbcTemplate.update(updateSQL, libId, id);
    			if (i > 0) {
    				updateJdbcTemplate.update(insertSQL, libId, LIB_MAN_ID, LIB_MAN, LIB_ASSIGNOR_ID, LIB_ASSIGNOR, id);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/libTGS/add", e);
			log.error("/berry/prod/lib/libTGS/add", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // TGS文库: 提交
    @RequestMapping("/commit")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commit(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String updateSQL = "UPDATE BR_LIB_INFO SET LIB_FLAG='已提交' WHERE ID=? AND LIB_FLAG='构建中'";
    		
    		String selectSQL = "SELECT * FROM BR_LIB_INFO WHERE ID=?";
    		
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(updateJdbcTemplate);
    		Map<String,Integer> metaMap=jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_LIB_T");
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(updateSQL, id);
    			if (i > 0) {
    				// 更新预处理结果到入口表
    				Map<String, Object> resultMap = updateJdbcTemplate.queryForMap(selectSQL, id);
    				resultMap.put("ID", resultMap.get("LINK_ID"));
    				SysBasic.filterMap(resultMap, metaMap.keySet());
    				SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_LIB_T", resultMap);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/libReady/commit", e);
			log.error("/berry/prod/lib/libReady/commit", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // TGS文库: 删除
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String delSQL = "DELETE FROM BR_LIB_INFO WHERE ID=? AND LIB_FLAG='构建中'";
    		String updateSQL = "UPDATE BR_MODUAL_LIB_T SET LIB_T_ID=NULL,OBJ_FLAG=NULL WHERE LIB_T_ID=?";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(delSQL, id);
    			if (i > 0) {
    				updateJdbcTemplate.update(updateSQL, id);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/lib/libTGS/del", e);
			log.error("/berry/prod/lib/libTGS/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
