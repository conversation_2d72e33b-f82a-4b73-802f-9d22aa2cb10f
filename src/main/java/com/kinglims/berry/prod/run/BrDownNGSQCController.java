package com.kinglims.berry.prod.run;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.system.workflow.handler.logic.Modulars;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowMessage;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowUtils;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/run/downNGSQC")
@Slf4j
public class BrDownNGSQCController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private WorkflowUtils workflowUtils;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 提交
    @RequestMapping("/commit")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commit(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> NGS_FLOWCELL_LIST = (List<String>) data.get("NGS_FLOWCELL_LIST");
    		
        	String selectSQL_IDS = "SELECT ID FROM BR_MODUAL_NGS_DATAQC WHERE NGS_FLOWCELL=? AND FLOW_COMMIT_FLAG='未提交' ";
        	
        	String updateSQL_FLAG = "UPDATE BR_MODUAL_NGS_DATAQC SET FLOW_COMMIT_FLAG='已提交' WHERE NGS_FLOWCELL=? AND FLOW_COMMIT_FLAG='未提交' ";
        	
        	for (String NGS_FLOWCELL : NGS_FLOWCELL_LIST) {
				List<String> ids = queryJdbcTemplate.queryForList(selectSQL_IDS, String.class, NGS_FLOWCELL);
	    		if (ids!=null && ids.size()>0) {
	    			WorkflowMessage wflowMsg = workflowUtils.set(updateJdbcTemplate,updateJdbcTemplate).submit(Modulars.NGSDATAQC, ids);
	    			if (wflowMsg.isB()) {
	    				updateJdbcTemplate.update(updateSQL_FLAG, NGS_FLOWCELL);
	    			}
	    		}
	    	}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/run/downNGSQC/commit", e);
			log.error("/berry/prod/run/downNGSQC/commit", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 撤回方案: 下机 NGS QC
    @RequestMapping("/revoke")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeDownNGSFlow(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> NGS_FLOWCELL_LIST = (List<String>) data.get("NGS_FLOWCELL_LIST");
    		
        	String selectSQL_IDS = "SELECT ID FROM BR_MODUAL_NGS_DATAQC WHERE NGS_FLOWCELL=? AND FLOW_COMMIT_FLAG='已提交' ";
        	
        	String updateSQL_FLAG = "UPDATE BR_MODUAL_NGS_DATAQC SET FLOW_COMMIT_FLAG='未提交' WHERE NGS_FLOWCELL=? AND FLOW_COMMIT_FLAG='已提交' ";
        	
        	for (String NGS_FLOWCELL : NGS_FLOWCELL_LIST) {
				List<String> ids = queryJdbcTemplate.queryForList(selectSQL_IDS, String.class, NGS_FLOWCELL);
	    		if (ids!=null && ids.size()>0) {
	    			WorkflowMessage wflowMsg = workflowUtils.set(updateJdbcTemplate,updateJdbcTemplate).revoke(Modulars.NGSDATAQC, ids);
	    			if (wflowMsg.isB()) {
	    				updateJdbcTemplate.update(updateSQL_FLAG, NGS_FLOWCELL);
	    			}
	    		}
	    	}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/run/downNGSQC/revoke", e);
			log.error("/berry/prod/run/downNGSQC/revoke", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
