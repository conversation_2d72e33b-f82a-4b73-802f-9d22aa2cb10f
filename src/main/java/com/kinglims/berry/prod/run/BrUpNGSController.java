package com.kinglims.berry.prod.run;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.system.workflow.handler.logic.Modulars;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowMessage;
import com.kinglims.framework.common.system.workflow.handler.logic.WorkflowUtils;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/run/upNGS")
@Slf4j
public class BrUpNGSController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    @Autowired
    private WorkflowUtils workflowUtils;
    
    // 选择添加明细 
    @RequestMapping("/addMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject addMX(@RequestBody JSONObject data) {
    	
    	try {
    		String BOARDING_ID = data.getString("BOARDING_ID");//混库主单ID
    		JSONArray rData = data.getJSONArray("rData");
    		
    		// 验证主表单状态，判断是否可以添加明细
    		String selectSQL = "SELECT COUNT(1) FROM BR_NGS_BOARDING_INFO WHERE ID=? AND NGS_FLAG='方案制定'";
    		// 添加明细数据
    		String insertSQL = "INSERT INTO BR_2BOARDING_MX_INFO ("
    				+ "ID" //唯一标识
    				+ ",BOARDING_ID" //关联上机方案主表ID
    				+ ",LINK_ID" //关联入口表ID
    				+ ",LANE_NO" //lane编号
    				+ ",POOL_LIB_ID" //关联文库ID
    				+ ",POOL_LIB_CODE" //混合后文库编号
    				+ ",LANE_REMARK" //备注
    				+ ") "
    				+ "SELECT"
    				+ " ? AS ID" //唯一标识
    				+ ",? AS BOARDING_ID" //关联上机方案主表ID
    				+ ",ID AS LINK_ID" //关联入口表ID
    				+ ",LANE_NO" //lane编号
    				+ ",POOL_LIB_ID" //关联文库ID
    				+ ",POOL_LIB_CODE" //混合后文库编号
    				+ ",LANE_REMARK" //备注
    				+ " FROM BR_MODUAL_NGS WHERE ID=?";
    		// 更新入口表关联信息
    		String updateSQL = "UPDATE BR_MODUAL_NGS SET NGS_ID=?,NGS_MX_ID=?,OBJ_FLAG='归结' WHERE POOL_LIB_CODE=? AND NGS_ID IS NULL";
    		
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, BOARDING_ID);
    		if (count == 1) {
	    		for (int i=0; i<rData.size(); i++) {
	    			JSONObject r = rData.getJSONObject(i);
	    			String ID = r.getString("ID");
	    			String POOL_LIB_CODE = r.getString("POOL_LIB_CODE");
	    			
	    			String NGS_MX_ID = SysBasic.getUUID();
	    			int nextFlag = updateJdbcTemplate.update(updateSQL, BOARDING_ID, NGS_MX_ID, POOL_LIB_CODE);
	    			if (nextFlag > 0) {
	    				updateJdbcTemplate.update(insertSQL, NGS_MX_ID, BOARDING_ID, ID);
	    			}
	    		}
    		} else {
    			return new CurrResponseResolve(-1).put("errMsg","状态不是进行中不能添加明细").put(responseMessageParameter).getData();
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/run/upNGS/addMX", e);
			log.error("/berry/prod/run/upNGS/addMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除明细 
    @RequestMapping("/delMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delMX(@RequestBody JSONObject data) {
    	
    	try {
    		String BOARDING_ID = data.getString("BOARDING_ID");//混库主单ID
    		List<String> ids = (List<String>) data.get("ids");
    		
    		// 验证主表单状态，判断是否可以添加明细
    		String selectSQL = "SELECT COUNT(1) FROM BR_NGS_BOARDING_INFO WHERE ID=? AND NGS_FLAG='方案制定'";
    		// 添加明细数据
    		String delSQL = "DELETE FROM BR_2BOARDING_MX_INFO WHERE ID=?";
    		// 更新入口表关联信息
    		String updateSQL = "UPDATE BR_MODUAL_NGS SET NGS_ID=NULL,NGS_MX_ID=NULL,OBJ_FLAG=NULL WHERE NGS_MX_ID=?";
    		
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, BOARDING_ID);
    		if (count == 1) {
	    		for (String id : ids) {
	    			updateJdbcTemplate.update(delSQL, id);
	    			updateJdbcTemplate.update(updateSQL, id);
	    		}
    		} else {
    			return new CurrResponseResolve(-1).put("errMsg","状态不是进行中不能删除明细").put(responseMessageParameter).getData();
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/run/upNGS/delMX", e);
			log.error("/berry/prod/run/upNGS/delMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 删除
    @RequestMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject del(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String delSQL_M = "DELETE FROM BR_NGS_BOARDING_INFO WHERE ID=? AND NGS_FLAG='方案制定'";
    		String delSQL_D = "DELETE FROM BR_2BOARDING_MX_INFO WHERE BOARDING_ID=?";
    		String updateSQL_RKB = "UPDATE BR_MODUAL_NGS SET NGS_ID=NULL,NGS_MX_ID=NULL,OBJ_FLAG=NULL WHERE NGS_ID=?";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(delSQL_M, id);
    			if (i > 0) {
    				updateJdbcTemplate.update(delSQL_D, id);
    				updateJdbcTemplate.update(updateSQL_RKB, id);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/run/upNGS/del", e);
			log.error("/berry/prod/run/upNGS/del", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提交上机方案
    @RequestMapping("/commitToRuning")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commitToRuning(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String updateSQL = "UPDATE BR_NGS_BOARDING_INFO SET NGS_FLAG='进行中' WHERE ID=? AND NGS_FLAG='方案制定'";
    		String selectSQL_M = "SELECT * FROM BR_NGS_BOARDING_INFO WHERE ID=?";
    		String selectSQL_MX = "SELECT * FROM BR_2BOARDING_MX_INFO WHERE BOARDING_ID=?";
    		String selectSQL_RK = "SELECT * FROM BR_MODUAL_NGS WHERE NGS_MX_ID=?";
    		
    		//入口表: 分组 : TO CJ_M
    		String selectSQL_RK_GROUP = "SELECT"
						    		+ " MAX(NGS_FLOWCELL) AS FLOWCELL"
						    		+ ",MAX(COMPANY_ID) AS COMPANY_ID" //客户单位ID
						    		+ ",MAX(COMPANY_CODE) AS COMPANY_CODE" //客户单位编号
						    		+ ",MAX(COMPANY_NAME) AS COMPANY_NAME" //客户单位名称
						    		+ ",PROJECT_ID" //项目ID
						    		+ ",MAX(PROJECT_NO) AS PROJECT_NO" //项目编号
						    		+ ",MAX(PROJECT_NAME) AS PROJECT_NAME" //项目名称
						    		+ ",MAX(PROJECT_TYPE) AS PROJECT_TYPE" //项目类型
						    		+ ",'待发送' AS MIS_FLAG"
						    		+ ",'NGS' AS RUN_TYPE"
						    		+ " FROM BR_MODUAL_NGS"
						    		+ " WHERE NGS_ID=?"
						    		+ " GROUP BY PROJECT_ID";
    		//入口表: BY ID : TO CJ_MX (非文库样本)
    		String selectSQL_RK_CJ_MX_1 = "INSERT INTO BR_INFO_ANALYZE_COLLECT_MX (ID,M_ID,LINK_ID,SAMPLE_ID,SAMPLE_CODE,SAMPLE_NAME,SAMPLE_TYPE,RUN_TYPE)"
    								+ " SELECT"
				    				+ " RAWTOHEX( SYS_GUID() ) AS ID"
				    				+ ",? AS M_ID"
				    				+ ",M.ID AS LINK_ID"
				    				+ ",M.SAMPLE_ID" // 样本ID
				    				+ ",M.SAMPLE_CODE" // 样本编号
				    				+ ",M.SAMPLE_NAME" // 样本名称
				    				+ ",M.SAMPLE_TYPE" // 样本类型
				    				+ ",'NGS' AS RUN_TYPE"
				    				+ " FROM BR_PROJECT_MX_INFO M"
				    				+ " WHERE M.PROJECT_ID=? AND (M.SAMPLE_TYPE<>'文库' OR M.SAMPLE_TYPE IS NULL)";
    		//入口表: BY ID : TO CJ_MX (文库样本) - 拆分样本
    		String selectSQL_RK_CJ_MX_2 = "INSERT INTO BR_INFO_ANALYZE_COLLECT_MX (ID,M_ID,LINK_ID,SAMPLE_ID,SAMPLE_CODE,SAMPLE_NAME,SAMPLE_TYPE,RUN_TYPE)"
									+ " SELECT"
				    				+ " RAWTOHEX( SYS_GUID() ) AS ID"
				    				+ ",? AS M_ID"
				    				+ ",M.ID AS LINK_ID"
				    				+ ",M.SAMPLE_ID" // 样本ID
				    				+ ",M.SAMPLE_CODE" // 样本编号
				    				+ ",S.LIB_NAME" // 样本名称
				    				+ ",M.SAMPLE_TYPE" // 样本类型
				    				+ ",'NGS' AS RUN_TYPE"
				    				+ " FROM BR_PROJECT_MX_INFO M LEFT JOIN BR_SAMPLE_INFO_DETAIL_LIB S ON M.SAMPLE_ID=S.SAMPLE_ID"
				    				+ " WHERE M.PROJECT_ID=? AND M.SAMPLE_TYPE='文库'";
    		
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(queryJdbcTemplate);
    		// 入口表结构
    		Map<String,Integer> metaMap_RK = jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_NGS");
    		// 采集单主表结构
    		Map<String,Integer> metaMap_CJ_M = jdbcTemplateUtils.queryMetaDataByTableName("BR_INFO_ANALYZE_COLLECT");
    		
    		// 缓存: 项目类型 与 信息采集单模板 的关系
    		String selectSQL_projectType = "SELECT ITEM_TEXT, FIELD_C FROM BR_DATA_DICT WHERE CODE='PROJECT_TYPE' AND IS_VALID='Y'";
    		List<Map<String, Object>> projectTypeList = queryJdbcTemplate.queryForList(selectSQL_projectType);
    		Map<Object, Object> cjModelMap = new HashMap<Object, Object>();
    		if (projectTypeList !=null) {
    			for (Map<String, Object> projectType : projectTypeList) {
    				cjModelMap.put( projectType.get("ITEM_TEXT") ,  projectType.get("FIELD_C") );
    			}
    		}
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(updateSQL, id);
    			if (i>0) {
    				Map<String, Object> M_map = updateJdbcTemplate.queryForMap(selectSQL_M, id);
    				List<Map<String, Object>> MX_map_List = updateJdbcTemplate.queryForList(selectSQL_MX, id);
    				for (Map<String, Object> MX_map : MX_map_List) {
    					Object NGS_MX_ID = MX_map.get("ID");
    					List<Map<String, Object>> RK_map_List = updateJdbcTemplate.queryForList(selectSQL_RK, NGS_MX_ID);
    					for (Map<String, Object> RK_map : RK_map_List) {
    						M_map.remove("ID");
    						MX_map.remove("ID");
    						RK_map.putAll(M_map);
    						RK_map.putAll(MX_map);
    						SysBasic.filterMap(RK_map, metaMap_RK.keySet());//过滤表不存在的字段
    						SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_MODUAL_NGS", RK_map);//更新
    					}
    				}
    				
    				//分组入口表
    				List<Map<String, Object>> RK_GROUP_LIST = updateJdbcTemplate.queryForList(selectSQL_RK_GROUP, id);
    				for (Map<String, Object> RK_GROUP : RK_GROUP_LIST) {
    					Object PROJECT_ID = RK_GROUP.get("PROJECT_ID");// 项目ID
    					if (PROJECT_ID == null) {// 项目ID为空，直接跳过
    						continue;
    					}
    					Object PROJECT_TYPE = RK_GROUP.get("PROJECT_TYPE");// 项目类型
	    	    		//插入采集单主表
    					String CJ_M_ID = SysBasic.getUUID();
    					RK_GROUP.put("ID", CJ_M_ID);
    					RK_GROUP.put("COLLECT_TYPE", cjModelMap.get(PROJECT_TYPE));//采集单类型-模板
    					SysBasic.filterMap(RK_GROUP, metaMap_CJ_M.keySet());//过滤表不存在的字段
						SysBasic.insertDataByTableMap(updateJdbcTemplate, "BR_INFO_ANALYZE_COLLECT", RK_GROUP);//插入采集单主表
	    	    		//插入采集单明细表
						updateJdbcTemplate.update(selectSQL_RK_CJ_MX_1, CJ_M_ID, PROJECT_ID);
						updateJdbcTemplate.update(selectSQL_RK_CJ_MX_2, CJ_M_ID, PROJECT_ID);
    				}
    			}
    		}
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			SysBasic.rollBack();
			log.info("/berry/prod/run/upTGS/commitToRuning", e);
			log.error("/berry/prod/run/upTGS/commitToRuning", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 提交流程方案
    @RequestMapping("/commitFlow")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commitFlow(@RequestBody JSONObject data) {
    	
    	try {
    		int rsCode = -1;
    		
    		List<String> ids = (List<String>) data.get("ids");
    		
    		List<String> rkIDList = new ArrayList<String>();
    		
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(queryJdbcTemplate);
    		// 入口表表名
        	String BR_MODUAL_tableName = "BR_MODUAL_NGS";
    		// 入口表结构
    		Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName(BR_MODUAL_tableName);
    		
    		String updateSQL = "UPDATE BR_NGS_BOARDING_INFO SET NGS_FLAG='上机完成' WHERE ID=? AND NGS_FLAG='进行中'";
    		String selectSQL_M = "SELECT * FROM BR_NGS_BOARDING_INFO WHERE ID=?";
    		String selectSQL_MX = "SELECT * FROM BR_2BOARDING_MX_INFO WHERE BOARDING_ID=?";
    		String selectSQL_RK = "SELECT * FROM "+BR_MODUAL_tableName+" WHERE NGS_MX_ID=?";
    		
    		for (String id : ids) {
    			int i = updateJdbcTemplate.update(updateSQL, id);
    			if (i>0) {
    				Map<String, Object> M_map = updateJdbcTemplate.queryForMap(selectSQL_M, id);
    				List<Map<String, Object>> MX_map_List = updateJdbcTemplate.queryForList(selectSQL_MX, id);
    				for (Map<String, Object> MX_map : MX_map_List) {
    					Object NGS_MX_ID = MX_map.get("ID");
    					List<Map<String, Object>> RK_map_List = updateJdbcTemplate.queryForList(selectSQL_RK, NGS_MX_ID);
    					for (Map<String, Object> RK_map : RK_map_List) {
    						String RK_ID = (String) RK_map.get("ID");
    						
    						M_map.remove("ID");
    						MX_map.remove("ID");
    						RK_map.putAll(M_map);
    						RK_map.putAll(MX_map);
    						
    				    	// 判断是否变更新方案
    				    	Object FLOW_PLAN_ID_NEW = RK_map.get("FLOW_PLAN_ID_NEW");//任务明细:新方案ID
    				    	Object FLOW_PLAN_ID = RK_map.get("FLOW_PLAN_ID");//模块表:原方案ID
    				    	if (FLOW_PLAN_ID_NEW != null && !(FLOW_PLAN_ID_NEW+"").equals(FLOW_PLAN_ID)) {
    				    		RK_map.put("FLOW_PLAN_ID_FORMER", FLOW_PLAN_ID);//模块表:方案ID -> 模块表:原方案ID
    				    		RK_map.put("FLOW_PLAN_ID", FLOW_PLAN_ID_NEW);//任务明细:新方案ID -> 模块表:方案ID
    				    	}
    				    	
    						SysBasic.filterMap(RK_map, metaMap.keySet());//过滤表不存在的字段
    						SysBasic.updateDataByTableMap(updateJdbcTemplate, BR_MODUAL_tableName, RK_map);//更新
    						
    						rkIDList.add(RK_ID);
    					}
    				}
    			}
    		}
		    if (rkIDList.size() > 0) {
		    	WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).submit(Modulars.NGS, rkIDList);
    			if  (wflowMsg.isB()) {
    				rsCode = 1;
    			} else {
    				SysBasic.rollBack();
    			}
		    }
	    	return new CurrResponseResolve(rsCode).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/run/upTGS/commitFlow", e);
			log.error("/berry/prod/run/upTGS/commitFlow", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 撤回方案: 上机 NGS
    @RequestMapping("/revokeFlow")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeFlow(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String sqlUpdate = "UPDATE BR_NGS_BOARDING_INFO SET NGS_FLAG='进行中' WHERE ID=? and NGS_FLAG='上机完成'";
    		String sqlSELECT_RK_idList = "SELECT ID FROM BR_MODUAL_NGS WHERE NGS_ID=?";
    		for (String id : ids) {
				// 查询入口表 idList
				List<String> idList = updateJdbcTemplate.queryForList(sqlSELECT_RK_idList, String.class, id);
				// 撤回流程
				WorkflowMessage wflowMsg = workflowUtils.set(queryJdbcTemplate,updateJdbcTemplate).revoke(Modulars.NGS, idList);
				if (wflowMsg.isB()) {
					updateJdbcTemplate.update(sqlUpdate, id);// 更新状态
				} else {
					SysBasic.rollBack();
					break;
				}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/run/upNGS/revokeFlow", e);
			log.error("/berry/prod/run/upNGS/revokeFlow", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    
    // 提交上机异常
    @RequestMapping("/commitToRunERR")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject commitToRunERR(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String updateSQL = "UPDATE BR_NGS_BOARDING_INFO SET NGS_FLAG='上机异常'"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND NGS_FLAG='进行中'";
    		
    		updateJdbcTemplate.update(updateSQL, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			SysBasic.rollBack();
			log.info("/berry/prod/run/upTGS/commitToRunERR", e);
			log.error("/berry/prod/run/upTGS/commitToRunERR", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 撤回上机异常
    @RequestMapping("/revokeRunERR")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject revokeRunERR(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String updateSQL = "UPDATE BR_NGS_BOARDING_INFO SET NGS_FLAG='进行中'"
    				+ " WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND NGS_FLAG='上机异常'";
    		
    		updateJdbcTemplate.update(updateSQL, ids.toArray());
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			SysBasic.rollBack();
			log.info("/berry/prod/run/upTGS/revokeRunERR", e);
			log.error("/berry/prod/run/upTGS/revokeRunERR", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
