package com.kinglims.berry.prod.prod;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.utils.database.JdbcTemplateUtils;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;

import lombok.extern.slf4j.Slf4j;

/**
 * -----------------------------------------------------
 * Author:kinglims
 * Date:2019/11/13
 * Version:1.0
 * -----------------------------------------------------
 * You may think you know what the following code does.
 * But you dont. Trust me.
 * Fiddle with it, and youll spend many a sleepless
 * night cursing the moment you thought youd be clever
 * enough to "optimize" the code below.
 * Now close this file and go play with something else.
 * -------------------------------------------------------
 * 神剑一出
 * KILL====|K#I#N#G#L#I#M#S#K*I*N*G*L*I*M*S*===+++--->bug
 * BUG不见
 */
@Service
@RestController
@RequestMapping("/berry/prod/prod/prodTaskBus")
@Slf4j
public class BrProdTaskBusController {

    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;
    
    @Autowired
    private ResponseMessageParameter responseMessageParameter;
    
    // 生产任务: 添加任务明细
    @RequestMapping("/addMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject addMX(@RequestBody JSONObject data) {
    	
    	try {
    		String TASK_ID = data.getString("TASK_ID");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String sqlUpdate = "UPDATE BR_MODUAL_PD SET TASK_ID=?, TASK_MX_ID=?,OBJ_FLAG='归结' WHERE ID=? AND TASK_ID IS NULL";
    		
    		String insertSQL = "INSERT INTO BR_PRODU_TASK_MX ("
    				+ "ID" // 唯一标识
    				+ ",LINK_ID" // 入口表ID
    				+ ",TASK_ID" // 主表ID
    				+ ",TASK_TYPE" // 任务类型
    				+ ",DNA_RNA_ID" // 关联核酸质检ID
    				+ ",DNA_RNA_CODE" // 核酸编号
    				+ ",SAMPLE_ID" // 关联样本ID
    				+ ",SAMPLE_CODE" // 样品编号
    				+ ",SAMPLE_NAME" // 样品名称
    				+ ",SEQ_FLAG" // 检测状态
    				+ ",LIB_INDEX_CODE" // index编号/barcode
    				+ ",LIB_CODE" // 文库名称
    				+ ",LIB_LB" // 文库类别
    				+ ",LIB_TYPE" // 文库类型
    				+ ",TEST_TYPE" // 检测类型
    				+ ",SEQ_TYPE" // 测序类型
    				+ ",SAMPLE_TYPE" // 样品类型
    				+ ",GO_PLAN" // 去往产品线
    				+ ",BOARD96_CODE" // 96孔板板号
    				+ ",BOARD96_HOLE" // 96孔板孔号
    				+ ",IS_BOARD" // 是否拼板
    				+ ",INSER_LENGTH" // 插入片段长度 (bp)
    				+ ",TASK_NUM" // 建任务数
    				+ ",SEQ_TASK_NUM" // 上机任务数
    				+ ",DATA_NUM" // 数据量(M)
    				+ ",START_DATE" // 开始日期
    				+ ",CLOSING_DATE" // 截止日期
//    				+ ",CREATOR" // 创建人
//    				+ ",CREATTIME" // 创建时间
//    				+ ",LASTUPDATOR" // 最近修改人
//    				+ ",LASTUPDATETIME" // 最近修改时间
//    				+ ",LOGINCOMPANY" // 账套
    				+ ") "
    				+ "SELECT"
    				+ " ? AS ID" // 唯一标识
    				+ ",ID AS LINK_ID" // 入口表ID
    				+ ",? AS TASK_ID" // 关联提取主单
					+ ",TASK_TYPE" // 任务类型
					+ ",DNA_RNA_ID" // 关联核酸质检ID
					+ ",DNA_RNA_CODE" // 核酸编号
					+ ",SAMPLE_ID" // 关联样本ID
					+ ",SAMPLE_CODE" // 样品编号
					+ ",SAMPLE_NAME" // 样品名称
					+ ",SEQ_FLAG" // 检测状态
					+ ",LIB_INDEX_CODE" // index编号/barcode
					+ ",LIB_CODE" // 文库名称
					+ ",LIB_LB" // 文库类别
					+ ",LIB_TYPE" // 文库类型
					+ ",TEST_TYPE" // 检测类型
					+ ",SEQ_TYPE" // 测序类型
					+ ",SAMPLE_TYPE" // 样品类型
					+ ",GO_PLAN" // 去往产品线
					+ ",BOARD96_CODE" // 96孔板板号
					+ ",BOARD96_HOLE" // 96孔板孔号
					+ ",IS_BOARD" // 是否拼板
					+ ",INSER_LENGTH" // 插入片段长度 (bp)
					+ ",TASK_NUM" // 建任务数
					+ ",SEQ_TASK_NUM" // 上机任务数
					+ ",DATA_NUM" // 数据量(M)
					+ ",START_DATE" // 开始时间
					+ ",CLOSING_DATE" // 截止日期
//					+ ",PROJECT_ID" // 关联项目ID
//					+ ",PROJECT_NO" // 项目编号
//					+ ",PROJECT_NAME" // 项目名称
//					+ ",TASK_NO" // 任务单号
//					+ ",TASK_NAME" // 任务单名称
//					+ ",TASK_DESC" // 任务描述
//					+ ",SEQ_PLATFORM" // 测序平台
//					+ ",PLAT_CATEGORY" // 平台类别
//					+ ",EXEC_SAMLL" // 执行小组
//					+ ",TASK_MX_ID" // 任务单明细ID
//					+ ",FLOW_PLAN_ID" // 流程唯一标识
    				+ " FROM BR_MODUAL_PD WHERE ID=?";
    		
    		for (String MODUAL_PD_ID : ids) {
    			String mxId = SysBasic.getUUID();
    			int updateRs = updateJdbcTemplate.update(sqlUpdate, TASK_ID, mxId, MODUAL_PD_ID);
    			if (updateRs == 0) {
    				continue;
    			}
    			Object[] objs = { mxId, TASK_ID, MODUAL_PD_ID };
    			updateJdbcTemplate.update(insertSQL, objs);
    		}
    		
    		// 明细变化: 更新主单信息
    		updateTaskByMX(data);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/prod/prodTaskBus/addMX", e);
			log.error("/berry/prod/prod/prodTaskBus/addMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 生产任务: 添加任务明细: 复制模式
    @RequestMapping("/addMXCopy")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject addMXCopy(@RequestBody JSONObject data) {
    	
    	try {
    		String TASK_ID = data.getString("TASK_ID");
    		List<String> ids = (List<String>) data.get("ids");//MX_ID
    		List<String> linkIds = (List<String>) data.get("linkIds");//LINK_ID
    		
//    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(queryJdbcTemplate);
//    		Map<String, Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName("BR_MODUAL_PD");
			for (int i = 0; i < ids.size(); i++) {
				
				String id = ids.get(i);
				String linkId = linkIds.get(i);
				
				String newMxId = SysBasic.getUUID();
				String newLinkId = SysBasic.getUUID();
				
				Map<String, Object> dataMap_MX = queryJdbcTemplate.queryForMap("SELECT * FROM BR_PRODU_TASK_MX WHERE ID=?", id);
				// 移除关联字段
				dataMap_MX.remove("ID");
				dataMap_MX.remove("TASK_ID");
				dataMap_MX.remove("LINK_ID");
				// 重置关联信息
				dataMap_MX.put("ID", newMxId);
				dataMap_MX.put("TASK_ID", TASK_ID);
				dataMap_MX.put("LINK_ID", newLinkId);
				dataMap_MX.put("COPY_FLAG", "Y");//复制标识符号
				// 复制新行
//    			SysBasic.filterMap(dataMap_MX, metaMap.keySet());//过滤表不存在的字段
				SysBasic.insertDataByTableMap(updateJdbcTemplate, "BR_PRODU_TASK_MX", dataMap_MX);
				
				//复制模块入口数据
				Map<String, Object> dataMap_RK = queryJdbcTemplate.queryForMap("SELECT * FROM BR_MODUAL_PD WHERE ID=?", linkId);
				// 移除关联字段
				dataMap_RK.remove("ID");
				dataMap_RK.remove("TASK_ID");
				dataMap_RK.remove("TASK_MX_ID");
				// 重置关联信息
				dataMap_RK.put("ID", newLinkId);
				dataMap_RK.put("TASK_ID", TASK_ID);
				dataMap_RK.put("TASK_MX_ID", newMxId);
				// 复制新行
//    			SysBasic.filterMap(dataMap_RK, metaMap.keySet());//过滤表不存在的字段
				SysBasic.insertDataByTableMap(updateJdbcTemplate, "BR_MODUAL_PD", dataMap_RK);
			}
    		
    		// 明细变化: 更新主单信息
    		updateTaskByMX(data);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/prod/prodTaskBus/addMXCopy", e);
			log.error("/berry/prod/prod/prodTaskBus/addMXCopy", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 批量修改
    @RequestMapping("/editMxBatch")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject editMxBatch(@RequestBody JSONObject data) {
    	
    	try {
    		String TASK_ID = data.getString("TASK_ID");
    		String IDS = data.getString("IDS");
    		String[] idsArray = IDS.split(",");
    		
    		String selectSQL = "SELECT COUNT(1) FROM BR_PRODU_TASK WHERE ID=? AND TASK_FLAG='草稿'";
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, TASK_ID);
    		if (count == 0) {
    			return new CurrResponseResolve(-1).put("errMsg", "只能修改“草稿”的任务明细").put(responseMessageParameter).getData();
    		}
    		
    		JdbcTemplateUtils jdbcTemplateUtils = new JdbcTemplateUtils(updateJdbcTemplate);
    		Map<String,Integer> metaMap = jdbcTemplateUtils.queryMetaDataByTableName("BR_PRODU_TASK_MX");
    		
    		SysBasic.filterMap(data, metaMap.keySet());//过滤表不存在的字段
    		
    		for (String id : idsArray) {
    			data.put("ID", id);
    			SysBasic.updateDataByTableMap(updateJdbcTemplate, "BR_PRODU_TASK_MX", data);
    		}
    		
    		// 明细变化: 更新主单信息
    		data.put("TASK_ID", TASK_ID);
    		updateTaskByMX(data);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/prod/prodTaskBus/editMxBatch", e);
			log.error("/berry/prod/prod/prodTaskBus/editMxBatch", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 明细变化: 更新主单信息
    @RequestMapping("/updateTaskByMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject updateTaskByMX(@RequestBody JSONObject data) {
    	
    	try {
    		String TASK_ID = data.getString("TASK_ID");
    		
    		String selectSQL_TASK_MX = "SELECT COUNT(1) AS DATA_NUM_COUNT, SUM(DATA_NUM) AS DATA_NUM FROM BR_PRODU_TASK_MX WHERE TASK_ID=?";
    		Map<String, Object> mxMap = updateJdbcTemplate.queryForMap(selectSQL_TASK_MX, TASK_ID);
    		
    		String updateSQL_TASK = "UPDATE BR_PRODU_TASK SET DATA_NUM=? WHERE ID=?";
    		Object[] params = { mxMap.get("DATA_NUM"), TASK_ID };
    		updateJdbcTemplate.update(updateSQL_TASK, params);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/prod/prodTaskBus/updateTaskByMX", e);
			log.error("/berry/prod/prod/prodTaskBus/updateTaskByMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 明细删除
    @RequestMapping("/delMX")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject delMX(@RequestBody JSONObject data) {
    	
    	try {
    		String TASK_ID = data.getString("TASK_ID");
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String selectSQL = "SELECT COUNT(1) FROM BR_PRODU_TASK WHERE ID=? AND TASK_FLAG='草稿'";
    		int count = queryJdbcTemplate.queryForObject(selectSQL, Integer.class, TASK_ID);
    		if (count == 0) {
    			return new CurrResponseResolve(-1).put("errMsg", "只能删除“草稿”状态的任务单的明细").put(responseMessageParameter).getData();
    		}
    		
    		// 删除模块表数据: 复制方式
    		String deleteSQL_RK = "DELETE BR_MODUAL_PD WHERE ID IN (SELECT LINK_ID FROM BR_PRODU_TASK_MX WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+") AND COPY_FLAG='Y')";
    		// 删除明细
    		String deleteSQL_MX = "DELETE FROM BR_PRODU_TASK_MX WHERE ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		// 更新模块表： 清空关联关系
    		String updateSQL_RK = "UPDATE BR_MODUAL_PD SET TASK_ID=NULL,TASK_MX_ID=NULL WHERE TASK_MX_ID IN ("+SysBasic.getQuestionMarkBySzie(ids.size())+")";
    		
    		updateJdbcTemplate.update(deleteSQL_RK, ids.toArray());
    		updateJdbcTemplate.update(deleteSQL_MX, ids.toArray());
	    	updateJdbcTemplate.update(updateSQL_RK, ids.toArray());
    		
    		// 明细变化: 更新主单信息
    		updateTaskByMX(data);
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/prod/prodTaskBus/delMX", e);
			log.error("/berry/prod/prod/prodTaskBus/delMX", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 明细主单
    @RequestMapping("/delM")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject prodTaskBusDelM(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		
    		String deleteSQL_M = "DELETE FROM BR_PRODU_TASK WHERE ID=? AND TASK_FLAG='草稿'";
    		// 删除模块表数据: 复制方式
    		String deleteSQL_RK = "DELETE BR_MODUAL_PD WHERE ID IN (SELECT LINK_ID FROM BR_PRODU_TASK_MX WHERE TASK_ID=? AND COPY_FLAG='Y')";
    		// 删除明细
    		String deleteSQL_MX = "DELETE FROM BR_PRODU_TASK_MX WHERE TASK_ID=?";
    		// 更新模块表： 清空关联关系
    		String updateSQL_RK = "UPDATE BR_MODUAL_PD SET TASK_ID=NULL,TASK_MX_ID=NULL,OBJ_FLAG=NULL WHERE TASK_ID=?";
    		for (String TASK_ID : ids) {
    			int i = updateJdbcTemplate.update(deleteSQL_M, TASK_ID);
    			if (i > 0) {
	    			updateJdbcTemplate.update(deleteSQL_RK, TASK_ID);
	    			updateJdbcTemplate.update(deleteSQL_MX, TASK_ID);
		    		updateJdbcTemplate.update(updateSQL_RK, TASK_ID);
    			}
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/prod/prodTaskBus/delM", e);
			log.error("/berry/prod/prod/prodTaskBus/delM", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
    // 提交审核
    @RequestMapping("/submitToApprove")
    @Transactional(rollbackFor = Exception.class)
    public JSONObject prodTaskBusSubmitToApprove(@RequestBody JSONObject data) {
    	
    	try {
    		List<String> ids = (List<String>) data.get("ids");
    		String sqlUpdate = "UPDATE BR_PRODU_TASK SET TASK_FLAG='待审核' WHERE ID=? and TASK_FLAG='草稿'";
    		for (String id : ids) {
    			updateJdbcTemplate.update(sqlUpdate, id);
    		}
    		
	    	return new CurrResponseResolve(1).put(responseMessageParameter).getData();
	    	
		} catch (Exception e) {
			
			log.info("/berry/prod/prod/prodTaskBus/submitToApprove", e);
			log.error("/berry/prod/prod/prodTaskBus/submitToApprove", e);
			
			return new CurrResponseResolve(-1).put(responseMessageParameter).getData();
		}
    }
}
