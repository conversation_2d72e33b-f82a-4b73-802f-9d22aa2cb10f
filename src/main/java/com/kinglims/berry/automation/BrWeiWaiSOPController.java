package com.kinglims.berry.automation;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.GridSchemaModel;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;
import jdk.nashorn.api.scripting.ScriptObjectMirror;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedCaseInsensitiveMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@RestController
@RequestMapping("/berry/automation/sop")
@Slf4j
public class BrWeiWaiSOPController {
    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;

    @Autowired
    private BMKPoolingCalculateSamplingVolume bmkPCSLV;

    @Autowired
    private ResponseMessageParameter responseMessageParameter;

    @Autowired
    private HttpCommonController httpCommonController;


    @RequestMapping("/wwdd")
    public JSONObject subcontractingOrder(@RequestBody JSONObject data) throws Exception {
        String exNO = SysBasic.toTranStringByObject(data.get("EX_DH_NO")); //执行单号
        String DEFINE32 = SysBasic.toTranStringByObject(data.get("DEFINE32")); //阶段
        String username = SysBasic.toTranStringByObject(data.get("username")); //人
        String sopCode = SysBasic.toTranStringByObject(data.get("sopCode")); //
        String TYPE = SysBasic.toTranStringByObject(data.get("TYPE")); //混样建库区分常规与MCD
        double tqMgs = SysBasic.toTranDoubleByObject(data.get("tqMgs")); //
        String SYS_INSERTTIME_JL = SysBasic.toTranStringByObject(data.get("SYS_INSERTTIME_JL"));
        double count = SysBasic.toTranIntegerByObject(data.get("count")); //
        List<Map<String, Object>> listSOP = (List<Map<String, Object>>) data.get("listSOP");
        List<Map<String, Object>> listSOPMx = (List<Map<String, Object>>) data.get("listSOPMx");
        List<Map<String, Object>> listEx = (List<Map<String, Object>>) data.get("listEx");
        Map<String, Object> sopADDs = (Map<String, Object>) data.get("sopADDs");
        Date mNdate = getmNdate("委外订单接口", SYS_INSERTTIME_JL);


        HashMap<String, Map<String, Object>> SampleMap = new LinkedHashMap<>();//各项目期号提取数
        String sqlEx = SysBasic.toTranStringByObject(data.get("sqlEx")); //

        for (int j = 0; j < listEx.size(); j++) {
            Map mapXM = listEx.get(j);
            Double TQ_MG = SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG"));
            String PROJECT_SUBNO =SysBasic.toTranStringByObject(mapXM.get("PROJECT_SUBNO"));
            String TASK_NO =SysBasic.toTranStringByObject(mapXM.get("TASK_NO"));
            String subNo = (String) mapXM.get("PROJECT_SUBNO")+TASK_NO;
            String pName = (String) mapXM.get("PROJECT_NAME");
            String pNo = (String) mapXM.get("PROJECT_NO");
            String cType = (String) mapXM.get("CONTRACT_TYPE");
            String cNo = (String) mapXM.get("CONTRACT_NO");

            Date BTIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("BTIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    BTIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    BTIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }
            Date ETIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("ETIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    ETIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    ETIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }
            String MAN = (String) mapXM.get("MAN");
            double num = 0;
            if (SampleMap.containsKey(subNo)) {
                double sNum = (Double) SampleMap.get(subNo).get("sNum");
                int sNum1 = (int) SampleMap.get(subNo).get("sNum1");
                double tq_mg = SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG"));
                BigDecimal bsNum = new BigDecimal(Double.toString(sNum));
                BigDecimal bDtqMg = new BigDecimal(Double.toString(tq_mg));
                bsNum = bsNum.add(bDtqMg);
                double d = bsNum.doubleValue();
                SampleMap.get(subNo).put("sNum", d);
                SampleMap.get(subNo).put("sNum1", sNum1 + 1);
            } else {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("PROJECT_NAME", pName);
                map.put("TASK_NO", mapXM.get("TASK_NO"));
                map.put("PROJECT_SUBNO", PROJECT_SUBNO);
                map.put("PROJECT_NO", pNo);
                map.put("CONTRACT_TYPE", cType);
                map.put("CONTRACT_NO", cNo);
                map.put("BTIME", BTIME);
                map.put("ETIME", ETIME);
                map.put("MAN", MAN);
                map.put("sNum", SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG")));
                map.put("sNum1", 1);
                SampleMap.put(subNo, map);
            }
        }

        //  if (SampleNum > count) count = SampleNum;
        Iterator<Map.Entry<String, Map<String, Object>>> entries = SampleMap.entrySet().iterator();
        while (entries.hasNext()) {
            Map.Entry<String, Map<String, Object>> entry = entries.next();
            String key = entry.getKey();
            Map value = entry.getValue();
            if ((Double) value.get("sNum") == 0) entries.remove();
        }


        int DEFINE29 = 1;
        for (int j = 0; j < listSOP.size(); j++) {


            Map<String, Object> OrderMap = new HashMap<String, Object>();
            String OrderID = SysBasic.getUUID();
            OrderMap.put("EX_DH_NO", exNO);
            OrderMap.put("EXE_TQQC_ID", listSOP.get(j).get("ID"));
            OrderMap.put("ID", OrderID);
            OrderMap.put("CODE", sopCode);
            OrderMap.put("BILLDATE", mNdate);
            OrderMap.put("VENDORCODE", "1030");
            OrderMap.put("DEPTCODE", listSOP.get(j).get("U8_DEPT_CODE"));
            OrderMap.put("PURCHASE_TYPE_CODE", "01");
            OrderMap.put("ORDERTYPE", "0");
            OrderMap.put("OPERATION_TYPE_CODE", "委外加工");
            OrderMap.put("CURRENCY_NAME", "人民币");
            OrderMap.put("CURRENCY_RATE", "1");
            OrderMap.put("TAX_RATE", "6");
            OrderMap.put("MAKER", "demo");
            OrderMap.put("DEFINE10", "分子实验中心-管理");
            OrderMap.put("PUSH_STATE", "待推送");
            OrderMap.put("IMPUTATIONDATA", mNdate);
            OrderMap.put("DEFINE1", sopCode);
            OrderMap.put("GENERATE_DATE", mNdate);
            int a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_WW_ORDER", OrderMap);

            for (String key : SampleMap.keySet()) {

                double num = (Double) SampleMap.get(key).get("sNum");//一期用量
                String subNo = (String) SampleMap.get(key).get("PROJECT_SUBNO"); //项目期号
                String pNo = (String) SampleMap.get(key).get("PROJECT_NO");   //项目编号
                String cNo = (String) SampleMap.get(key).get("CONTRACT_NO");  //合同编号
                Date BTIME = new Date();
                Date ETIME = new Date();
                BTIME = (Date) SampleMap.get(key).get("BTIME");
                ETIME = (Date) SampleMap.get(key).get("ETIME");

                Map<String, Object> OrderZbMap = new HashMap<String, Object>();
                String sqlU8ID = "select u8ID.nextval from dual";
                Integer u8ID = queryJdbcTemplate.queryForObject(sqlU8ID, Integer.class);
                u8ID = u8ID + 1000000000;

                if (TYPE.equals("混样建库常规")) {
                    num = (int) SampleMap.get(key).get("sNum1");
                } //混样建库非常规拆分按数据量，【QUANTITY】数量取样品数

                String OrderZBID = SysBasic.getUUID();
                OrderZbMap.put("ID", OrderZBID);
                OrderZbMap.put("ORDER_ID", OrderID);
                OrderZbMap.put("MODETAILSID", u8ID);
                OrderZbMap.put("INVENTORYCODE", listSOP.get(j).get("SOP_CODE"));
                OrderZbMap.put("QUANTITY", num);
                OrderZbMap.put("TAX", "0");
                OrderZbMap.put("TAXRATE", "6");
                OrderZbMap.put("ITEM_CLASS", "00");
                OrderZbMap.put("ITEM_CODE", SampleMap.get(key).get("CONTRACT_NO"));
                OrderZbMap.put("ITEM_NAME", SampleMap.get(key).get("PROJECT_NAME"));
                OrderZbMap.put("ARRIVEDATE", ETIME);
                OrderZbMap.put("STARTDATE", BTIME);
                OrderZbMap.put("DEFINE23", SampleMap.get(key).get("CONTRACT_TYPE"));
                OrderZbMap.put("DEFINE31", SampleMap.get(key).get("MAN"));
                OrderZbMap.put("DEFINE32", listSOP.get(j).get("S_STEP"));
                OrderZbMap.put("DEFINE33", SampleMap.get(key).get("PROJECT_SUBNO"));
                OrderZbMap.put("BOMID", "2001");
                OrderZbMap.put("DEFINE28", SampleMap.get(key).get("PROJECT_NO"));
                OrderZbMap.put("DEFINE29", sopCode + "-" + String.format("%04d", DEFINE29++));
                OrderZbMap.put("CBDEFINE12", SampleMap.get(key).get("TASK_NO"));
                a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_WW_ORDERZB", OrderZbMap);

                for (int i = 0; i < listSOPMx.size(); i++) {
                    Map mapSOP = listSOPMx.get(i);

                    String sqlhs = "SELECT  bsm.CHANGE_RATE FROM BIO_SC_MATERIEL bsm WHERE bsm.W_CODE= ?";
                    List<Map<String, Object>> sqlhsl = queryJdbcTemplate.queryForList(sqlhs, listSOPMx.get(i).get("W_CODE"));
                    double hsl = SysBasic.toTranDoubleByObject(sqlhsl.get(0).get("CHANGE_RATE"));

                    double standude = SysBasic.toTranDoubleByObject(mapSOP.get("M_STANDUSE")); //标准用量
                    double loss = SysBasic.toTranDoubleByObject(mapSOP.get("M_LOSS")); //损耗
                    double mFNum = SysBasic.toTranDoubleByObject(mapSOP.get("M_FY_NUMBER")); //反应数
                    double pTotal = SysBasic.toTranDoubleByObject(mapSOP.get("PRACTICAL_TOTAL")); //实际总量
                    String TASK_NO = (String) SampleMap.get(key).get("TASK_NO");


                    BigDecimal bDpTotal = new BigDecimal(Double.toString(pTotal));
                    BigDecimal bDnum = new BigDecimal(Double.toString((Double) SampleMap.get(key).get("sNum")));
                    BigDecimal bDtqMgs = new BigDecimal(Double.toString(tqMgs));
                    BigDecimal subtract = (bDnum.divide(bDtqMgs, 6, BigDecimal.ROUND_DOWN)).multiply(bDpTotal).setScale(6, BigDecimal.ROUND_DOWN);
                    ;
                    String noKey = mapSOP.get("ID") + "-" + key;
                    if (sopADDs.get(noKey) != null) {
                        Double aDouble = (Double) sopADDs.get(noKey);
                        BigDecimal bDaDouble = new BigDecimal(Double.toString(aDouble));
                        subtract = subtract.add(bDaDouble);
                    }
                    Double result = subtract.doubleValue();

                    Map<String, Object> OrderZbMxMap = new HashMap<String, Object>();
                    OrderZbMxMap.put("ID", SysBasic.getUUID());
                    OrderZbMxMap.put("ORDERZB_ID", OrderZBID);
                    OrderZbMxMap.put("MODETAILSID", u8ID);
                    OrderZbMxMap.put("INVENTORYCODE", mapSOP.get("W_CODE"));
                    OrderZbMxMap.put("QUANTITY", result);
                    OrderZbMxMap.put("REQUIREDDATE", ETIME);
                    OrderZbMxMap.put("DEFINE24", mapSOP.get("W_NAME"));
                    OrderZbMxMap.put("BASEQTYD", "1000");
                    OrderZbMxMap.put("BASEQTYN", standude * 1000);
                    OrderZbMxMap.put("UNITID", mapSOP.get("W_UNIT_CODE"));
                    OrderZbMxMap.put("FVGTY", "0");
                    OrderZbMxMap.put("WIPTYPE", "3");
                    OrderZbMxMap.put("OPCOMPONENTID", mapSOP.get("U8_MX_ID"));
                    OrderZbMxMap.put("WHCODE", "06");
                    OrderZbMxMap.put("USEQUANTITY", standude);
                    OrderZbMxMap.put("SENDTYPE", "0");
                    OrderZbMxMap.put("RID", i);
                    a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_WW_ORDERZBMX", OrderZbMxMap);


                }
            }
        }

        return new CurrResponseResolve(1).put("apiData", 1).getData();
    }

    /*
        @RequestMapping("/wwqg")
        public JSONObject buyingRequisition(@RequestBody JSONObject data) throws Exception {
            String exNO = SysBasic.toTranStringByObject(data.get("EX_DH_NO")); //执行单号

            double SampleNum = 0;//总提取数
            HashMap<String, Map<String, Object>> SampleMap = new LinkedHashMap<>();//各项目期号提取数

            String sqlEx = "select btt.PROJECT_NAME ,btt.PROJECT_SUBNO , btt.PROJECT_NO ,bpi.CONTRACT_NO,bpi.CONTRACT_TYPE,bdrq.TQ_MG" +
                    " FROM BIO_TQ_TASK btt," +
                    "BIO_TQ_TASK_MX bttm, " +
                    "BIO_DNA_RNA_QC bdrq, " +
                    "BIO_PROJECT_INFO bpi," +
                    "EXE_TQQC_SHEET ets " +
                    "WHERE btt.ID=bttm.TASK_ID " +
                    "AND bttm.ID=bdrq.TASK_TQ_ID " +
                    "AND bdrq.EXE_TQQC_ID=ets.ID " +
                    "and btt.CONTRACT_NO=bpi.CONTRACT_NO " +
                    "and ets.EX_DH_NO=? ";
            List<Map<String, Object>> listEx = queryJdbcTemplate.queryForList(sqlEx, exNO);

            for (int j = 0; j < listEx.size(); j++) {
                Map mapXM = listEx.get(j);

                SampleNum = SampleNum + SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG"));
                String subNo = (String) mapXM.get("PROJECT_SUBNO");
                String pName = (String) mapXM.get("PROJECT_NAME");
                String pNo = (String) mapXM.get("PROJECT_NO");
                String cType = (String) mapXM.get("CONTRACT_TYPE");
                double num = 0;
                if (SampleMap.containsKey(subNo + "-" + pName + "-" + pNo + "-" + cType)) {
                    double sNum = (Double) SampleMap.get(subNo + "-" + pName + "-" + pNo + "-" + cType).get("sNum");
                    double tq_mg = SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG"));
                    SampleMap.get(subNo + "-" + pName + "-" + pNo + "-" + cType).put("sNum", sNum + tq_mg);

                } else {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("PROJECT_NAME", pName);
                    map.put("PROJECT_SUBNO", subNo);
                    map.put("PROJECT_NO", pNo);
                    map.put("CONTRACT_TYPE", cType);
                    map.put("sNum", SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG")));
                    SampleMap.put(subNo + "-" + pName + "-" + pNo + "-" + cType, map);
                }

            }

            String sqlSop = "select * from BIO_BZ_MATERIEL_SOP_JL   where  " +
                    " (COLLECTION_FLAG !='已归集' or COLLECTION_FLAG is null) and EX_DH_NO=?";
            String sqlSopMx = "select bsjm.* from BIO_BZ_MATERIEL_SOP_JL  bsj,BIO_BZ_MATERIEL_SOP_JL_MX bsjm where bsj.ID=bsjm.BIO_BZ_MATERIEL_SOP_ID " +
                    "and (bsjm.COLLECTION_FLAG !='已归集' or bsjm.COLLECTION_FLAG is null) and bsj.EX_DH_NO=?";


            List<Map<String, Object>> listSOP = queryJdbcTemplate.queryForList(sqlSop, exNO);
            List<Map<String, Object>> listSOPMx = queryJdbcTemplate.queryForList(sqlSopMx, exNO);

            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String[] split = sdf.format(date).split("-");
            String sCode = split[0].substring(2) + split[1] + split[2];

            for (int j = 0; j < listSOP.size(); j++) {

                String sql = "select max(CODE) from BIO_WW_REQUISITION where CODE like '%'||?||'%'";
                String sqlCode = queryJdbcTemplate.queryForObject(sql, String.class, sCode);
                String sopCode = "";
                if (sqlCode != null) {

                    sopCode = sCode + String.format("%03d", Integer.parseInt(sqlCode.substring(6)) + 1);
                } else {
                    sopCode = sCode + "001";
                }


                Map<String, Object> BWRMap = new HashMap<String, Object>();
                String BWRID = SysBasic.getUUID();
                BWRMap.put("EX_DH_NO", exNO);
                BWRMap.put("ID", BWRID);
                BWRMap.put("CODE", sopCode);
                BWRMap.put("GENERATE_DATE", new Date());
                BWRMap.put("DEPARTMENTCODE", listSOP.get(j).get("U8_DEPT_CODE"));
                BWRMap.put("PURCHASETYPECODE", "01");
                BWRMap.put("BUSINESSTYPE", "普通采购");
                BWRMap.put("PUSH_STATE", "待推送");
                int a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_WW_REQUISITION", BWRMap);


                for (String key : SampleMap.keySet()) {
                    for (int i = 0; i < listSOPMx.size(); i++) {

                        Map mapSOP = listSOPMx.get(i);
                        double num = (Double) SampleMap.get(key).get("sNum");//一期用量
                        double standude = SysBasic.toTranDoubleByObject(mapSOP.get("M_STANDUSE")); //标准用量
                        double loss = SysBasic.toTranDoubleByObject(mapSOP.get("M_LOSS")); //损耗
                        double mFNum = SysBasic.toTranDoubleByObject(mapSOP.get("M_FY_NUMBER")); //反应数
                        double pTotal = SysBasic.toTranDoubleByObject(mapSOP.get("PRACTICAL_TOTAL")); //实际总量
                        Double result = pTotal * (num / mFNum);
                        result =((double)((int)(result*10000)))/10000;

                        Map<String, Object> BWRMXMap = new HashMap<String, Object>();
                        BWRMXMap.put("ID", SysBasic.getUUID());
                        BWRMXMap.put("REQUISITIONID", BWRID);
                        BWRMXMap.put("VENDORCODE", sopCode);
                        BWRMXMap.put("INVENTORYCODE", sopCode);
                        BWRMXMap.put("QUANTITY", result);
                        BWRMXMap.put("TAXRATE", "6");
                        BWRMXMap.put("REQUIREDATE", new Date());
                        BWRMXMap.put("ARRIVEDATE", new Date());
                        BWRMXMap.put("ITEM_CLASS", "00");
                        BWRMXMap.put("ITEM_CODE", SampleMap.get(key).get("PROJECT_NO"));
                        BWRMXMap.put("ITEM_NAME", SampleMap.get(key).get("PROJECT_NAME"));
                        BWRMXMap.put("DEFINE23", SampleMap.get(key).get("CONTRACT_TYPE"));
                        BWRMXMap.put("DEFINE24", SampleMap.get(key).get("CONTRACT_NO"));
                        BWRMXMap.put("DEFINE25", SampleMap.get(key).get("PROJECT_SUBNO"));
                        BWRMXMap.put("CURRENCY_NAME", "人民币");
                        BWRMXMap.put("CURRENCY_RATE", "1");
                        BWRMXMap.put("CBDEFINE2", "00");


                        a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_WW_REQUISITIONMX", BWRMXMap);
                    }
                }
            }
            return new CurrResponseResolve(1).put("apiData", 1).getData();

        }

    @RequestMapping("/wwcg")
    public JSONObject weiWaiCaiGou(@RequestBody JSONObject data) throws Exception {
        String exNO = SysBasic.toTranStringByObject(data.get("EX_DH_NO")); //执行单号
        String sqlEx = SysBasic.toTranStringByObject(data.get("sqlEx")); //
        String sopCode = SysBasic.toTranStringByObject(data.get("sopCode")); //
        String username = SysBasic.toTranStringByObject(data.get("username")); //人
        String sqlbt = SysBasic.toTranStringByObject(data.get("sqlbt")); //人
        double SampleNum = 0;//总提取数
        HashMap<String, Map<String, Object>> SampleMap = new LinkedHashMap<>();//各项目期号提取数


        List<Map<String, Object>> listEx = queryJdbcTemplate.queryForList(sqlEx, exNO);

        List<Map<String, Object>> listBt = queryJdbcTemplate.queryForList(sqlbt, exNO);

        for (int j = 0; j < listEx.size(); j++) {
            Map mapXM = listEx.get(j);

            SampleNum = SampleNum + SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG"));
            String subNo = (String) mapXM.get("PROJECT_SUBNO");
            String pName = (String) mapXM.get("PROJECT_NAME");
            String pNo = (String) mapXM.get("PROJECT_NO");
            String cType = (String) mapXM.get("CONTRACT_TYPE");
            String cName = (String) mapXM.get("CONTRACT_NAME");
            String cNo = (String) mapXM.get("CONTRACT_NO");
            Date BTIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("BTIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    BTIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    BTIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }
            Date ETIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("ETIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    ETIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    ETIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }
            String MAN = (String) mapXM.get("MAN");
            double num = 0;
            if (SampleMap.containsKey(subNo + "-" + pName + "-" + pNo + "-" + cType + "-" + cName + "-" + cNo)) {
                double sNum = (Double) SampleMap.get(subNo + "-" + pName + "-" + pNo + "-" + cType + "-" + cName + "-" + cNo).get("sNum");

                SampleMap.get(subNo + "-" + pName + "-" + pNo + "-" + cType + "-" + cName + "-" + cNo).put("sNum", sNum + 1.0);

            } else {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("PROJECT_NAME", pName);
                map.put("PROJECT_SUBNO", subNo);
                map.put("PROJECT_NO", pNo);
                map.put("CONTRACT_TYPE", cType);
                map.put("CONTRACT_NAME", cName);
                map.put("CONTRACT_NO", cNo);
                map.put("BTIME", BTIME);
                map.put("ETIME", ETIME);
                map.put("MAN", MAN);
                map.put("sNum", 1.0);
                SampleMap.put(subNo + "-" + pName + "-" + pNo + "-" + cType + "-" + cName + "-" + cNo, map);
            }

        }
        Iterator<Map.Entry<String, Map<String, Object>>> entries = SampleMap.entrySet().iterator();
        while (entries.hasNext()) {
            Map.Entry<String, Map<String, Object>> entry = entries.next();
            String key = entry.getKey();
            Map value = entry.getValue();
            if((Double) value.get("sNum")==0)entries.remove();
        }

        for (int i = 0; i < listBt.size(); i++) {

            Map<String, Object> BWRMap = new HashMap<String, Object>();
            String BWRID = SysBasic.getUUID();
            BWRMap.put("EX_DH_NO", exNO);
            BWRMap.put("ID", BWRID);
            BWRMap.put("BILL_ID", sopCode);
            BWRMap.put("RECEIVEFLAG", "1");
            BWRMap.put("VOUCHTYPE", "01");
            BWRMap.put("BUSINESSTYPE", "普通采购");
            BWRMap.put("PURCHASETYPECODE", "01");
            BWRMap.put("SOURCE", "库存");
            BWRMap.put("WAREHOUSECODE", "02");
            BWRMap.put("BILL_CODE", sopCode);
            BWRMap.put("RECEIVECODE", "112");
            BWRMap.put("DEPARTMENTCODE", "");
            BWRMap.put("VENDORCODE", "0000113");
            BWRMap.put("MEMORY", "1");
            BWRMap.put("MAKER", username);
            BWRMap.put("DEFINE10", " ");
            BWRMap.put("TAXRATE", "13");
            BWRMap.put("EXCHNAME", "人民币");
            BWRMap.put("EXCHRATE", "1");
            BWRMap.put("PUSH_STATE", "待推送");
            BWRMap.put("GENERATE_DATE", new Date());
            BWRMap.put("LIMS_TO_U8ID", sopCode);
            BWRMap.put("WAREHOUSING_DATE", new Date());

            int a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_WW_PURCHASE_RECEIPT", BWRMap);

            for (String key : SampleMap.keySet()) {

                double num = (Double) SampleMap.get(key).get("sNum");

                String sqlU8ID = "select u8ID.nextval from dual";
                Integer u8ID = queryJdbcTemplate.queryForObject(sqlU8ID, Integer.class);
                u8ID = u8ID + 1000000000;
                double PRICE = 1;
                Map<String, Object> BWRMXMap = new HashMap<String, Object>();
                BWRMXMap.put("ID", SysBasic.getUUID());
                BWRMXMap.put("BILL_ID", sopCode);
                BWRMXMap.put("AUTOID", u8ID);
                BWRMXMap.put("INVENTORYCODE", "900000");
                BWRMXMap.put("INVNAME", "委托加工");
                BWRMXMap.put("QUANTITY", num);
                BWRMXMap.put("CMASSUNITNAME", "管数");
                BWRMXMap.put("PRICE", "1");
                BWRMXMap.put("COST", PRICE * num);
                BWRMXMap.put("ITEMCLASSCODE", "00");
                BWRMXMap.put("ITEMCLASSNAME", "T3项目管理");
                BWRMXMap.put("ITEMCODE", SampleMap.get(key).get("CONTRACT_NO"));
                BWRMXMap.put("ITEMNAME", SampleMap.get(key).get("CONTRACT_NAME"));
                BWRMXMap.put("DEFINE28", SampleMap.get(key).get("PROJECT_NO"));
                BWRMXMap.put("DEFINE33", SampleMap.get(key).get("PROJECT_SUBNO"));
                BWRMXMap.put("TAXRATE", "1");
                BWRMXMap.put("MEMORY", "1");
                BWRMXMap.put("CBDEFINE1", "1");
                BWRMXMap.put("CBDEFINE2", "原核转录组");
                BWRMXMap.put("CBDEFINE3", "23073334");
                BWRMXMap.put("CBDEFINE4", "首付款");
                BWRMXMap.put("DEFINE32", "提取");


                a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_WW_PURCHASE_RECEIPT_MX", BWRMXMap);
            }
        }

        return new CurrResponseResolve(1).put("apiData", 1).getData();

    }
    */

    @RequestMapping("/qdww")
    public JSONObject qingdaoweiwai(@RequestBody JSONObject data) throws Exception {
        String exNO = SysBasic.toTranStringByObject(data.get("EX_DH_NO")); //执行单号
        String username = SysBasic.toTranStringByObject(data.get("username")); //人
        String DEFINE32 = SysBasic.toTranStringByObject(data.get("DEFINE32")); //阶段
        String sopCode = SysBasic.toTranStringByObject(data.get("sopCode")); //
        String sqlEx = SysBasic.toTranStringByObject(data.get("sqlEx")); //
        String TYPE = SysBasic.toTranStringByObject(data.get("TYPE")); //混样建库区分常规与MCD
        String SYS_INSERTTIME_JL = SysBasic.toTranStringByObject(data.get("SYS_INSERTTIME_JL"));

        double count = SysBasic.toTranIntegerByObject(data.get("count")); //
        List<Map<String, Object>> listSOP = (List<Map<String, Object>>) data.get("listSOP");
        List<Map<String, Object>> listSOPMx = (List<Map<String, Object>>) data.get("listSOPMx");
        List<Map<String, Object>> listEx = (List<Map<String, Object>>) data.get("listEx");
        Date mNdate = getmNdate("青岛U8生产订单接口", SYS_INSERTTIME_JL);


        HashMap<String, Map<String, Object>> SampleMap = new LinkedHashMap<>();//各项目期号提取数


        for (int j = 0; j < listEx.size(); j++) {
            Map mapXM = listEx.get(j);
            Double TQ_MG = SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG"));
            String PROJECT_SUBNO =SysBasic.toTranStringByObject(mapXM.get("PROJECT_SUBNO"));
            String TASK_NO =SysBasic.toTranStringByObject(mapXM.get("TASK_NO"));
            String subNo = (String) mapXM.get("PROJECT_SUBNO")+TASK_NO;
            String pName = (String) mapXM.get("PROJECT_NAME");
            String pNo = (String) mapXM.get("PROJECT_NO");
            String cType = (String) mapXM.get("CONTRACT_TYPE");
            String cName = (String) mapXM.get("CONTRACT_NAME");
            String cNo = (String) mapXM.get("CONTRACT_NO");
            Date BTIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("BTIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    BTIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    BTIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }
            Date ETIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("ETIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    ETIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    ETIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }
            String MAN = (String) mapXM.get("MAN");
            double num = 0;
            if (SampleMap.containsKey(subNo)) {
                double sNum = (Double) SampleMap.get(subNo).get("sNum");
                int sNum1 = (int) SampleMap.get(subNo).get("sNum1");
                SampleMap.get(subNo).put("sNum1", sNum1 + 1);
                BigDecimal bsNum = new BigDecimal(Double.toString(sNum));
                BigDecimal bDtqMg = new BigDecimal(Double.toString(TQ_MG));
                bsNum = bsNum.add(bDtqMg);
                double d = bsNum.doubleValue();
                SampleMap.get(subNo).put("sNum", d);

            } else {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("TASK_NO", mapXM.get("TASK_NO"));
                map.put("PROJECT_NAME", pName);
                map.put("PROJECT_SUBNO", PROJECT_SUBNO);
                map.put("PROJECT_NO", pNo);
                map.put("CONTRACT_TYPE", cType);
                map.put("CONTRACT_NAME", cName);
                map.put("CONTRACT_NO", cNo);
                map.put("BTIME", BTIME);
                map.put("ETIME", ETIME);
                map.put("MAN", MAN);
                map.put("sNum", TQ_MG);
                map.put("sNum1", 1);
                SampleMap.put(subNo, map);
            }

        }

        Iterator<Map.Entry<String, Map<String, Object>>> entries = SampleMap.entrySet().iterator();
        while (entries.hasNext()) {
            Map.Entry<String, Map<String, Object>> entry = entries.next();
            String key = entry.getKey();
            Map value = entry.getValue();
            if ((Double) value.get("sNum") == 0) entries.remove();
        }

        for (int j = 0; j < listSOP.size(); j++) {


            String BWRID = SysBasic.getUUID();
            int DEFINE29 = 1;
            for (String key : SampleMap.keySet()) {
                //  for (int i = 0; i < listSOPMx.size(); i++) {

                  /*  Map mapSOP = listSOPMx.get(i);
                    double num = (Double) SampleMap.get(key).get("sNum");//一期用量
                    double standude = SysBasic.toTranDoubleByObject(mapSOP.get("M_STANDUSE")); //标准用量
                    double loss = SysBasic.toTranDoubleByObject(mapSOP.get("M_LOSS")); //损耗
                    double mFNum = SysBasic.toTranDoubleByObject(mapSOP.get("M_FY_NUMBER")); //反应数
                    double pTotal = SysBasic.toTranDoubleByObject(mapSOP.get("PRACTICAL_TOTAL")); //实际总量
                    Double result = pTotal * (num / count);*/

                double num = (Double) SampleMap.get(key).get("sNum");//一期用量
                if (TYPE.equals("混样建库常规")) {
                    num = (int) SampleMap.get(key).get("sNum1");
                } //混样建库非常规拆分按数据量，【QUANTITY】数量取样品数
                String sqlU8ID = "select u8ID.nextval from dual";
                Integer u8ID = queryJdbcTemplate.queryForObject(sqlU8ID, Integer.class);
                u8ID = u8ID + 1000000000;

                Map<String, Object> BWRMXMap = new HashMap<String, Object>();
                BWRMXMap.put("EX_DH_NO", exNO);
                BWRMXMap.put("EXE_TQQC_ID", listSOP.get(j).get("ID"));
                BWRMXMap.put("ID", SysBasic.getUUID());
                BWRMXMap.put("MOID", sopCode);
                BWRMXMap.put("MOCODE", sopCode);
                BWRMXMap.put("IMPUTATIONDATA", mNdate);
                BWRMXMap.put("MODID", u8ID);
                BWRMXMap.put("SORTSEQ", DEFINE29);
                BWRMXMap.put("MOCLASS", "2");
                BWRMXMap.put("MOTYPECODE", "01");
                BWRMXMap.put("QTY", num);
                BWRMXMap.put("WHCODE", "06");
                BWRMXMap.put("MDEPTCODE", listSOP.get(j).get("U8_DEPT_CODE"));
                BWRMXMap.put("INVCODE", listSOP.get(j).get("SOP_CODE"));
                BWRMXMap.put("FREE1", "");
                BWRMXMap.put("FREE2", "");
                BWRMXMap.put("FREE3", "");
                BWRMXMap.put("FREE4", "");
                BWRMXMap.put("FREE5", "");
                BWRMXMap.put("FREE6", "");
                BWRMXMap.put("FREE7", "");
                BWRMXMap.put("FREE8", "");
                BWRMXMap.put("FREE9", "");
                BWRMXMap.put("FREE10", "");
                BWRMXMap.put("DEFINE23", SampleMap.get(key).get("CONTRACT_TYPE"));
                BWRMXMap.put("DEFINE28", SampleMap.get(key).get("PROJECT_NO"));
                BWRMXMap.put("DEFINE31", SampleMap.get(key).get("MAN"));
                BWRMXMap.put("DEFINE32", listSOP.get(j).get("S_STEP"));
                BWRMXMap.put("DEFINE33", SampleMap.get(key).get("PROJECT_SUBNO"));
                BWRMXMap.put("REMARK", "");
                BWRMXMap.put("DEFINE24", SampleMap.get(key).get("CONTRACT_NO"));
                BWRMXMap.put("DEFINE25", SampleMap.get(key).get("CONTRACT_NAME"));
                BWRMXMap.put("STARTDATE", getmindate("青岛U8生产订单接口", SYS_INSERTTIME_JL));
                BWRMXMap.put("DUEDATE", getmaxdate("青岛U8生产订单接口", SYS_INSERTTIME_JL));
                BWRMXMap.put("PUSH_STATE", "待推送");
                BWRMXMap.put("GENERATE_DATE", mNdate);
                BWRMXMap.put("DEFINE1", sopCode);
                BWRMXMap.put("DEFINE29", sopCode + "-" + String.format("%04d", DEFINE29));
                BWRMXMap.put("LIMS_TO_U8ID", sopCode);
                BWRMXMap.put("ITEM_CLASS", "1");
                BWRMXMap.put("CBDEFINE12", SampleMap.get(key).get("TASK_NO"));

                int a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_QD_PRODUCTION_ORDER", BWRMXMap);
                DEFINE29++;
                // }
            }
        }

        return new CurrResponseResolve(1).put("apiData", 1).getData();

    }

    @RequestMapping("/bjww")
    public JSONObject beijingweiwai(@RequestBody JSONObject data) throws Exception {
        String exNO = SysBasic.toTranStringByObject(data.get("EX_DH_NO")); //执行单号
        String DEFINE32 = SysBasic.toTranStringByObject(data.get("DEFINE32")); //阶段
        String username = SysBasic.toTranStringByObject(data.get("username")); //人
        String sopCode = SysBasic.toTranStringByObject(data.get("sopCode")); //
        String sqlEx = SysBasic.toTranStringByObject(data.get("sqlEx")); //
        String sqlCount = SysBasic.toTranStringByObject(data.get("sqlCount")); //
        String SYS_INSERTTIME_JL = SysBasic.toTranStringByObject(data.get("SYS_INSERTTIME_JL"));
        Map<String, Object> sopADDs = (Map<String, Object>) data.get("sopADDs");
        String TYPE = SysBasic.toTranStringByObject(data.get("TYPE")); //混样建库区分常规与MCD
        Date ndate = getmNdate("北京U8生产订单接口", SYS_INSERTTIME_JL);
        double count = SysBasic.toTranDoubleByObject(data.get("count")); //
        double tqMgs = SysBasic.toTranDoubleByObject(data.get("tqMgs")); //
        List<Map<String, Object>> listSOP = (List<Map<String, Object>>) data.get("listSOP");
        List<Map<String, Object>> listSOPMx = (List<Map<String, Object>>) data.get("listSOPMx");
        List<Map<String, Object>> listEx = (List<Map<String, Object>>) data.get("listEx");

        List<Map<String, Object>> SampleMa1p = (List<Map<String, Object>>) data.get("listEx");

        HashMap<String, Map<String, Object>> SampleMap = new LinkedHashMap<>();//各项目期号提取数

        for (int j = 0; j < listEx.size(); j++) {
            Map mapXM = listEx.get(j);

            Double TQ_MG = SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG"));

            String PROJECT_SUBNO =SysBasic.toTranStringByObject(mapXM.get("PROJECT_SUBNO"));
            String TASK_NO =SysBasic.toTranStringByObject(mapXM.get("TASK_NO"));
            String subNo = (String) mapXM.get("PROJECT_SUBNO")+TASK_NO;
            String pName = (String) mapXM.get("PROJECT_NAME");
            String pNo = (String) mapXM.get("PROJECT_NO");
            String cType = (String) mapXM.get("CONTRACT_TYPE");
            String cName = (String) mapXM.get("CONTRACT_NAME");
            String cNo = (String) mapXM.get("CONTRACT_NO");
            Date BTIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("BTIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    BTIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    BTIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }
            Date ETIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("ETIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    ETIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    ETIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }
            String MAN = (String) mapXM.get("MAN");
            double num = 0;
            if (SampleMap.containsKey(subNo)) {
                double sNum = (Double) SampleMap.get(subNo).get("sNum");
                BigDecimal bsNum = new BigDecimal(Double.toString(sNum));
                BigDecimal bDtqMg = new BigDecimal(Double.toString(TQ_MG));
                bsNum = bsNum.add(bDtqMg);
                double d = bsNum.doubleValue();
                SampleMap.get(subNo).put("sNum", d);
                int sNum1 = (int) SampleMap.get(subNo).get("sNum1");
                SampleMap.get(subNo).put("sNum1", sNum1 + 1);

            } else {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("TASK_NO", mapXM.get("TASK_NO"));
                map.put("PROJECT_NAME", pName);
                map.put("PROJECT_SUBNO", PROJECT_SUBNO);
                map.put("PROJECT_NO", pNo);
                map.put("CONTRACT_TYPE", cType);
                map.put("CONTRACT_NAME", cName);
                map.put("CONTRACT_NO", cNo);
                map.put("BTIME", BTIME);
                map.put("ETIME", ETIME);
                map.put("MAN", MAN);
                map.put("sNum", TQ_MG);
                map.put("sNum1", 1);
                SampleMap.put(subNo, map);
            }

        }

        //if (SampleNum > count) count = SampleNum;
        int DEFINE29 = 1;


        Iterator<Map.Entry<String, Map<String, Object>>> entries = SampleMap.entrySet().iterator();
        while (entries.hasNext()) {
            Map.Entry<String, Map<String, Object>> entry = entries.next();
            String key = entry.getKey();
            Map value = entry.getValue();
            if ((Double) value.get("sNum") == 0) entries.remove();
        }


        for (int j = 0; j < listSOP.size(); j++) {


            for (String key : SampleMap.keySet()) {

                double sNum = (Double) SampleMap.get(key).get("sNum");//一期用量
                if (TYPE.equals("混样建库常规")) {
                    sNum = (int) SampleMap.get(key).get("sNum1");
                } //混样建库非常规拆分按数据量，【QUANTITY】数量取样品数

                String sqlU8ID = "select u8ID.nextval from dual";
                Integer u8ID = queryJdbcTemplate.queryForObject(sqlU8ID, Integer.class);
                u8ID = u8ID + 1000000000;

                Map<String, Object> BPBOMap = new HashMap<String, Object>();
                String BWRID = SysBasic.getUUID();
                BPBOMap.put("ID", SysBasic.getUUID());
                BPBOMap.put("EX_DH_NO", exNO);
                BPBOMap.put("EXE_TQQC_ID", listSOP.get(j).get("ID"));
                BPBOMap.put("MOID", sopCode);
                BPBOMap.put("MOCODE", sopCode);
                BPBOMap.put("MODID", u8ID);
                BPBOMap.put("SORTSEQ", DEFINE29);
                BPBOMap.put("MOCLASS", "1");
                BPBOMap.put("MOTYPECODE", "01");
                BPBOMap.put("WHCODE", "10");
                BPBOMap.put("MDEPTCODE", listSOP.get(j).get("U8_DEPT_CODE"));
                BPBOMap.put("BOMTYPE", "1");
                BPBOMap.put("INVCODE", listSOP.get(j).get("SOP_CODE"));
                BPBOMap.put("FREE1", "");
                BPBOMap.put("FREE2", "");
                BPBOMap.put("FREE3", "");
                BPBOMap.put("FREE4", "");
                BPBOMap.put("FREE5", "");
                BPBOMap.put("FREE6", "");
                BPBOMap.put("FREE7", "");
                BPBOMap.put("FREE8", "");
                BPBOMap.put("FREE9", "");
                BPBOMap.put("FREE10", "");
                BPBOMap.put("DEFINE23", SampleMap.get(key).get("CONTRACT_TYPE"));
                BPBOMap.put("DEFINE28", SampleMap.get(key).get("CONTRACT_NO"));

                BPBOMap.put("DEFINE24", SampleMap.get(key).get("PROJECT_NO"));
                BPBOMap.put("DEFINE25", SampleMap.get(key).get("PROJECT_NAME"));
                BPBOMap.put("DEFINE31", SampleMap.get(key).get("MAN"));
                BPBOMap.put("DEFINE32", listSOP.get(j).get("S_STEP"));
                BPBOMap.put("DEFINE33", SampleMap.get(key).get("PROJECT_SUBNO"));
                BPBOMap.put("REMARK", "1");
                BPBOMap.put("STARTDATE", getmindate("北京U8生产订单接口", SYS_INSERTTIME_JL));
                BPBOMap.put("DUEDATE", getmaxdate("北京U8生产订单接口", SYS_INSERTTIME_JL));
                BPBOMap.put("PUSH_STATE", "待推送");
                BPBOMap.put("GENERATE_DATE", ndate);
                BPBOMap.put("DEFINE1", sopCode);
                BPBOMap.put("IMPUTATIONDATA", ndate);
                BPBOMap.put("DEFINE29", sopCode + "-" + String.format("%04d", DEFINE29));
                BPBOMap.put("QTY", sNum);
                BPBOMap.put("CBDEFINE12", SampleMap.get(key).get("TASK_NO"));
                SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_BJ_PRODUCTION_ORDER", BPBOMap);
                DEFINE29++;
                for (int i = 0; i < listSOPMx.size(); i++) {

                    String sqlhs = "SELECT  bsm.CHANGE_RATE FROM BIO_SC_MATERIEL bsm WHERE bsm.W_CODE= ?";
                    List<Map<String, Object>> sqlhsl = queryJdbcTemplate.queryForList(sqlhs, listSOPMx.get(i).get("W_CODE"));

                    double hsl = SysBasic.toTranDoubleByObject(sqlhsl.get(0).get("CHANGE_RATE"));


                    Map mapSOP = listSOPMx.get(i);
                    double num = (Double) SampleMap.get(key).get("sNum");//一期用量
                    double standude = SysBasic.toTranDoubleByObject(mapSOP.get("M_STANDUSE")); //标准用量
                    double loss = SysBasic.toTranDoubleByObject(mapSOP.get("M_LOSS")); //损耗
                    double mFNum = SysBasic.toTranDoubleByObject(mapSOP.get("M_FY_NUMBER")); //反应数
                    double pTotal = SysBasic.toTranDoubleByObject(mapSOP.get("PRACTICAL_TOTAL")); //实际总量
                    String subNo = (String) SampleMap.get(key).get("PROJECT_SUBNO"); //项目期号
                    String pNo = (String) SampleMap.get(key).get("PROJECT_NO");   //项目编号
                    String cNo = (String) SampleMap.get(key).get("CONTRACT_NO");  //合同编号
                    String TASK_NO = (String) SampleMap.get(key).get("TASK_NO");

                    BigDecimal bDpTotal = new BigDecimal(Double.toString(pTotal));
                    BigDecimal bDnum = new BigDecimal(Double.toString(num));
                    BigDecimal bDtqMgs = new BigDecimal(Double.toString(tqMgs));
                    BigDecimal subtract = (bDnum.divide(bDtqMgs, 6, BigDecimal.ROUND_DOWN)).multiply(bDpTotal).setScale(6, BigDecimal.ROUND_DOWN);
                    ;

                    String noKey = mapSOP.get("ID") + "-" + key;
                    if (sopADDs.get(noKey) != null) {
                        Double aDouble = (Double) sopADDs.get(noKey);
                        BigDecimal bDaDouble = new BigDecimal(Double.toString(aDouble));
                        subtract = subtract.add(bDaDouble);
                    }
                    Double result = subtract.doubleValue();

                    Map<String, Object> BWRMXMap = new HashMap<String, Object>();
                    String sqlallID = "select u8ID.nextval from dual";
                    Integer allID = queryJdbcTemplate.queryForObject(sqlallID, Integer.class);
                    allID = allID + 1000000000;

                    BWRMXMap.put("ID", SysBasic.getUUID());
                    BWRMXMap.put("ALLOCATEID", allID);
                    BWRMXMap.put("MODID", u8ID);
                    BWRMXMap.put("SORTSEQ", i + 1);
                    BWRMXMap.put("BASEQTYN", standude * 1000);
                    BWRMXMap.put("BASEQTYD", "1000");
                    BWRMXMap.put("QTY", ((standude * 1000) / 1000) * result);
                    BWRMXMap.put("STARTDEMDATE", getmindate("北京U8生产订单接口", SYS_INSERTTIME_JL));
                    BWRMXMap.put("ENDDEMDATE", getmaxdate("北京U8生产订单接口", SYS_INSERTTIME_JL));
                    BWRMXMap.put("WHCODE", "06");
                    BWRMXMap.put("WIPTYPE", "3");
                    BWRMXMap.put("BYPRODUCTFLAG", "false");
                    BWRMXMap.put("INVCODE", listSOPMx.get(i).get("W_CODE"));
                    BWRMXMap.put("FREE1", "");
                    BWRMXMap.put("FREE2", "");
                    BWRMXMap.put("FREE3", "");
                    BWRMXMap.put("FREE4", "");
                    BWRMXMap.put("FREE5", "");
                    BWRMXMap.put("FREE6", "");
                    BWRMXMap.put("FREE7", "");
                    BWRMXMap.put("FREE8", "");
                    BWRMXMap.put("FREE9", "");
                    BWRMXMap.put("FREE10", "");
                    BWRMXMap.put("AUXUNITCODE", listSOPMx.get(i).get("W_UNIT_CODE"));
                    BWRMXMap.put("CHANGERATE", hsl);
                    Double tyn = 0.0;
                    Double ty = 0.0;
                    if (hsl != 0) {
                        tyn = (standude * 1000) / hsl;
                        ty = ((standude * 1000) / 1000) * result / hsl;
                    }
                    BWRMXMap.put("AUXBASEQTYN", tyn);
                    BWRMXMap.put("AUXQTY", ty);


                    SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_BJ_PRODUCTION_ALLOCATEMX", BWRMXMap);
                }
            }
        }
        return new CurrResponseResolve(1).put("apiData", 1).getData();

    }

    @RequestMapping("/scclck")
    public JSONObject shengChanChaiLiaoChuKu(@RequestBody JSONObject data) throws Exception {
        String sqlEx = SysBasic.toTranStringByObject(data.get("sqlEx")); //
        String exNO = SysBasic.toTranStringByObject(data.get("EX_DH_NO")); //执行单号
        Map<String, Object> sopADDs = (Map<String, Object>) data.get("sopADDs");
        String DEFINE32 = SysBasic.toTranStringByObject(data.get("DEFINE32")); //阶段
        String username = SysBasic.toTranStringByObject(data.get("username")); //人
        String sopCode = SysBasic.toTranStringByObject(data.get("sopCode")); //
        String SYS_INSERTTIME_JL = SysBasic.toTranStringByObject(data.get("SYS_INSERTTIME_JL"));
        String sqlCount = SysBasic.toTranStringByObject(data.get("sqlCount")); //
        int libNum = SysBasic.toTranIntegerByObject(data.get("libNum")); //建库
        double count = SysBasic.toTranIntegerByObject(data.get("count")); //
        double tqMgs = SysBasic.toTranDoubleByObject(data.get("tqMgs")); //
        Date ndate = getmNdate("材料出库单-生产", SYS_INSERTTIME_JL);

        List<Map<String, Object>> listSOP = (List<Map<String, Object>>) data.get("listSOP");
        List<Map<String, Object>> listSOPMx = (List<Map<String, Object>>) data.get("listSOPMx");
        List<Map<String, Object>> listEx = (List<Map<String, Object>>) data.get("listEx");

        double SampleNum = 0;//总提取数
        HashMap<String, Map<String, Object>> SampleMap = new LinkedHashMap<>();//各项目期号提取数


        for (int j = 0; j < listEx.size(); j++) {
            Map mapXM = listEx.get(j);
            SampleNum = SampleNum + SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG"));
            String PROJECT_SUBNO =SysBasic.toTranStringByObject(mapXM.get("PROJECT_SUBNO"));
            String TASK_NO =SysBasic.toTranStringByObject(mapXM.get("TASK_NO"));
            String subNo = (String) mapXM.get("PROJECT_SUBNO")+TASK_NO;
            String pName = (String) mapXM.get("PROJECT_NAME");
            String pNo = (String) mapXM.get("PROJECT_NO");
            String cType = (String) mapXM.get("CONTRACT_TYPE");
            String cNo = (String) mapXM.get("CONTRACT_NO");  //合同编号
            String pCode = (String) mapXM.get("PRODUCT_CODE");
            Date BTIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("BTIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    BTIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    BTIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }
            Date ETIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("ETIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    ETIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    ETIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }

            String MAN = (String) mapXM.get("MAN");

            double num = 0;
            if (SampleMap.containsKey(subNo)) {
                double sNum = (Double) SampleMap.get(subNo).get("sNum");
                double tq_mg = SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG"));

                BigDecimal bsNum = new BigDecimal(Double.toString(sNum));
                BigDecimal bDtqMg = new BigDecimal(Double.toString(tq_mg));
                bsNum = bsNum.add(bDtqMg);
                double d = bsNum.doubleValue();
                SampleMap.get(subNo).put("sNum", d);

            } else {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("TASK_NO", mapXM.get("TASK_NO"));
                map.put("PROJECT_NAME", pName);
                map.put("PROJECT_SUBNO", PROJECT_SUBNO);
                map.put("PROJECT_NO", pNo);
                map.put("CONTRACT_NO", cNo);
                map.put("CONTRACT_TYPE", cType);
                map.put("PRODUCT_CODE", pCode);
                map.put("BTIME", BTIME);
                map.put("ETIME", ETIME);
                map.put("MAN", MAN);
                map.put("sNum", SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG")));
                SampleMap.put(subNo, map);
            }
        }
        Iterator iter = SampleMap.entrySet().iterator();
        Iterator<Map.Entry<String, Map<String, Object>>> entries = SampleMap.entrySet().iterator();
        while (entries.hasNext()) {
            Map.Entry<String, Map<String, Object>> entry = entries.next();
            String key = entry.getKey();
            Map value = entry.getValue();
            if ((Double) value.get("sNum") == 0) entries.remove();
        }

        for (int j = 0; j < listSOP.size(); j++) {


            Map<String, Object> BOSMap = new HashMap<String, Object>();
            String BSOID = SysBasic.getUUID();
            BOSMap.put("EX_DH_NO", exNO);
            BOSMap.put("EXE_TQQC_ID", listSOP.get(j).get("ID"));
            BOSMap.put("ID", BSOID);
            BOSMap.put("TRADE_NO", sopCode);
            //BOSMap.put("DEFINE4", "1");  //备注
            BOSMap.put("DEFINE5", count);
            BOSMap.put("DEFINE1", exNO);
            int LINENUMBER = 0;
            Date BTIME = new Date();
            Date ETIME = new Date();
            String MAN = "";
            String VUDEF7 = "";  //阶段

            for (String key : SampleMap.keySet()) {

                Map<String, Object> BPBOMap = new HashMap<String, Object>();
                String BWRID = SysBasic.getUUID();
                BPBOMap.put("ID", BWRID);
                BPBOMap.put("SOURCING_ID", BSOID);
                BPBOMap.put("LINENUMBER", ++LINENUMBER);
                BPBOMap.put("PRODUCTCODE", listSOP.get(j).get("SOP_CODE"));
                BPBOMap.put("VDEF2", listSOP.get(j).get("SOP_NAME"));
                BTIME = (Date) SampleMap.get(key).get("BTIME");
                ETIME = (Date) SampleMap.get(key).get("ETIME");
                MAN = (String) SampleMap.get(key).get("MAN");
                SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_OUT_SOURCING_DETAILMX", BPBOMap);
                for (int i = 0; i < listSOPMx.size(); i++) {

                    Map mapSOP = listSOPMx.get(i);
                    double num = (Double) SampleMap.get(key).get("sNum");//一期用量
                    double standude = SysBasic.toTranDoubleByObject(mapSOP.get("M_STANDUSE")); //标准用量
                    double loss = SysBasic.toTranDoubleByObject(mapSOP.get("M_LOSS")); //损耗
                    double mFNum = SysBasic.toTranDoubleByObject(mapSOP.get("M_FY_NUMBER")); //反应数
                    double pTotal = SysBasic.toTranDoubleByObject(mapSOP.get("PRACTICAL_TOTAL")); //实际总量
                    String subNo = (String) SampleMap.get(key).get("PROJECT_SUBNO"); //项目期号
                    String pNo = (String) SampleMap.get(key).get("PROJECT_NO");   //项目编号
                    String cNo = (String) SampleMap.get(key).get("CONTRACT_NO");  //合同编号
                    String TASK_NO = (String) SampleMap.get(key).get("TASK_NO");

                    BigDecimal bDpTotal = new BigDecimal(Double.toString(pTotal));
                    BigDecimal bDnum = new BigDecimal(Double.toString(num));
                    BigDecimal bDtqMgs = new BigDecimal(Double.toString(tqMgs));
                    BigDecimal subtract = (bDnum.divide(bDtqMgs, 6, BigDecimal.ROUND_DOWN)).multiply(bDpTotal).setScale(6, BigDecimal.ROUND_DOWN);
                    ;

                    String noKey = mapSOP.get("ID") + "-" + key;
                    if (sopADDs.get(noKey) != null) {
                        Double aDouble = (Double) sopADDs.get(noKey);
                        BigDecimal bDaDouble = new BigDecimal(Double.toString(aDouble));
                        subtract = subtract.add(bDaDouble);
                    }
                    Double result = subtract.doubleValue();

                    String sqlhs = "SELECT  bsm.CHANGE_RATE FROM BIO_SC_MATERIEL bsm WHERE bsm.W_CODE= ?";
                    List<Map<String, Object>> sqlhsl = queryJdbcTemplate.queryForList(sqlhs, listSOPMx.get(i).get("W_CODE"));
                    double hsl = SysBasic.toTranDoubleByObject(sqlhsl.get(0).get("CHANGE_RATE"));

                    Map<String, Object> BWRMXMap = new HashMap<String, Object>();
                    BWRMXMap.put("ID", SysBasic.getUUID());
                    BWRMXMap.put("Detail_ID", BWRID);
                    BWRMXMap.put("MATERIALCODE", mapSOP.get("W_CODE"));
                    BWRMXMap.put("PROJECT", SampleMap.get(key).get("CONTRACT_NO"));
                    BWRMXMap.put("SNUM", result);
                    // BWRMXMap.put("PRICE", 1);
                    BWRMXMap.put("VUDEF1", SampleMap.get(key).get("PROJECT_NO"));
                    BWRMXMap.put("VUDEF2", SampleMap.get(key).get("PROJECT_NAME"));
                    BWRMXMap.put("VUDEF3", mapSOP.get("W_MATERIAL_BATCH"));
                    BWRMXMap.put("VUDEF4", mapSOP.get("W_MATERIAL_DATE"));
                    BWRMXMap.put("VUDEF5", listSOP.get(j).get("S_STEP"));
                    BWRMXMap.put("VUDEF6", mapSOP.get("W_NAME"));
                    BWRMXMap.put("VUDEF7", listSOP.get(j).get("S_STEP"));
                    VUDEF7 = (String) listSOP.get(j).get("S_STEP");
                    BWRMXMap.put("VUDEF8", SampleMap.get(key).get("PROJECT_SUBNO"));
                    BWRMXMap.put("RID", i);
                    BWRMXMap.put("CBDEFINE12", SampleMap.get(key).get("TASK_NO"));

                    SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_OUT_SOURCING_MATERMX", BWRMXMap);
                }
            }
            if (!(VUDEF7.equals("提取"))) {
                BOSMap.put("DEFINE6", libNum);//建库数
            }
            BOSMap.put("DEFINE7", BTIME);
            BOSMap.put("DEFINE8", ETIME);
            BOSMap.put("DEFINE9", MAN);
            BOSMap.put("LIMS_TO_U8ID", "");
            BOSMap.put("GENERATE_DATE", ndate);
            BOSMap.put("DEFINE2", ndate);
            BOSMap.put("PUSH_STATE", "待推送");
            SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_OUT_SOURCING", BOSMap);
        }
        return new CurrResponseResolve(1).put("apiData", 1).getData();
    }

    @RequestMapping("/scyfckd")
    public JSONObject sgengChanYanFaChuKuDan(@RequestBody JSONObject data) throws Exception {
        String sqlEx = SysBasic.toTranStringByObject(data.get("sqlEx")); //
        String exNO = SysBasic.toTranStringByObject(data.get("EX_DH_NO")); //执行单号
        String DEFINE32 = SysBasic.toTranStringByObject(data.get("DEFINE32")); //阶段
        String username = SysBasic.toTranStringByObject(data.get("username")); //人
        //String sopCode = SysBasic.toTranStringByObject(data.get("sopCode")); //
        String SYS_INSERTTIME_JL = SysBasic.toTranStringByObject(data.get("SYS_INSERTTIME_JL"));
        String sqlCount = SysBasic.toTranStringByObject(data.get("sqlCount")); //
        Date ndate = getmNdate("材料出库单-研发", SYS_INSERTTIME_JL);

        double tqMgs = SysBasic.toTranDoubleByObject(data.get("tqMgs")); //
        double count = SysBasic.toTranIntegerByObject(data.get("count")); //
        List<Map<String, Object>> listSOP = (List<Map<String, Object>>) data.get("listSOP");
        List<Map<String, Object>> listSOPMx = (List<Map<String, Object>>) data.get("listSOPMx");
        List<Map<String, Object>> listEx = (List<Map<String, Object>>) data.get("listEx");
        Map<String, Object> sopADDs = (Map<String, Object>) data.get("sopADDs");


        double SampleNum = 0;//总提取数
        HashMap<String, Map<String, Object>> SampleMap = new LinkedHashMap<>();//各项目期号提取数


        for (int j = 0; j < listEx.size(); j++) {
            Map mapXM = listEx.get(j);
            SampleNum = SampleNum + SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG"));
            String PROJECT_SUBNO =SysBasic.toTranStringByObject(mapXM.get("PROJECT_SUBNO"));
            String TASK_NO =SysBasic.toTranStringByObject(mapXM.get("TASK_NO"));
            String subNo = (String) mapXM.get("PROJECT_SUBNO")+TASK_NO;
            String pName = (String) mapXM.get("PROJECT_NAME");
            String pNo = (String) mapXM.get("PROJECT_NO");
            String cType = (String) mapXM.get("CONTRACT_TYPE");
            String pCode = (String) mapXM.get("PRODUCT_CODE");
            String rdDept = (String) mapXM.get("RD_DEPT");
            String rdDeptCode = (String) mapXM.get("RD_DEPT_CODE"); //部门编码
            Date BTIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("BTIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    BTIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    BTIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }
            Date ETIME = null;
            try {
                String date = SysBasic.toTranStringByObject(mapXM.get("ETIME"));
                if (date.contains("-")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    ETIME = simpleDateFormat.parse(date);
                }
                if (date.contains("/")) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                    ETIME = simpleDateFormat.parse(date);
                }
            } catch (Exception e) {
            }
            String MAN = (String) mapXM.get("MAN");
            double num = 0;
            if (SampleMap.containsKey(subNo)) {
                double sNum = (Double) SampleMap.get(subNo).get("sNum");
                double tq_mg = SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG"));
                BigDecimal bsNum = new BigDecimal(Double.toString(sNum));
                BigDecimal bDtqMg = new BigDecimal(Double.toString(tq_mg));
                bsNum = bsNum.add(bDtqMg);
                double d = bsNum.doubleValue();
                SampleMap.get(subNo).put("sNum", d);

            } else {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("PROJECT_NAME", pName);
                map.put("PROJECT_SUBNO", PROJECT_SUBNO);
                map.put("PROJECT_NO", pNo);
                map.put("CONTRACT_TYPE", cType);
                map.put("PRODUCT_CODE", pCode);
                map.put("RD_DEPT", rdDept);
                map.put("RD_DEPT_CODE", rdDeptCode);
                map.put("BTIME", BTIME);
                map.put("ETIME", ETIME);
                map.put("MAN", MAN);
                map.put("sNum", SysBasic.toTranDoubleByObject(mapXM.get("TQ_MG")));
                SampleMap.put(subNo, map);
            }
        }

        int DEFINE29 = 0;
        Iterator iter = SampleMap.entrySet().iterator();
        Iterator<Map.Entry<String, Map<String, Object>>> entries = SampleMap.entrySet().iterator();
        while (entries.hasNext()) {
            Map.Entry<String, Map<String, Object>> entry = entries.next();
            String key = entry.getKey();
            Map value = entry.getValue();
            if ((Double) value.get("sNum") == 0) entries.remove();
        }

        for (String key : SampleMap.keySet()) {
            for (int j = 0; j < listSOP.size(); j++) {

                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String[] split = sdf.format(date).split("-");
                String sCode = getSopCode("材料出库单-研发", SYS_INSERTTIME_JL);
                String sql = "select max(OUTBOUND_CODE) from BIO_GOO_RDRECORD09 where OUTBOUND_CODE like '%'||?||'%'";
                String sqlCode = queryJdbcTemplate.queryForObject(sql, String.class, sCode);
                String sopCode = "";
                if (sqlCode != null) {

                    sopCode = sCode + String.format("%03d", Integer.parseInt(sqlCode.substring(6)) + 1);
                } else {
                    sopCode = sCode + "001";
                }

                Map<String, Object> BPBOMap = new HashMap<String, Object>();
                String BWRID = SysBasic.getUUID();
                BPBOMap.put("ID", sopCode);
                BPBOMap.put("EX_DH_NO", exNO);
                BPBOMap.put("EXE_TQQC_ID", listSOP.get(j).get("ID"));
                BPBOMap.put("DEFINE1", exNO);
                BPBOMap.put("VOUCHTYPE", "09");
                BPBOMap.put("BUSINESSTYPE", "其他出库");
                String WAREHOUSECODE = "06";
                if (listSOP.get(j).get("S_WAREHOUSE").equals("研发库")) WAREHOUSECODE = "07";
                BPBOMap.put("WAREHOUSECODE", WAREHOUSECODE);
                BPBOMap.put("OUTBOUND_DATE", ndate);
                BPBOMap.put("OUTBOUND_CODE", sopCode);
                BPBOMap.put("RECEIVECODE", "204");
                //BPBOMap.put("MEMORY", "1");
                BPBOMap.put("MAKER", "demo");
                BPBOMap.put("DEFINE10", (String) SampleMap.get(key).get("RD_DEPT"));
                String DEPARTMENTCODE = "";
                String DEFINE10 = (String) SampleMap.get(key).get("RD_DEPT");
                try {
                    if (DEFINE10.equals("公司研发-创新中心")) {
                        DEPARTMENTCODE = "5103";
                    }
                    if (DEFINE10.equals("公司研发-产品研发平台")) {
                        DEPARTMENTCODE = "5102";
                    }
                    if (DEFINE10.equals("公司研发-公司研发平台")) {
                        DEPARTMENTCODE = "5101";
                    }
                    if (listEx.get(0).get("RD_DEPT").equals("智造研发部")) {
                        DEPARTMENTCODE = "5802";
                    }
                } catch (Exception e) {
                }

                BPBOMap.put("DEPARTMENTCODE", DEPARTMENTCODE);
                BPBOMap.put("PUSH_STATE", "待推送");
                BPBOMap.put("GENERATE_DATE", ndate);
                BPBOMap.put("DEFINE4", (Date) SampleMap.get(key).get("ETIME"));
                BPBOMap.put("DEFINE6", (Date) SampleMap.get(key).get("BTIME"));
                BPBOMap.put("DEFINE12", (String) SampleMap.get(key).get("MAN"));
                SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_GOO_RDRECORD09", BPBOMap);
                for (int i = 0; i < listSOPMx.size(); i++) {
                    Map mapSOP = listSOPMx.get(i);
                    double mfn = SysBasic.toTranDoubleByObject(mapSOP.get("M_FY_NUMBER")); //反应数
                    double standude = SysBasic.toTranDoubleByObject(mapSOP.get("M_STANDUSE")); //标准用量
                    double loss = SysBasic.toTranDoubleByObject(mapSOP.get("M_LOSS")); //损耗
                    double pTotal = SysBasic.toTranDoubleByObject(mapSOP.get("PRACTICAL_TOTAL")); //实际总用量
                    double num = (Double) SampleMap.get(key).get("sNum");//一期用量
                    String subNo = (String) SampleMap.get(key).get("PROJECT_SUBNO"); //项目期号
                    String pNo = (String) SampleMap.get(key).get("PROJECT_NO");   //项目编号
                    String cNo = (String) SampleMap.get(key).get("CONTRACT_NO");  //合同编号
                    String TASK_NO = (String) SampleMap.get(key).get("TASK_NO");


                    BigDecimal bDpTotal = new BigDecimal(Double.toString(pTotal));
                    BigDecimal bDnum = new BigDecimal(Double.toString(num));
                    BigDecimal bDtqMgs = new BigDecimal(Double.toString(tqMgs));
                    BigDecimal subtract = (bDnum.divide(bDtqMgs, 6, BigDecimal.ROUND_DOWN)).multiply(bDpTotal).setScale(6, BigDecimal.ROUND_DOWN);
                    ;

                    String noKey = mapSOP.get("ID") + "-" + key;
                    if (sopADDs.get(noKey) != null) {
                        Double aDouble = (Double) sopADDs.get(noKey);
                        BigDecimal bDaDouble = new BigDecimal(Double.toString(aDouble));
                        subtract = subtract.add(bDaDouble);
                    }
                    Double result = subtract.doubleValue();
                    Map<String, Object> BWRMXMap = new HashMap<String, Object>();
                    String sqlallID = "select u8ID.nextval from dual";
                    Integer allID = queryJdbcTemplate.queryForObject(sqlallID, Integer.class);
                    allID = allID + 1000000000;
                    String sqlhs = "SELECT  bsm.CHANGE_RATE FROM BIO_SC_MATERIEL bsm WHERE bsm.W_CODE= ?";
                    List<Map<String, Object>> sqlhsl = queryJdbcTemplate.queryForList(sqlhs, listSOPMx.get(i).get("W_CODE"));
                    double hsl = SysBasic.toTranDoubleByObject(sqlhsl.get(0).get("CHANGE_RATE"));
                    BWRMXMap.put("ID", allID);
                    BWRMXMap.put("RDRECORD09_ID", sopCode);
                    BWRMXMap.put("INVENTORYCODE", listSOPMx.get(i).get("W_CODE"));
                    BWRMXMap.put("QUANTITY", result);
                    BWRMXMap.put("CMASSUNITNAME", listSOPMx.get(i).get("W_UNIT"));
                    BWRMXMap.put("ASSITANTUNITNAME", listSOPMx.get(i).get("W_UNIT"));
                    BWRMXMap.put("IRATE", hsl);
                    BWRMXMap.put("NUMS", num);
                    BWRMXMap.put("SERIAL", listSOPMx.get(i).get("W_MATERIAL_BATCH"));
                    BWRMXMap.put("VALIDDATE", listSOPMx.get(i).get("W_MATERIAL_DATE"));
                    BWRMXMap.put("ITEMCLASSCODE", "00");
                    BWRMXMap.put("ITEMCLASSNAME", "T3项目管理");
                    BWRMXMap.put("ITEMNAME", SampleMap.get(key).get("PROJECT_NAME"));
                    BWRMXMap.put("ITEMCODE", SampleMap.get(key).get("PROJECT_NO"));
                    BWRMXMap.put("DEFINE22", listSOP.get(j).get("S_STEP"));
                    BWRMXMap.put("DEFINE32", listSOP.get(j).get("S_STEP"));
                    BWRMXMap.put("DEFINE24", listSOPMx.get(i).get("W_NAME"));
                    BWRMXMap.put("DEFINE33", SampleMap.get(key).get("PROJECT_SUBNO"));
                    // BWRMXMap.put("MEMORY", "1");
                    SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_GOO_RDRECORDS09MX", BWRMXMap);
                }
            }
        }
        return new CurrResponseResolve(1).put("apiData", 1).getData();

    }

    @RequestMapping("/yfckd")
    public JSONObject yanFaChuKuDan(@RequestBody JSONObject data) throws Exception {
        String exNO = SysBasic.toTranStringByObject(data.get("EX_DH_NO")); //执行单号
        String DEFINE32 = SysBasic.toTranStringByObject(data.get("DEFINE32")); //阶段
        String username = SysBasic.toTranStringByObject(data.get("username")); //人
        String SYS_INSERTTIME_JL = SysBasic.toTranStringByObject(data.get("SYS_INSERTTIME_JL"));
        Date ndate = getmNdate("材料出库单-研发", SYS_INSERTTIME_JL);


        String sqlEx = "SELECT * FROM BIO_RD_TASK brt where brt.RD_TASK_NO = ?"; //
        List<Map<String, Object>> listEx = queryJdbcTemplate.queryForList(sqlEx, exNO);

        String project_no = (String) listEx.get(0).get("RD_TASK_TYPE");//工序


        String sqlSop = "select * from BIO_BZ_MATERIEL_SOP_JL   where  " +
                " (COLLECTION_FLAG !='已归集' or COLLECTION_FLAG is null) and EX_DH_NO=?";

        String sqlSopMx = "select bsjm.* from BIO_BZ_MATERIEL_SOP_JL  bsj,BIO_BZ_MATERIEL_SOP_JL_MX bsjm where bsj.ID=bsjm.BIO_BZ_MATERIEL_SOP_ID " +
                "and (bsjm.COLLECTION_FLAG !='已归集' or bsjm.COLLECTION_FLAG is null) and bsj.EX_DH_NO=?";


        List<Map<String, Object>> listSOP = queryJdbcTemplate.queryForList(sqlSop, exNO);
        List<Map<String, Object>> listSOPMx = queryJdbcTemplate.queryForList(sqlSopMx, exNO);

        double SampleNum = 0;//总提取数
        HashMap<String, List<Map<String, Object>>> SampleMap = new LinkedHashMap<>();//各项目期号提取数

        for (int j = 0; j < listSOPMx.size(); j++) {
            Map mapXM = listSOPMx.get(j);
            String sw = (String) mapXM.get("S_WAREHOUSE");
            if (SampleMap.containsKey(sw)) {
                SampleMap.get(sw).add(mapXM);
            } else {
                List<Map<String, Object>> list = new LinkedList<>();
                list.add(mapXM);
                SampleMap.put(sw, list);
            }
        }
        for (String key : SampleMap.keySet()) {
            int DEFINE29 = 0;
            for (int j = 0; j < listSOP.size(); j++) {


                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String[] split = sdf.format(date).split("-");
                String sCode = getSopCode("材料出库单-研发", SYS_INSERTTIME_JL);
                String sql = "select max(OUTBOUND_CODE) from BIO_GOO_RDRECORD09 where OUTBOUND_CODE like '%'||?||'%'";
                String sqlCode = queryJdbcTemplate.queryForObject(sql, String.class, sCode);
                String sopCode = "";
                if (sqlCode != null) {

                    sopCode = sCode + String.format("%03d", Integer.parseInt(sqlCode.substring(6)) + 1);
                } else {
                    sopCode = sCode + "001";
                }

                Map<String, Object> BPBOMap = new HashMap<String, Object>();
                String BWRID = SysBasic.getUUID();
                BPBOMap.put("ID", sopCode);
                BPBOMap.put("EXE_TQQC_ID", listSOP.get(j).get("ID"));
                BPBOMap.put("EX_DH_NO", exNO);
                BPBOMap.put("DEFINE1", exNO);
                BPBOMap.put("VOUCHTYPE", "09");
                BPBOMap.put("BUSINESSTYPE", "其他出库");
                String WAREHOUSECODE = "06";
                if (key.equals("研发库")) {
                    WAREHOUSECODE = "07";
                }
                BPBOMap.put("WAREHOUSECODE", WAREHOUSECODE);
                BPBOMap.put("OUTBOUND_DATE", ndate);
                BPBOMap.put("OUTBOUND_CODE", sopCode);
                BPBOMap.put("RECEIVECODE", "204");
                String DEPARTMENTCODE = "";
                if (listEx.get(0).get("RD_DEPT").equals("公司研发-创新中心")) {
                    DEPARTMENTCODE = "5103";
                }
                if (listEx.get(0).get("RD_DEPT").equals("公司研发-产品研发平台")) {
                    DEPARTMENTCODE = "5102";
                }
                if (listEx.get(0).get("RD_DEPT").equals("公司研发-公司研发平台")) {
                    DEPARTMENTCODE = "5101";
                }
                if (listEx.get(0).get("RD_DEPT").equals("智造研发部")) {
                    DEPARTMENTCODE = "5802";
                }
                BPBOMap.put("DEPARTMENTCODE", DEPARTMENTCODE);

                BPBOMap.put("MEMORY", listEx.get(0).get("RD_RS_REMARK"));

                BPBOMap.put("MAKER", "demo");
                BPBOMap.put("DEFINE4", listEx.get(0).get("REALITY_END_TIME"));
                BPBOMap.put("DEFINE6", listEx.get(0).get("ORDER_TIME"));
                BPBOMap.put("DEFINE10", listEx.get(0).get("RD_DEPT"));
                BPBOMap.put("DEFINE12", listEx.get(0).get("EXEC_MAN"));
                BPBOMap.put("PUSH_STATE", "待推送");
                BPBOMap.put("GENERATE_DATE", ndate);

                int a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_GOO_RDRECORD09", BPBOMap);

                for (int i = 0; i < SampleMap.get(key).size(); i++) {

                    Map mapSOP = SampleMap.get(key).get(i);
                    double mfn = SysBasic.toTranDoubleByObject(mapSOP.get("M_FY_NUMBER")); //反应数
                    double standude = SysBasic.toTranDoubleByObject(mapSOP.get("M_STANDUSE")); //标准用量
                    double loss = SysBasic.toTranDoubleByObject(mapSOP.get("M_LOSS")); //损耗
                    double pTotal = SysBasic.toTranDoubleByObject(mapSOP.get("PRACTICAL_TOTAL")); //实际总用量
                    Double result = pTotal;
                    Map<String, Object> BWRMXMap = new HashMap<String, Object>();
                    String sqlallID = "select u8ID.nextval from dual";
                    Integer allID = queryJdbcTemplate.queryForObject(sqlallID, Integer.class);
                    allID = allID + 1000000000;
                    String sqlhs = "SELECT  bsm.CHANGE_RATE FROM BIO_SC_MATERIEL bsm WHERE bsm.W_CODE= ?";
                    List<Map<String, Object>> sqlhsl = queryJdbcTemplate.queryForList(sqlhs, SampleMap.get(key).get(i).get("W_CODE"));
                    double hsl = SysBasic.toTranDoubleByObject(sqlhsl.get(0).get("CHANGE_RATE"));
                    BWRMXMap.put("ID", allID);
                    BWRMXMap.put("RDRECORD09_ID", sopCode);
                    BWRMXMap.put("INVENTORYCODE", SampleMap.get(key).get(i).get("W_CODE"));
                    BWRMXMap.put("QUANTITY", SampleMap.get(key).get(i).get("PRACTICAL_TOTAL"));
                    BWRMXMap.put("CMASSUNITNAME", SampleMap.get(key).get(i).get("W_UNIT"));
                    BWRMXMap.put("ASSITANTUNITNAME", SampleMap.get(key).get(i).get("W_UNIT"));
                    BWRMXMap.put("SERIAL", SampleMap.get(key).get(i).get("W_MATERIAL_BATCH"));
                    Date date1 = new Date();
                    try {
                        Object w_material_date1 = SampleMap.get(key).get(i).get("W_MATERIAL_DATE");
                        Timestamp w_material_date = (Timestamp) SysBasic.toTranDateByValidAndFormat(w_material_date1);
                        date1 = w_material_date;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    BWRMXMap.put("PRICE", 1);
                    BWRMXMap.put("COST", 1);
                    BWRMXMap.put("IRATE", hsl);
                    BWRMXMap.put("VALIDDATE", date1);
                    BWRMXMap.put("ITEMCLASSCODE", "00");
                    BWRMXMap.put("ITEMCLASSNAME", "T3项目管理");
                    BWRMXMap.put("ITEMNAME", listEx.get(0).get("PROJECT_NAME"));
                    BWRMXMap.put("ITEMCODE", listEx.get(0).get("PROJECT_NO"));
                    BWRMXMap.put("DEFINE22", SampleMap.get(key).get(i).get("S_WORKBY"));
                    BWRMXMap.put("DEFINE24", SampleMap.get(key).get(i).get("W_NAME"));
                    BWRMXMap.put("DEFINE33", listEx.get(0).get("PROJECT_SUBNO"));
                    BWRMXMap.put("DEFINE32", SampleMap.get(key).get(i).get("S_WORKBY"));
                    BWRMXMap.put("MEMORY", listEx.get(0).get("RD_RS_REMARK"));
                    BWRMXMap.put("NUMS", SampleMap.get(key).get(i).get("M_FY_NUMBER"));
                    a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_GOO_RDRECORDS09MX", BWRMXMap);
                }

            }
        }


        return new CurrResponseResolve(1).put("apiData", 1).getData();

    }

    // 委外建库测序任务结果填写 节点 ，批量填写
    @RequestMapping(value ="/wwjkcb", produces = "application/json;charset=UTF-8")
    public JSONObject wwJanKuCost(@RequestBody Map<String, Object> map) {

        String info = SysBasic.toTranStringByObject(map.get("info"));
        String username = SysBasic.toTranStringByObject(map.get("username"));
        List<List<String>> lists = SysBasic.toTranListsByImportInfo(info);
        String msg = "";
        int code = 1 ;
        String sqlWWBM="SELECT\n" +
                "bwjr.ID, -- {\"title\":\"唯一标识\",\"field\":\"ID\",\"type\":\"string\",\"width\":280,\"sort\":1,\"hidden\":true,\"menu\":false} \n" +
                "bwjr.DEPARTMENTCODE, -- {\"title\":\"部门编码\",\"width\":150}\n" +
                "bwjr.MEHOD_CBJRLAT -- {\"title\":\"成本记入部门\",\"width\":150}\n" +
                "FROM BIO_WW_JK_RULES bwjr";
        List<Map<String, Object>> listWWBM = queryJdbcTemplate.queryForList(sqlWWBM);


        for (int i = 0; i < lists.size(); i++) {

            List<String> list = lists.get(i);
            int proof =0;
            for (int j = 0; j < listWWBM.size(); j++) {
                if(listWWBM.get(j).get("DEPARTMENTCODE").equals(list.get(11)) && listWWBM.get(j).get("MEHOD_CBJRLAT").equals(list.get(12))  ){
                    proof=1;
                }
            }
            if(proof==0){
                code  = 0;
                msg = msg + "任务单编号【" + list.get(1) + "】对应的部门在配置中位找到；导入失败！";
                continue;
            }

            //查询当前选中单是否已经生成委外成本结算
            String sqlWWZD= "select bwpr.ID ,bwprmx.ID as MXID\n" +
                    "FROM BIO_WW_PURCHASE_RECEIPT bwpr \n" +
                    "LEFT JOIN BIO_WW_PURCHASE_RECEIPT_MX bwprmx on bwpr.BILL_ID=bwprmx.BILL_ID\n" +
                    "where bwpr.WW_TASK_NO = ? ";
            List<Map<String, Object>> listWWZD = queryJdbcTemplate.queryForList(sqlWWZD, list.get(1));

            String sqlBTL= " SELECT\n" +
                    "btl.ID, -- {\"title\":\"唯一标识\",\"field\":\"ID\",\"type\":\"string\",\" \n" +
                    "btl.LSM_KEY, -- {\"title\":\"jiraLSM关键字\",\"width\":150}\n" +
                    "btl.LSM_KEY_P, -- {\"title\":\"jira项目关键字\",\"width\":150}\n" +
                    "btl.DD_TASK_LS_STATUS, -- {\"title\":\"状态\",\"width\":100}\n" +
                    "btl.PROJECT_MAN, -- {\"title\":\"项目经理\",\"width\":100}\n" +
                    "btl.TASK_LS_NO, -- {\"title\":\"任务单编号\",\"width\":220,\"exp\":[\"-A\"]}\n" +
                    "btl.TASK_LS_TYPE, -- {\"title\":\"任务类型\",\"width\":150}\n" +
                    "btl.TASK_LS_TYPE_LB, -- {\"title\":\"任务类别\",\"width\":150,\"hidden\":true}\n" +
                    "btl.TASK_LIB_TYPE, -- {\"title\":\"文库类型\",\"width\":150}\n" +
                    "btl.PROJECT_MANAGER, -- {\"title\":\"客户服务经理\",\"width\":150}\n" +
                    "btl.CONTRACT_CKDATE, -- {\"title\":\"合同开始日期\",\"width\":150,\"type\":\"date\"}\n" +
                    "btl.PROJECT_SUBNO, -- {\"title\":\"项目期号\",\"width\":200,\"exp\":[\"-A\"]}\n" +
                    "btl.PROJECT_NO, -- {\"title\":\"项目编号\",\"width\":200,\"hidden\":true,\"exp\":[\"-A\"]}\n" +
                    "btl.PROJECT_NAME, -- {\"title\":\"项目名称\",\"width\":350,\"exp\":[\"-A\"]}\n" +
                    "btl.WW_NO, -- {\"title\":\"委外编号\",\"width\":150,\"exp\":[\"A\"]}\n" +
                    "btl.CONTRACT_NO, -- {\"title\":\"合同编号\",\"width\":200,\"hidden\":true}\n" +
                    "btl.PRODUCT_TYPE, -- {\"title\":\"产品类型\",\"width\":250}\n" +
                    "btl.TASK_LS_CDATE, -- {\"title\":\"运营审核通过日期\",\"width\":150,\"type\":\"date\"}\n" +
                    "btl.DD_LS_AUDIT_TIME, -- {\"title\":\"调度审核任务单日期\",\"width\":150,\"type\":\"date\",\"field\":\"DD_LS_AUDIT_TIME\"}\n" +
                    "btl.TASK_LS_LDATE, -- {\"title\":\"建库标准结单日期\",\"width\":150,\"type\":\"date\"}\n" +
                    "btl.TASK_LS_DELIVERDATE, -- {\"title\":\"实验标准交付日期\",\"width\":150,\"type\":\"date\"}\n" +
                    "btl.TASK_TEST_DELIVERDATE, -- {\"title\":\"实验计划交付日期\",\"width\":150,\"type\":\"date\"}\n" +
                    "btl.NEEDING_ATTENTION, -- {\"title\":\"建库注意事项\",\"width\":150}\n" +
                    "btl.TASK_LS_SAMPLESUM, -- {\"title\":\"启动样本个数\",\"width\":150}\n" +
                    "btl.THE_DATA_SUM, -- {\"title\":\"启动样品总数据量\",\"width\":150}\n" +
                    "btl.TASK_LS_BACK, -- {\"title\":\"退回原因\",\"width\":150}\n" +
                    "btl.SYS_MAN_L, -- {\"title\":\"最近操作人\",\"width\":150}\n" +
                    "btl.WW_SUPPLIER, -- {\"title\":\"委外供应商\",\"width\":150,\"exp\":[\"A\"]}\n" +
                    "btl.WW_PRODUCT_TYPE, -- {\"title\":\"委外产品类型\",\"width\":150,\"exp\":[\"A\"]}\n" +
                    "btl.W_CODE, -- {\"title\":\"委外供应商编码\",\"width\":150}\n" +
                    "btl.WW_DELIVERY_DATE, -- {\"title\":\"委外交付日期\",\"width\":150,\"type\":\"date\",\"exp\":[\"A\"]}\n" +
                    "btl.WW_FILE, -- {\"title\":\"委外附件报告\",\"width\":150}\n" +
                    "bpi.CONTRACT_NAME,\n" +
                    "bpi.CONTRACT_TYPE,\n" +
                    "btl.SYS_INSERTTIME_L -- {\"title\":\"最近操作时间\",\"width\":150,\"type\":\"date\"}\n" +
                    "FROM BIO_TASK_LIB btl,\n" +
                    "(select PROJECT_NO, max(CONTRACT_NAME) as CONTRACT_NAME,max(CONTRACT_TYPE) as CONTRACT_TYPE   ,CONTRACT_NO from BIO_PROJECT_INFO GROUP BY  PROJECT_NO, CONTRACT_NO ) bpi \n" +
                    "WHERE btl.ID  =? \n" +
                    "and btl.CONTRACT_NO=bpi.CONTRACT_NO (+) \n" +
                    "and btl.PROJECT_NO=bpi.PROJECT_NO (+) ";
            List<Map<String, Object>> listBTL = queryJdbcTemplate.queryForList(sqlBTL, list.get(0));

            //获取当前的时间
            Calendar calendar = Calendar.getInstance();
            Date time = calendar.getTime();
           int year= calendar.get(Calendar.YEAR)%100;
            //年
            String yearstr =  String.format("%02d",year);
            // 月份
            int month = calendar.get(Calendar.MONTH) + 1;
            // 将月份转换为两位的字符串格式
            String monthstr = String.format("%02d", month);

            // 日
            int day = calendar.get(Calendar.DATE) ;
            // 将日转换为两位的字符串格式
            String daystr = String.format("%02d", day);
            String  sopCode = yearstr+monthstr+daystr;

            String maxCodeSql = "SELECT\n" +
                    "max(BILL_ID) as BILL_ID -- {\"title\":\"单据ID\",\"width\":150}\n" +
                    "FROM BIO_WW_PURCHASE_RECEIPT bwpr\n" +
                    "where  bwpr.BILL_ID LIKE  ('%'||?||'%')\n " ;

            List<Map<String, Object>> listMaxCode = queryJdbcTemplate.queryForList(maxCodeSql, sopCode);

            if(listMaxCode.get(0).get("BILL_ID")!=null){
                String maxCode = (String) listMaxCode.get(0).get("BILL_ID");
                Integer sc =  SysBasic.toTranIntegerByObject(maxCode.substring(6))+1;
                String scstr=String.format("%03d", sc);
                sopCode=sopCode+scstr;
            }else {
                sopCode=sopCode+"001";
            }


            Map<String, Object> wwZDMap = new HashMap<>();
            wwZDMap.put("WW_TASK_NO", listBTL.get(0).get("TASK_LS_NO"));
            wwZDMap.put("EX_DH_NO", listBTL.get(0).get("TASK_LS_NO"));
            wwZDMap.put("EXE_TQQC_ID", listBTL.get(0).get("ID"));
            wwZDMap.put("PROJECT_SUBNO", listBTL.get(0).get("PROJECT_SUBNO"));
            wwZDMap.put("W_NAME", listBTL.get(0).get("WW_SUPPLIER"));
            wwZDMap.put("WW_PRODUCT_TYPE", listBTL.get(0).get("PRODUCT_TYPE"));
            wwZDMap.put("WW_SUPJS_NO", listBTL.get(0).get("WW_NO"));
            wwZDMap.put("WAREHOUSING_DATE", time);
            wwZDMap.put("GENERATE_DATE", time);
            wwZDMap.put("IMPUTATIONDATA", time);
            wwZDMap.put("RECEIVEFLAG", "1");
            wwZDMap.put("VOUCHTYPE", "01");
            wwZDMap.put("BUSINESSTYPE", "普通采购");
            wwZDMap.put("PURCHASETYPECODE", "01");
            wwZDMap.put("SOURCE", "库存");
            wwZDMap.put("WAREHOUSECODE", "10");
            wwZDMap.put("RECEIVECODE", "112");
            wwZDMap.put("MEMORY", "1");
            wwZDMap.put("MAKER", username);
            wwZDMap.put("PUSH_STATE", "草稿");
            wwZDMap.put("VENDORCODE", listBTL.get(0).get("W_CODE"));
            wwZDMap.put("TAXRATE", "13");
            wwZDMap.put("EXCHNAME", "人民币");
            wwZDMap.put("EXCHRATE", "1");
            wwZDMap.put("DEPARTMENTCODE", list.get(11));
            wwZDMap.put("TAXRATE", list.get(14));
            wwZDMap.put("WW_TASK_NO",listBTL.get(0).get("TASK_LS_NO"));
            wwZDMap.put("EX_DH_NO",listBTL.get(0).get("TASK_LS_NO"));
         //   wwZDMap.put("DEFINE33", listBTL.get(0).get("PROJECT_SUBNO"));


            Map<String, Object> wwMxMap = new HashMap<>();


            wwMxMap.put("DEFINE28", listBTL.get(0).get("PROJECT_NO"));
            wwMxMap.put("CBDEFINE3", listBTL.get(0).get("WW_NO"));
            wwMxMap.put("DEFINE33", listBTL.get(0).get("PROJECT_SUBNO"));
            wwMxMap.put("WW_SUPPLIER", listBTL.get(0).get("WW_SUPPLIER"));
            wwMxMap.put("CBDEFINE2", listBTL.get(0).get("WW_PRODUCT_TYPE"));
            wwMxMap.put("WW_DELIVERY_DATE", listBTL.get(0).get("WW_DELIVERY_DATE"));
            wwMxMap.put("ITEMCODE", listBTL.get(0).get("CONTRACT_NO"));
            wwMxMap.put("ITEMNAME", listBTL.get(0).get("CONTRACT_NO"));
           // wwMxMap.put("WWID", listBTL.get(0).get("ID"));
            wwMxMap.put("DEFINE32", "测序委外");
            wwMxMap.put("DEFINE23", listBTL.get(0).get("CONTRACT_TYPE"));
            wwMxMap.put("QUANTITY", list.get(9));
            wwMxMap.put("IORISUM", list.get(10));
            wwMxMap.put("DEPARTMENTCODE", list.get(11));
            wwMxMap.put("MEHOD_CBJRLAT", list.get(12));
            wwMxMap.put("CBDEFINE1","正常");
            wwMxMap.put("GENERATE_DATE",time);
            wwMxMap.put("CBDEFINE4", list.get(13));
            wwMxMap.put("TAXRATE", list.get(14));
            wwMxMap.put("MEMORY", list.get(15));
            double m1 = SysBasic.toTranDoubleByObject(list.get(10)) ;
            double m2 = SysBasic.toTranDoubleByObject(list.get(14))  ;
            double aa = bmkPCSLV.AccurateCalculation(m2, (double) 100,'/',6);
            double m3= bmkPCSLV.AccurateCalculation(aa, (double) 1,'+',6);
            double m4 =  bmkPCSLV.AccurateCalculation(m1,m3,'/',6) ;
            wwMxMap.put("IORIMONEY", m4);
            wwMxMap.put("ITEMCLASSCODE", "00");
            wwMxMap.put("ITEMCLASSNAME", "T3项目管理");
            wwMxMap.put("INVENTORYCODE", "900000");
            wwMxMap.put("INVNAME", "委托加工");
            wwMxMap.put("CMASSUNITNAME", "管数");
            double z1 = m4;
            double z2 = m2 ;
            double z4 = SysBasic.toTranDoubleByObject(list.get(9));
            double z3 =  bmkPCSLV.AccurateCalculation( bmkPCSLV.AccurateCalculation(z1,z2,'*',6), (double) 100,'/',6) ;
            double price = bmkPCSLV.AccurateCalculation(z3,z4,'/',6);
            wwMxMap.put("IORITAXPRICE", z3);
            wwMxMap.put("COST", z1);
            wwMxMap.put("PRICE", price);

            if(listWWZD.size()!=0){ //判断主单
                wwZDMap.put("ID",  listWWZD.get(0).get("ID"));
                 SysBasic.updateDataByTableMap(updateJdbcTemplate, "BIO_WW_PURCHASE_RECEIPT", wwZDMap);
                if( listWWZD.get(0).get("MXID") ==null){//判断明细主单

                    String sqlallID = "select u8ID.nextval from dual";
                    Integer allID = queryJdbcTemplate.queryForObject(sqlallID, Integer.class);
                    allID = allID + 1000000000;
                    wwMxMap.put("AUTOID", allID);
                    wwMxMap.put("DEFINE31", allID);
                    wwMxMap.put("ID", SysBasic.getUUID());
                    wwMxMap.put("BILL_ID", listMaxCode.get(0).get("BILL_ID"));
                    SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_WW_PURCHASE_RECEIPT_MX", wwMxMap);
                }else {
                    wwMxMap.put("ID",  listWWZD.get(0).get("MXID"));
                     SysBasic.updateDataByTableMap(updateJdbcTemplate, "BIO_WW_PURCHASE_RECEIPT_MX", wwMxMap);
                }

            }else {//无主单
                wwZDMap.put("BILL_ID", sopCode);
                wwZDMap.put("BILL_CODE", sopCode);


                wwMxMap.put("BILL_ID", sopCode);
               // wwMxMap.put("BILL_CODE", sopCode);


                String sqlallID = "select u8ID.nextval from dual";
                Integer allID = queryJdbcTemplate.queryForObject(sqlallID, Integer.class);
                allID = allID + 1000000000;
                wwMxMap.put("AUTOID", allID);
                wwMxMap.put("DEFINE31", allID);


                wwZDMap.put("ID",  SysBasic.getUUID());
                 SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_WW_PURCHASE_RECEIPT", wwZDMap);

                wwMxMap.put("ID", SysBasic.getUUID());
                SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_WW_PURCHASE_RECEIPT_MX", wwMxMap);
            }



        }


        return new CurrResponseResolve(code).put(responseMessageParameter).put("msg", msg).getData();

    }

    @RequestMapping("/queryprojectyf")
    public JSONObject queryProjectYF(@RequestBody JSONObject data) throws Exception {
        String str = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
                "<ufinterface sender=\"020\" receiver=\"u8\"\n" +
                " \troottag=\"SQLEXE\" proc=\"query\"\n" +
                " \tcodeexchanged=\"n\" >\n" +
                "    <sql value=\"select citemcode,citemname from fitemss00 where ( bclose=0 or bclose=NULL ) and citemccode=2 and citemcode like 'YF%' \"/>\n" +
                "</ufinterface>";
        JSONObject date = new JSONObject();
        log.info(str);
        date.put("url", "http://bj01ufi04.bmk.local/U8EAI/import.asp");
        date.put("outContentType", "xml");
        date.put("outCharset", "utf-8");
        date.put("inCharset", "utf-8");
        date.put("bodyParams", str);
        JSONObject jsonObject = httpCommonController.apiCommonPost(date);
        log.info(String.valueOf(jsonObject));
        String apiData = (String) jsonObject.get("apiData");
        String[] s1 = apiData.split("</query>\t\t<query>");
        String[] strarray = apiData.split("</query>\t\t<query>");


        JSONObject json = new JSONObject();
        json.put("status", 1);
        json.put("code", 1);
        json.put("message", "成功");
        json.put("query", "1");
        json.put("size", 1000);
        json.put("start", 1);
        json.put("pages", 1);

        json.put("formKey", "");
        ArrayList<Object> cols = new ArrayList<>();
        GridSchemaModel model1 = new GridSchemaModel();
        model1.setTitle("研发项目名称");
        model1.setField("CITEMNAME");
        model1.setType("string");
        model1.setWidth(500);
        model1.setSort(400);
        model1.setHidden(false);
        model1.setMenu(true);
        cols.add(model1);
        GridSchemaModel model2 = new GridSchemaModel();
        model2.setTitle("研发项目编号");
        model2.setField("CITEMCODE");
        model2.setType("string");
        model2.setWidth(120);
        model2.setSort(400);
        model2.setHidden(false);
        model2.setMenu(true);
        cols.add(model2);
        GridSchemaModel model6 = new GridSchemaModel();
        model6.setTitle("唯一标识");
        model6.setField("ID");
        model6.setType("string");
        model6.setWidth(120);
        model6.setSort(1);
        model6.setHidden(true);
        model6.setMenu(false);
        model6.setExp(null);
        cols.add(model6);


        json.put("cols", cols);
        ArrayList<Object> rows = new ArrayList<>();
        for (int i = 0; i < strarray.length; i++) {
            Map<String, Object> bUMBMap = new LinkedCaseInsensitiveMap<>();
            String s = strarray[i];
            bUMBMap.put("ROWNO", i + 1);//行
            bUMBMap.put("ID", null);//行
            if (s.indexOf("<citemname>") == -1) {
                bUMBMap.put("CITEMNAME", null);//批次
            } else {
                bUMBMap.put("CITEMNAME", s.substring(s.indexOf("<citemname>") + 11, s.lastIndexOf("</citemname>")));//量
            }
            if (s.indexOf("<citemcode>") == -1) {
                bUMBMap.put("CITEMCODE", null);//批次
            } else {
                bUMBMap.put("CITEMCODE", s.substring(s.indexOf("<citemcode>") + 11, s.lastIndexOf("</citemcode>")));//存货
            }
            rows.add(bUMBMap);
        }
        json.put("total", strarray.length);
        json.put("end", strarray.length);
        json.put("curr", strarray.length);
        json.put("rows", rows);

        return json;
    }

    @RequestMapping("/queryic")
    public JSONObject queryMaterialBatch(@RequestBody JSONObject data) throws Exception {
        String cInvCode = SysBasic.toTranStringByObject(data.get("cInvCode")); //阶段


//        String sql = "DELETE FROM BIO_U8_MATERIAL_BATCH WHERE U8_CINVCODE = ?";
//        updateJdbcTemplate.update(sql, cInvCode);
        String str = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
                "<ufinterface sender=\"020\" receiver=\"u8\"\n" +
                " \troottag=\"SQLEXE\" proc=\"query\"\n" +
                " \tcodeexchanged=\"n\" >\n" +
                "    <sql value=\"select DISTINCT a.cBatch,a.iQuantity,a.dVDate ,rdrecords08.dvdate,a.cInvCode,a.cWhCode \n" +
                "\tfrom currentstock a left join rdrecords01 b on b.cBatch=a.cBatch and a.cInvCode =b.cInvCode  \n" +
                "\tleft join rdrecord01 c on a.cWhCode=c.cWhCode and b.id=c.ID \n" +
                "\tleft join rdrecords08 on rdrecords08.cBatch=a.cBatch and a.cInvCode =rdrecords08.cInvCode \n" +
                "\twhere a.cInvCode= '" + cInvCode + "' and a.cWhCode='06' and a.iQuantity>0 and a.cBatch != '' group by a.cBatch,a.iQuantity,a.dVDate ,rdrecords08.dvdate,a.cInvCode,a.cWhCode \"/>\n" +
                "</ufinterface>\n";

        JSONObject date = new JSONObject();
        log.info(str);
        date.put("url", "http://BJ01UFI04.bmk.local/U8EAI/import.asp");
        date.put("outContentType", "xml");
        date.put("outCharset", "utf-8");
        date.put("inCharset", "utf-8");
        date.put("bodyParams", str);
        JSONObject jsonObject = httpCommonController.apiCommonPost(date);
        log.info(String.valueOf(jsonObject));
        String apiData = (String) jsonObject.get("apiData");
        String[] s1 = apiData.split("</query>\t\t<query>");
        String[] strarray = apiData.split("</query>\t\t<query>");


        JSONObject json = new JSONObject();
        json.put("status", 1);
        json.put("code", 1);
        json.put("message", "成功");
        json.put("query", "1");
        json.put("size", 500);
        json.put("start", 1);
        json.put("pages", 1);
        json.put("formKey", "");
        ArrayList<Object> cols = new ArrayList<>();
        GridSchemaModel model1 = new GridSchemaModel();
        model1.setTitle("批次");
        model1.setField("CBATCH");
        model1.setType("string");
        model1.setWidth(120);
        model1.setSort(400);
        model1.setHidden(false);
        model1.setMenu(true);
        cols.add(model1);
        GridSchemaModel model2 = new GridSchemaModel();
        model2.setTitle("现存量");
        model2.setField("IQUANTITY");
        model2.setType("string");
        model2.setWidth(120);
        model2.setSort(400);
        model2.setHidden(false);
        model2.setMenu(true);
        cols.add(model2);
        GridSchemaModel model3 = new GridSchemaModel();
        model3.setTitle("有效期");
        model3.setField("DVDATE");
        model3.setType("date");
        model3.setWidth(120);
        model3.setSort(400);
        model3.setHidden(false);
        model3.setMenu(true);
        cols.add(model3);
        GridSchemaModel model7 = new GridSchemaModel();
        model7.setTitle("有效期1");
        model7.setField("DVDATE1");
        model7.setType("date");
        model7.setWidth(120);
        model7.setSort(400);
        model7.setHidden(false);
        model7.setMenu(true);
        cols.add(model7);
        GridSchemaModel model4 = new GridSchemaModel();
        model4.setTitle("存货编码");
        model4.setField("CINVCODE");
        model4.setType("string");
        model4.setWidth(120);
        model4.setSort(400);
        model4.setHidden(false);
        model4.setMenu(true);
        cols.add(model4);
        GridSchemaModel model5 = new GridSchemaModel();
        model5.setTitle("出库编码");
        model5.setField("CWHCODE");
        model5.setType("string");
        model5.setWidth(120);
        model5.setSort(400);
        model5.setHidden(false);
        model5.setMenu(true);
        cols.add(model5);
        GridSchemaModel model6 = new GridSchemaModel();
        model6.setTitle("唯一标识");
        model6.setField("ID");
        model6.setType("string");
        model6.setWidth(120);
        model6.setSort(1);
        model6.setHidden(true);
        model6.setMenu(false);
        model6.setExp(null);
        cols.add(model6);


        json.put("cols", cols);
        ArrayList<Object> rows = new ArrayList<>();
        for (int i = 0; i < strarray.length; i++) {
            Map<String, Object> bUMBMap = new LinkedCaseInsensitiveMap<>();
            String s = strarray[i];
            bUMBMap.put("ROWNO", i + 1);//行
            bUMBMap.put("ID", null);//行
            if (s.indexOf("<cBatch>") == -1) {
                bUMBMap.put("CBATCH", null);//批次
            } else {
                bUMBMap.put("CBATCH", s.substring(s.indexOf("<cBatch>") + 8, s.lastIndexOf("</cBatch>")));//批次
            }
            if (s.indexOf("<iQuantity>") == -1) {
                bUMBMap.put("IQUANTITY", null);//批次
            } else {
                bUMBMap.put("IQUANTITY", s.substring(s.indexOf("<iQuantity>") + 11, s.lastIndexOf("</iQuantity>")));//量
            }
            if (s.indexOf("<cInvCode>") == -1) {
                bUMBMap.put("CINVCODE", null);//批次
            } else {
                bUMBMap.put("CINVCODE", s.substring(s.indexOf("<cInvCode>") + 10, s.lastIndexOf("</cInvCode>")));//存货
            }

            if (s.indexOf("<dVDate>") == -1) {
                bUMBMap.put("DVDATE", null);//批次
            } else {
                bUMBMap.put("DVDATE", s.substring(s.indexOf("<dVDate>") + 8, s.lastIndexOf("</dVDate>")));//有效期
            }

            if (s.indexOf("<cWhCode>") == -1) {
                bUMBMap.put("CWHCODE", null);//批次
            } else {
                bUMBMap.put("CWHCODE", s.substring(s.indexOf("<cWhCode>") + 9, s.lastIndexOf("</cWhCode>")));//仓库
            }

            if (s.indexOf("<dvdate1>") == -1) {
                bUMBMap.put("DVDATE1", null);//有效期1
            } else {
                bUMBMap.put("DVDATE1", s.substring(s.indexOf("<dvdate1>") + 9, s.lastIndexOf("</dvdate1>")));//有效期1
                if (bUMBMap.get("DVDATE") == null) {
                    bUMBMap.put("DVDATE", s.substring(s.indexOf("<dvdate1>") + 9, s.lastIndexOf("</dvdate1>")));//有效期1
                }
            }
            rows.add(bUMBMap);
        }
        json.put("total", strarray.length);
        json.put("end", strarray.length);
        json.put("curr", strarray.length);
        json.put("rows", rows);

        return json;
    }

    /**
     * 获取本月第一天
     *
     * @return
     */
    public Date getmindate(String str, String str2) throws ParseException {


        String sqlSop = "SELECT sdd.NDAYPUSH \n" +
                "FROM SYS_DATA_DICT sdd\n" +
                "where sdd.CODE in ('U8接口调用模式')\n" +
                "and sdd.IS_VALID = ('Y') " +
                "and sdd.ITEM_TEXT = ?";


        int count = queryJdbcTemplate.queryForObject(sqlSop, Integer.class, str); //26

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf.parse(str2);
        String[] split = sdf.format(date).split("-");
        String s = split[2];
        int dom = Integer.parseInt(s);


        if (count > dom) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
            return calendar.getTime();

        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.add(Calendar.MONTH, 1);
            return calendar.getTime();
        }
    }

    /**
     * 获取本月最后一天
     *
     * @return
     */
    public Date getmaxdate(String str, String str2) throws ParseException {


        String sqlSop = "SELECT sdd.NDAYPUSH \n" +
                "FROM SYS_DATA_DICT sdd\n" +
                "where sdd.CODE in ('U8接口调用模式')\n" +
                "and sdd.IS_VALID = ('Y') " +
                "and sdd.ITEM_TEXT = ?";


        int count = queryJdbcTemplate.queryForObject(sqlSop, Integer.class, str); //26

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf.parse(str2);
        String[] split = sdf.format(date).split("-");
        String s = split[2];
        int dom = Integer.parseInt(s);

        if (count > dom) {
            Calendar calendar2 = Calendar.getInstance();
            calendar2.setTime(date);
            calendar2.set(Calendar.DAY_OF_MONTH, calendar2.getActualMaximum(Calendar.DAY_OF_MONTH));
            return calendar2.getTime();
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            return calendar.getTime();
        }


    }

    /**
     * N日后次月推送
     *
     * @return
     */
    public Date getmNdate(String str, String str2) throws ParseException {

        String sqlSop = "SELECT sdd.NDAYPUSH \n" +
                "FROM SYS_DATA_DICT sdd\n" +
                "where sdd.CODE in ('U8接口调用模式')\n" +
                "and sdd.IS_VALID = ('Y') " +
                "and sdd.ITEM_TEXT = ?";


        int count = queryJdbcTemplate.queryForObject(sqlSop, Integer.class, str); //26

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf.parse(str2);
        String[] split = sdf.format(date).split("-");
        String s = split[2];
        int dom = Integer.parseInt(s);

        if (count > dom) {
            return date;
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.add(Calendar.MONTH, 1);

            // 1号的日期
            return calendar.getTime();
        }

    }

    public String getSopCode(String str, String str2) throws ParseException {

        String sqlSop = "SELECT sdd.NDAYPUSH \n" +
                "FROM SYS_DATA_DICT sdd\n" +
                "where sdd.CODE in ('U8接口调用模式')\n" +
                "and sdd.IS_VALID = ('Y') " +
                "and sdd.ITEM_TEXT = ?";


        int count = queryJdbcTemplate.queryForObject(sqlSop, Integer.class, str); //26
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf.parse(str2);
        String[] split = sdf.format(date).split("-");
        String s = split[2];
        int dom = Integer.parseInt(s);
        if (count > dom) {
            return split[0].substring(2) + split[1] + split[2];
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            int month = calendar.get(Calendar.MONTH) + 1;
            //   return  String.format("%02d",month)+ "01";
            return String.valueOf(calendar.get(Calendar.YEAR)).substring(2) + String.format("%02d", month) + "01";
        }

    }


    @RequestMapping("/base")
    public JSONObject queryMaterialBatch111(@RequestBody JSONObject data) throws Exception {


        Object[] LANEIDS = null;
        if (data.get("LANEID") != null) {
            if (data.get("LANEID") instanceof ArrayList) {
                LANEIDS = SysBasic.toTranObjectsByList((ArrayList) data.get("LANEID"));
            }
        }

        JSONObject json = new JSONObject();
        json.put("status", 1);
        json.put("code", 1);
        json.put("message", "成功");
        json.put("query", "1");
        json.put("size", 500);
        json.put("start", 1);
        json.put("pages", 1);

        json.put("formKey", "");
        ArrayList<Object> cols = new ArrayList<>();


        GridSchemaModel model11 = new GridSchemaModel();
        model11.setTitle("LANE编号");
        model11.setField("LANE_NO");
        model11.setType("string");
        model11.setWidth(120);
        model11.setSort(400);
        model11.setHidden(false);
        model11.setMenu(true);
        cols.add(model11);

        GridSchemaModel model10 = new GridSchemaModel();
        model10.setTitle("碱基");
        model10.setField("BASE");
        model10.setType("string");
        model10.setWidth(120);
        model10.setSort(400);
        model10.setHidden(false);
        model10.setMenu(true);
        cols.add(model10);
        GridSchemaModel model1 = new GridSchemaModel();
        model1.setTitle("1");
        model1.setField("ONE");
        model1.setType("string");
        model1.setWidth(120);
        model1.setSort(400);
        model1.setHidden(false);
        model1.setMenu(true);
        cols.add(model1);
        GridSchemaModel model2 = new GridSchemaModel();
        model2.setTitle("2");
        model2.setField("TWO");
        model2.setType("string");
        model2.setWidth(120);
        model2.setSort(400);
        model2.setHidden(false);
        model2.setMenu(true);
        cols.add(model2);
        GridSchemaModel model3 = new GridSchemaModel();
        model3.setTitle("3");
        model3.setField("THREE");
        model3.setType("string");
        model3.setWidth(120);
        model3.setSort(400);
        model3.setHidden(false);
        model3.setMenu(true);
        cols.add(model3);
        GridSchemaModel model4 = new GridSchemaModel();
        model4.setTitle("4");
        model4.setField("FOUR");
        model4.setType("string");
        model4.setWidth(120);
        model4.setSort(400);
        model4.setHidden(false);
        model4.setMenu(true);
        cols.add(model4);
        GridSchemaModel model5 = new GridSchemaModel();
        model5.setTitle("5");
        model5.setField("FIVE");
        model5.setType("string");
        model5.setWidth(120);
        model5.setSort(400);
        model5.setHidden(false);
        model5.setMenu(true);
        cols.add(model5);
        GridSchemaModel model6 = new GridSchemaModel();
        model6.setTitle("6");
        model6.setField("SIX");
        model6.setType("string");
        model6.setWidth(120);
        model6.setSort(400);
        model6.setHidden(false);
        model6.setMenu(true);
        cols.add(model6);
        GridSchemaModel model7 = new GridSchemaModel();
        model7.setTitle("7");
        model7.setField("SEVEN");
        model7.setType("string");
        model7.setWidth(120);
        model7.setSort(400);
        model7.setHidden(false);
        model7.setMenu(true);
        cols.add(model7);
        GridSchemaModel model8 = new GridSchemaModel();
        model8.setTitle("8");
        model8.setField("EIGHT");
        model8.setType("string");
        model8.setWidth(120);
        model8.setSort(400);
        model8.setHidden(false);
        model8.setMenu(true);
        cols.add(model8);
        GridSchemaModel model9 = new GridSchemaModel();
        model9.setTitle("唯一标识");
        model9.setField("ID");
        model9.setType("string");
        model9.setWidth(120);
        model9.setSort(1);
        model9.setHidden(true);
        model9.setMenu(false);
        model9.setExp(null);
        cols.add(model9);


        json.put("cols", cols);
        ArrayList<Object> rows = new ArrayList<>();



        for (int k = 0; k < LANEIDS.length; k++) {
            String LANEID = (String) LANEIDS[k];


            String sqlEx = "SELECT\n" +
                    "k.ID, " +
                    "k.LANE_ID, -- {\"title\":\"laneId\",\"width\":150,\"hidden\":true}\n" +
                    "k.SEQ_ID, -- {\"title\":\"排单池ID\",\"width\":150,\"hidden\":true}\n" +
                    "k.LIBID, -- {\"title\":\"LIBID\",\"width\":150,\"hidden\":true,\"exp\":[\"#B\"]}\n" +
                    "k.LANE_NO, -- {\"title\":\"lane号\",\"width\":150,\"hidden\":true,\"exp\":[\"-A\"]}\n" +
                    "k.JK_TASKMX_MAN, -- {\"title\":\"建库实验员\",\"width\":150}\n" +
                    "k.JK_TASKMX_ENDATE, -- {\"title\":\"建库实际结束日期\",\"width\":150,\"type\":\"date\"}\n" +
                    "k.LIBRARY_TYPE, -- {\"title\":\"文库类型\",\"width\":250,\"exp\":[\"-A\"]}\n" +
                    "k.READSD, -- {\"title\":\"项目名称\",\"width\":250,\"hidden\":true}\n" +
                    "k.PROJECT_NO, -- {\"title\":\"项目编号\",\"width\":250,\"hidden\":true}\n" +
                    "k.PROJECT_SUBNO, -- {\"title\":\"项目期号\",\"width\":250}\n" +
                    "k.PROJECT_MANAGER, -- {\"title\":\"客户服务经理\",\"width\":250,\"hidden\":true}\n" +
                    "k.TASK_LS_REMARKS, -- {\"title\":\"备注\",\"width\":250,\"hidden\":true}\n" +
                    "k.PROJECT_MAN, -- {\"title\":\"项目经理\",\"width\":250,\"hidden\":true}\n" +
                    "k.TASK_LS_PLAT, -- {\"title\":\"测序平台\",\"width\":150}\n" +
                    "k.DJC_RESULT, -- {\"title\":\"检测结果\",\"width\":150 }\n" +
                    "k.TASK_LSMX_BIOSAM, -- {\"title\":\"生物学重复编号\",\"width\":150 }\n" +
                    "k.SPECS, -- {\"title\":\"物种\",\"width\":250,\"hidden\":true}\n" +
                    "k.SAMPLE_NAME, -- {\"title\":\"样品名称\",\"width\":250,\"hidden\":true}\n" +
                    "k.POOL_CODE, -- {\"title\":\"混库编号\",\"width\":180,\"exp\":[\"-A\"]}\n" +
                    "k.LIBRARY_CODE, -- {\"title\":\"文库编号\",\"width\":200,\"exp\":[\"-A\",\"-B\"]}\n" +
                    "k.DATA_SUM, -- {\"title\":\"合同数据量\",\"width\":150,\"exp\":[\"-A\"]}\n" +
                    "k.DATA_UNIT, -- {\"title\":\"合同数据量单位\",\"width\":150,\"exp\":[\"-A\"]}\n" +
                    "k.THE_DATA_SUM, -- {\"title\":\"本期数据量(M)\",\"width\":150,\"exp\":[\"*A\"]}\n" +
                    "k.LANE_MX_DATA_SUMTHE, -- {\"title\":\"本次安排数据量(M)\",\"width\":150,\"exp\":[\"*A\"]}\n" +
                    "k.LANE_MX_DATA_SUM, -- {\"title\":\"上机安排数据量1(M)\",\"width\":150,\"exp\":[\"*A\"]}\n" +
                    "k.TASK_MX_ID, -- {\"title\":\"TASK_MX_ID\",\"width\":250,\"hidden\":true}\n" +
                    "k.TASK_ID, -- {\"title\":\"TASK_ID\",\"width\":250,\"hidden\":true}\n" +
                    "k.TASK_LS_BIOTYPE, -- {\"title\":\"生物类型\",\"width\":150}\n" +
                    "k.SPECIES_CH_NAME, -- {\"title\":\"物种中文名\",\"width\":150}\n" +
                    "k.INDEX_NAME, -- {\"title\":\"index\",\"width\":250,\"exp\":[\"-A\",\"*B\"]}\n" +
                    "k.SEQ_I7_1, -- {\"title\":\"i7序列\",\"width\":250 }\n" +
                    "k.SEQ_I5_NAVA, -- {\"title\":\"i5序列\",\"width\":350 }\n" +
                    "k.TASK_LSSQ_QJLEN, -- {\"title\":\"酶切组合\",\"width\":350 }\n" +
                    "k.TASK_LSSQ_ENZEM, -- {\"title\":\"切胶范围(bp)\",\"width\":350 }\n" +
                    "k.ENZYME, -- {\"title\":\"内切酶\",\"width\":350 }\n" +
                    "k.REQUIRED_DEPTH, -- {\"title\":\"合同要求深度\",\"width\":350 }\n" +
                    "k.LIB_CW -- {\"title\":\"文库储位\",\"width\":150,\"exp\":[\"-A\"]}\n" +
                    "from (\n" +
                    "select\n" +
                    "p1.*,\n" +
                    "p2.SEQ_I7_1,\n" +
                    "p2.SEQ_I5_NAVA,\n" +
                    "p2.ID,\n" +
                    "p2.SEQ_ID,\n" +
                    "p2.SEQLIBID,\n" +
                    "p2.pool_code,\n" +
                    "p2.ISPOOL,\n" +
                    "P2.LANE_ID,\n" +
                    "P2.LANE_NO,\n" +
                    "p2.LIBRARY_CODE,\n" +
                    "p2.LIBID,\n" +
                    "p2.INDEX_NAME,\n" +
                    "p2.JK_TASKMX_QJAREA,\n" +
                    "p2.JK_TASKMX_MAN,\n" +
                    "p2.JK_TASKMX_ENDATE,\n" +
                    "p2.JK_TASKMX_RESULT,\n" +
                    "p2.LIBRARY_METHOD,\n" +
                    "p2.THE_DATA_SUM,\n" +
                    "p2.LANE_MX_DATA_SUM,\n" +
                    "p2.DJC_RESULT,\n" +
                    "p2.LANE_MX_DATA_UNIT,\n" +
                    "p2.LANE_MX_DATA_SUM2,\n" +
                    "p2.LANE_MX_DATA_SUMTHE,\n" +
                    "p2.LANE_MX_SAMPLE_RATIO,\n" +
                    "p2.LANE_MX_SAMPLE_VOL,\n" +
                    "blqi.LIB_CW\n" +
                    "from (\n" +
                    "SELECT\n" +
                    "btl.TASK_LS_PLAT,\n" +
                    "btl.TASK_LS_BIOTYPE,\n" +
                    "btlmx.SPECIES_CH_NAME,\n" +
                    "btlmx.LIBRARY_TYPE_EN AS LIBRARY_TYPE,\n" +
                    "btl.CUSTOMER_VIP,\n" +
                    "btl.PROJECT_NAME,\n" +
                    "btl.PROJECT_NO,\n" +
                    "btl.PROJECT_SUBNO,\n" +
                    "btl.SPECS,\n" +
                    "btlmx.SAMPLE_NAME,\n" +
                    "btlmx.DATA_SUM,\n" +
                    "btlmx.DATA_UNIT,\n" +
                    "btl.TASK_LS_SP_REQUEST,\n" +
                    "btl.TASK_LS_REMARKS,\n" +
                    "btlmx.ID AS TASK_MX_ID,\n" +
                    "btl.ID AS TASK_ID,\n" +
                    "btlmx.TASK_LSMX_BIOSAM,\n" +
                    "btlmx.THE_DATA_SUM AS READSD,\n" +
                    "bsp.TASK_LSSQ_ENZEM,\n" +
                    "btlmx.TASK_LSSQ_TAGCOUNT,\n" +
                    "btl.PROJECT_MANAGER,\n" +
                    "btl.PROJECT_MAN,\n" +
                    "btlmx.REQUIRED_DEPTH,\n" +
                    "bsp.TASK_LSSQ_QJLEN,\n" +
                    "btl.ENZYME,\n" +
                    "btlmx.TASK_LSSQ_CONTROLPER\n" +
                    "FROM BIO_TASK_LIB btl\n" +
                    "inner join BIO_TASK_LIBMX btlmx on btl.ID=btlmx.TASK_LS_ID\n" +
                    "left join BIO_SLAF_PLAN bsp on bsp.TASK_LS_ID = btl.ID\n" +
                    "union all\n" +
                    "SELECT\n" +
                    "btt.TASK_PLAT AS TASK_LS_PLAT,\n" +
                    "bttm.SPECIES as TASK_LS_BIOTYPE,\n" +
                    "bttm.SPECIES_CH_NAME,\n" +
                    "bttm.LIBRARY_TYPE_EN AS LIBRARY_TYPE,\n" +
                    "btt.CUSTOMER_VIP,\n" +
                    "btt.PROJECT_NAME,\n" +
                    "btt.PROJECT_NO,\n" +
                    "btt.PROJECT_SUBNO,\n" +
                    "btt.SPECS,\n" +
                    "bttm.SAMPLE_NAME,\n" +
                    "bttm.DATA_SUM,\n" +
                    "bttm.DATA_UNIT,\n" +
                    "btt.TASK_LS_SP_REQUEST,\n" +
                    "btt.TASK_REMARKS AS TASK_LS_REMARKS,\n" +
                    "bttm.ID AS TASK_MX_ID,\n" +
                    "btt.ID AS TASK_ID,\n" +
                    "bttm.TASK_LSMX_BIOSAM,\n" +
                    "bttm.THE_DATA_SUM AS READSD,\n" +
                    "'' AS TASK_LSSQ_ENZEM,\n" +
                    "'' AS TASK_LSSQ_TAGCOUNT,\n" +
                    "btt.PROJECT_MANAGER,\n" +
                    "'' AS PROJECT_MAN,\n" +
                    "'' AS REQUIRED_DEPTH,\n" +
                    "'' AS TASK_LSSQ_QJLEN,\n" +
                    "btt.ENZYME,\n" +
                    "'' AS TASK_LSSQ_CONTROLPER\n" +
                    "FROM BIO_TQ_TASK btt,BIO_TQ_TASK_MX bttm where btt.ID=bttm.TASK_ID\n" +
                    "union all\n" +
                    "SELECT\n" +
                    "brtp1.TASK_LS_PLAT,\n" +
                    "brtp1.TASK_LS_BIOTYPE,\n" +
                    "brtp1.SPECIES_CH_NAME,\n" +
                    "brtp1.LIBRARY_TYPE,\n" +
                    "brtp1.CUSTOMER_VIP,\n" +
                    "brtp1.PROJECT_NAME,\n" +
                    "brtp1.PROJECT_NO,\n" +
                    "brtp1.PROJECT_SUBNO,\n" +
                    "brtp1.SPECIES,\n" +
                    "brtp1.SAMPLE_NAME,\n" +
                    "brtp1.DATA_SUM,\n" +
                    "brtp1.DATA_UNIT,\n" +
                    "brtp1.TASK_LS_SP_REQUEST,\n" +
                    "brtp1.SEQ_REMARK AS TASK_LS_REMARKS,\n" +
                    "brtp1.ID AS TASK_MX_ID,\n" +
                    "brtp1.ID AS TASK_ID,\n" +
                    "brtp1.TASK_LSMX_BIOSAM,\n" +
                    "brtp1.THE_DATA_SUM AS READSD,\n" +
                    "'' AS TASK_LSSQ_ENZEM,\n" +
                    "'' AS TASK_LSSQ_TAGCOUNT,\n" +
                    "'' AS PROJECT_MANAGER,\n" +
                    "'' AS PROJECT_MAN,\n" +
                    "'' AS REQUIRED_DEPTH,\n" +
                    "'' AS TASK_LSSQ_QJLEN,\n" +
                    "'' AS ENZYME,\n" +
                    "'' AS TASK_LSSQ_CONTROLPER\n" +
                    "FROM BIO_RD_TASK_POOL brtp1\n" +
                    ") p1 inner join (\n" +
                    "SELECT\n" +
                    "bdrq.DJC_RESULT,\n" +
                    "lib.SEQ_I7_1,\n" +
                    "lib.SEQ_I5_NAVA,\n" +
                    "blm.ID,\n" +
                    "blm.SEQ_ID,\n" +
                    "blm.LIB_ID AS SEQLIBID,\n" +
                    "blm.ISPOOL,\n" +
                    "blm.TASKZJ_L2100_SIZE,\n" +
                    "blm.TASKZJ_QUBIT_ND,\n" +
                    "blm.LIB_QUBIT_LLND,\n" +
                    "bli.ID AS LANE_ID,\n" +
                    "bli.LANE_NO,\n" +
                    "blp.POOL_CODE,\n" +
                    "lib.LIBRARY_CODE,\n" +
                    "lib.TASK_LIB_MX_ID,\n" +
                    "lib.ID AS LIBID,\n" +
                    "lib.INDEX_NAME,\n" +
                    "lib.LIBRARY_TYPE,\n" +
                    "lib.JK_TASKMX_QJAREA,\n" +
                    "lib.JK_TASKMX_MAN,\n" +
                    "lib.JK_TASKMX_ENDATE,\n" +
                    "lib.JK_TASKMX_RESULT,\n" +
                    "lib.LIBRARY_METHOD,\n" +
                    "blm.THE_DATA_SUM,\n" +
                    "blm.LANE_MX_DATA_SUM,\n" +
                    "blm.LANE_MX_DATA_UNIT,\n" +
                    "blm.LANE_MX_DATA_SUM2,\n" +
                    "blm.LANE_MX_DATA_SUMTHE,\n" +
                    "blm.LANE_MX_SAMPLE_RATIO,\n" +
                    "blm.LANE_MX_SAMPLE_VOL\n" +
                    "FROM BIO_LANE_MX blm inner join BIO_LIB_INFO lib ON (blm.LIB_ID=lib.ID OR blm.LIB_ID=lib.POOL_ID)\n" +
                    "inner join BIO_LANE_INFO bli ON blm.LANE_ID=bli.ID\n" +
                    "left join BIO_LIB_POOLING blp ON lib.POOL_ID=blp.ID\n" +
                    "left join BIO_DNA_RNA_QC bdrq ON lib.BIO_CODE=bdrq.SAMPLE_GENNO\n" +
                    "union all\n" +
                    "SELECT\n" +
                    "brtp.DJC_RESULT,\n" +
                    "brtp.SEQ_I7_1,\n" +
                    "brtp.SEQ_I5_NAVA,\n" +
                    "blm.ID,\n" +
                    "blm.SEQ_ID,\n" +
                    "blm.LIB_ID AS SEQLIBID,\n" +
                    "blm.ISPOOL,\n" +
                    "blm.TASKZJ_L2100_SIZE,\n" +
                    "blm.TASKZJ_QUBIT_ND,\n" +
                    "blm.LIB_QUBIT_LLND,\n" +
                    "bli.ID AS LANE_ID,\n" +
                    "bli.LANE_NO,\n" +
                    "''AS POOL_CODE,\n" +
                    "brtp.LIBRARY_CODE,\n" +
                    "brtp.ID AS TASK_LIB_MX_ID,\n" +
                    "brtp.ID AS LIBID,\n" +
                    "brtp.INDEX_NAME,\n" +
                    "brtp.LIBRARY_TYPE,\n" +
                    "'' AS JK_TASKMX_QJAREA,\n" +
                    "brtp.JK_TASKMX_MAN,\n" +
                    "brtp.JK_TASKMX_ENDATE,\n" +
                    "brtp.TASK_LS_SP_REQUEST AS JK_TASKMX_RESULT,\n" +
                    "brtp.LIBRARY_METHOD,\n" +
                    "blm.THE_DATA_SUM,\n" +
                    "blm.LANE_MX_DATA_SUM,\n" +
                    "blm.LANE_MX_DATA_UNIT,\n" +
                    "blm.LANE_MX_DATA_SUM2,\n" +
                    "blm.LANE_MX_DATA_SUMTHE,\n" +
                    "blm.LANE_MX_SAMPLE_RATIO,\n" +
                    "blm.LANE_MX_SAMPLE_VOL\n" +
                    "FROM BIO_LANE_MX blm inner join BIO_RD_TASK_POOL brtp ON blm.SEQ_ID=brtp.ID\n" +
                    "inner join BIO_LANE_INFO bli ON blm.LANE_ID=bli.ID\n" +
                    ") p2 ON p1.TASK_MX_ID=p2.TASK_LIB_MX_ID\n" +
                    "left join BIO_LIB_QC_INFO blqi ON blqi.TASK_JK_ID=p2.LIBID\n" +
                    "AND blqi.TASK_LS_MX_ID=p2.TASK_LIB_MX_ID\n" +
                    ")k\n" +
                    "WHERE 1=1\n" +
                    "AND k.LANE_ID  =  ? " +
                    "ORDER BY k.LANE_NO,k.POOL_CODE,k.LIBRARY_CODE  ";


            List<Map<String, Object>> listEx = queryJdbcTemplate.queryForList(sqlEx, LANEID);

            double[] I7A = {0, 0, 0, 0, 0, 0, 0, 0};
            double[] I7T = {0, 0, 0, 0, 0, 0, 0, 0};
            double[] I7C = {0, 0, 0, 0, 0, 0, 0, 0};
            double[] I7G = {0, 0, 0, 0, 0, 0, 0, 0};

            double[] I5A = {0, 0, 0, 0, 0, 0, 0, 0};
            double[] I5T = {0, 0, 0, 0, 0, 0, 0, 0};
            double[] I5C = {0, 0, 0, 0, 0, 0, 0, 0};
            double[] I5G = {0, 0, 0, 0, 0, 0, 0, 0};


            List<String> SEQ_I7_1list = new LinkedList<>();
            BigDecimal READSDS = new BigDecimal("0");
            boolean aa = false;
            for (int i = 0; i < listEx.size(); i++) {
                Map mapSOP = listEx.get(i);
                HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                if (!aa) {
                    aa = SEQ_I7_1list.contains(mapSOP.get("SEQ_I7_1"));
                }
                BigDecimal READSD = new BigDecimal(SysBasic.toTranDoubleByObject(mapSOP.get("READSD")));
                READSDS = READSD.add(READSDS);
                SEQ_I7_1list.add(SysBasic.toTranStringByObject(mapSOP.get("SEQ_I7_1")));
                char[] i7ch = SysBasic.toTranStringByObject(mapSOP.get("SEQ_I7_1")).toCharArray();
                for (int j = 0; j < i7ch.length; j++) {
                    if (i7ch[j] == 'A') {
                        BigDecimal I7READSD = new BigDecimal(I7A[j]);
                        I7READSD = READSD.add(I7READSD);
                        I7A[j] = I7READSD.doubleValue();
                    }
                    if (i7ch[j] == 'T') {
                        BigDecimal I7READSD = new BigDecimal(I7T[j]);
                        I7READSD = READSD.add(I7READSD);
                        I7T[j] = I7READSD.doubleValue();
                    }
                    if (i7ch[j] == 'C') {
                        BigDecimal I7READSD = new BigDecimal(I7C[j]);
                        I7READSD = READSD.add(I7READSD);
                        I7C[j] = I7READSD.doubleValue();
                    }
                    if (i7ch[j] == 'G') {
                        BigDecimal I7READSD = new BigDecimal(I7G[j]);
                        I7READSD = READSD.add(I7READSD);
                        I7G[j] = I7READSD.doubleValue();
                    }
                }
                char[] i5ch = SysBasic.toTranStringByObject(mapSOP.get("SEQ_I5_NAVA")).toCharArray();
                for (int j = 0; j < i5ch.length; j++) {
                    if (i5ch[j] == 'A') {
                        BigDecimal I5READSD = new BigDecimal(I5A[j]);
                        I5READSD = READSD.add(I5READSD);
                        I5A[j] = I5READSD.doubleValue();
                    }
                    if (i5ch[j] == 'T') {
                        BigDecimal I5READSD = new BigDecimal(I5T[j]);
                        I5READSD = READSD.add(I5READSD);
                        I5T[j] = I5READSD.doubleValue();
                    }
                    if (i5ch[j] == 'C') {
                        BigDecimal I5READSD = new BigDecimal(I5C[j]);
                        I5READSD = READSD.add(I5READSD);
                        I5C[j] = I5READSD.doubleValue();
                    }
                    if (i5ch[j] == 'G') {
                        BigDecimal I5READSD = new BigDecimal(I5G[j]);
                        I5READSD = READSD.add(I5READSD);
                        I5G[j] = I5READSD.doubleValue();
                    }
                }
            }
            BigDecimal READSD = new BigDecimal("0");
            BigDecimal hundred = new BigDecimal("100");
            for (int i = 0; i < 8; i++) {

                READSD = new BigDecimal(I7A[i]);
                READSD = READSD.divide(READSDS, 8, BigDecimal.ROUND_HALF_UP).multiply(hundred).setScale(2, BigDecimal.ROUND_HALF_UP);
                I7A[i] = READSD.doubleValue();

                READSD = new BigDecimal(I7C[i]);
                READSD = READSD.divide(READSDS, 8, BigDecimal.ROUND_HALF_UP).multiply(hundred).setScale(2, BigDecimal.ROUND_HALF_UP);
                I7C[i] = READSD.doubleValue();

                READSD = new BigDecimal(I7T[i]);
                READSD = READSD.divide(READSDS, 8, BigDecimal.ROUND_HALF_UP).multiply(hundred).setScale(2, BigDecimal.ROUND_HALF_UP);
                I7T[i] = READSD.doubleValue();

                READSD = new BigDecimal(I7G[i]);
                READSD = READSD.divide(READSDS, 8, BigDecimal.ROUND_HALF_UP).multiply(hundred).setScale(2, BigDecimal.ROUND_HALF_UP);
                I7G[i] = READSD.doubleValue();

                READSD = new BigDecimal(I5A[i]);
                READSD = READSD.divide(READSDS, 8, BigDecimal.ROUND_HALF_UP).multiply(hundred).setScale(2, BigDecimal.ROUND_HALF_UP);
                I5A[i] = READSD.doubleValue();

                READSD = new BigDecimal(I5C[i]);
                READSD = READSD.divide(READSDS, 8, BigDecimal.ROUND_HALF_UP).multiply(hundred).setScale(2, BigDecimal.ROUND_HALF_UP);
                I5C[i] = READSD.doubleValue();

                READSD = new BigDecimal(I5T[i]);
                READSD = READSD.divide(READSDS, 8, BigDecimal.ROUND_HALF_UP).multiply(hundred).setScale(2, BigDecimal.ROUND_HALF_UP);
                I5T[i] = READSD.doubleValue();

                READSD = new BigDecimal(I5G[i]);
                READSD = READSD.divide(READSDS, 8, BigDecimal.ROUND_HALF_UP).multiply(hundred).setScale(2, BigDecimal.ROUND_HALF_UP);
                I5G[i] = READSD.doubleValue();
            }
            List<Map<String, Object>> SampleList = new LinkedList<>();
            Map mapI7A = new HashMap();
            mapI7A.put("key", "I7A");
            mapI7A.put("value", I7A);
            SampleList.add(mapI7A);
            Map mapI7T = new HashMap();
            mapI7T.put("key", "I7T");
            mapI7T.put("value", I7T);
            SampleList.add(mapI7T);
            Map mapI7C = new HashMap();
            mapI7C.put("key", "I7C");
            mapI7C.put("value", I7C);
            SampleList.add(mapI7C);
            Map mapI7G = new HashMap();
            mapI7G.put("key", "I7G");
            mapI7G.put("value", I7G);
            SampleList.add(mapI7G);
            if (aa) {
                Map mapI5A = new HashMap();
                mapI5A.put("key", "I5A");
                mapI5A.put("value", I5A);
                SampleList.add(mapI5A);
                Map mapI5T = new HashMap();
                mapI5T.put("key", "I5T");
                mapI5T.put("value", I5T);
                SampleList.add(mapI5T);
                Map mapI5C = new HashMap();
                mapI5C.put("key", "I5C");
                mapI5C.put("value", I5C);
                SampleList.add(mapI5C);
                Map mapI5G = new HashMap();
                mapI5G.put("key", "I5G");
                mapI5G.put("value", I5G);
                SampleList.add(mapI5G);
            }

            for (int i = 0; i < SampleList.size(); i++) {
                Map<String, Object> bUMBMap = new LinkedCaseInsensitiveMap<>();
                Map Sample = SampleList.get(i);
                bUMBMap.put("ROWNO", i + 1);//行
                bUMBMap.put("ID", null);
                bUMBMap.put("BASE", Sample.get("key"));
                double[] value = (double[]) Sample.get("value");
                String s = SysBasic.toTranStringByObject(value[0]) + "%";
                bUMBMap.put("LANE_NO",listEx.get(0).get("LANE_NO"));
                bUMBMap.put("ONE", SysBasic.toTranStringByObject(value[0]) + "%");
                bUMBMap.put("TWO", SysBasic.toTranStringByObject(value[1]) + "%");
                bUMBMap.put("THREE", SysBasic.toTranStringByObject(value[2]) + "%");
                bUMBMap.put("FOUR", SysBasic.toTranStringByObject(value[3]) + "%");
                bUMBMap.put("FIVE", SysBasic.toTranStringByObject(value[4]) + "%");
                bUMBMap.put("SIX", SysBasic.toTranStringByObject(value[5]) + "%");
                bUMBMap.put("SEVEN", SysBasic.toTranStringByObject(value[6]) + "%");
                bUMBMap.put("EIGHT", SysBasic.toTranStringByObject(value[7]) + "%");
                rows.add(bUMBMap);
            }

        }
        json.put("total", rows.size());
        json.put("end", rows.size());
        json.put("curr", rows.size());
        json.put("rows", rows);
        return json;
    }

}
