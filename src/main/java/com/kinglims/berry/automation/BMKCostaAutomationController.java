package com.kinglims.berry.automation;


import com.alibaba.fastjson.JSONObject;
import com.kinglims.berry.rdtask.*;
import com.kinglims.framework.utils.system.SysBasic;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.*;


public class BMKCostaAutomationController extends QuartzJobBean {
    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;

    @Autowired
    private BrExperimentalProductionOrder brEPO;

    @Autowired
    private BjProductionOrderPush bjwwdd;

    @Autowired
    private QdProductionOrderPush qdwwdd;

    @Autowired
    private WwOrderPush wwddjk;

    @Autowired
    private OutSourcings scclck;

    @Autowired
    private RdRecord09 yfck;

    @Autowired
    private WwPurchaseReceipt wwcg;


    //成本自动化
    @SneakyThrows
    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) {

       // String urlhtml ="http://bj01ufi02.bmk.local";
        String urlhtml ="http://bj01ufi04.bmk.local";

        String sop = "SELECT *\n" +
                "FROM BIO_BZ_MATERIEL_SOP_JL bbmsj\n" +
                "where SOP_REVIEW_FLAG='已审核'\n" +
                "and COLLECTION_FLAG='待归集'";

        List<Map<String, Object>> listSOP = queryJdbcTemplate.queryForList(sop);
        if (listSOP.size() != 0) {
            for (int i = 0; i < listSOP.size(); i++) {
                HashMap<String, Map<String, Object>> sopMap = new LinkedHashMap<>();
                String step = "";
                Map<String, Object> map = listSOP.get(i);
                String source = (String) map.get("SOURCE");
                String str = (String) map.get("S_STEP");
                String tqjc = (String) map.get("SOP_TIQU_JC");
                if (str.equals("提取")) {
                    step = "提取结果审核";
                }
                if (str.equals("测序")) {
                    step = "常规建库审核";
                }
                if (str.equals("建库")) {
                    step = "常规建库审核";
                }
                if (source.equals("混样建库")) {
                    step = "混样建库审核";
                }
                if (source.equals("ONT上机信息")) {
                    step = "ONT上机信息";
                }
                if (source.equals("PB上机信息")) {
                    step = "混样建库审核";
                }
                if (source.equals("HIC建库")) {
                    step = "HIC建库审核";
                }
                if (source.equals("核酸质检")) {
                    step = "核酸质检审核";
                }
                if (source.equals("ATAC建库")) {
                    step = "HIC建库审核";
                }
                if (source.equals("DNA常规检测")) {
                    step = "检测结果审核";
                }
                if (source.equals("医学检测")) {
                    step = "检测结果审核";
                }
                if (source.equals("DNA检测-MCD检测")) {
                    step = "MCD检测结果审核";
                }
                if (source.equals("DNA提取") || source.equals("RNA提取") || source.equals("解离") || source.equals("空间")) {
                    step = "DNARNA提取";
                }
                if ( source.equals("空间")) {
                    step = "空间成本";
                }
                if (source.equals("回收")) {
                    step = "回收成本";
                }
                if (source.equals("单细胞建库")) {
                    step = "单细胞建库成本";
                }
                if (source.equals("切胶结果填写-ONT")) {
                    step = "ONT切胶";
                }
                if (source.equals("PB混样-微生物全长")) {
                    step = "PB混样微生物";
                    if (tqjc.equals("PCR")) {
                        step = "PB混样微生物PCR";
                    }
                }
                if (source.equals("PB混样-全长转录组")) {
                    step = "PB全长转录组";
                }
                if (source.equals("PB混样-基因组")) {
                    step = "PB混样基因组";
                }
                if (source.equals("切胶纯化结果-MCD")) {
                    step = "MCD切胶";
                }
                if (source.equals("DNA混样建库-SLAF建库")) {
                    step = "DNA混样SLAF建库";
                }
                if (source.equals("PB上机结合")) {
                    step = "PB上机结合";
                }
                if (source.equals("PB上机信息")) {
                    step = "PB上机信息";
                }
                if (source.equals("二代上机信息")) {
                    step = "二代上机信息";
                    if (tqjc.equals("QPCR")) {
                        step = "二代QPCR上机信息";
                    }
                }
                if (source.equals("文库浓度检测")) {
                    step = "文库浓度检测";
                }
                if (source.equals("文库片段检测")) {
                    step = "文库片段检测";
                }
                if (source.equals("文库QPCR检测")) {
                    step = "文库QPCR检测";
                }
                if (source.equals("蛋白前处理")) {
                    step = "蛋白前处理";
                }
                if (source.equals("代谢蛋白检测")) {
                    step = "代谢蛋白检测";
                }
                HashMap<String, Object> SampleMap = new LinkedHashMap<>();//各项目期号提取数
                SampleMap.put("EX_DH_NO", listSOP.get(i).get("EX_DH_NO"));
                SampleMap.put("EXE_TQQC_ID", listSOP.get(i).get("EXE_TQQC_ID"));
                SampleMap.put("DEFINE32", step);
                SampleMap.put("username", "自动");
                try {
                    brEPO.subcontractingOrder(new JSONObject(SampleMap));
                } catch (Exception e) {
                    System.out.println(e);
                }


            }
        }
        //北京
        String bjww = "SELECT " +
                " MOID " +
                "FROM BIO_BJ_PRODUCTION_ORDER bbpo  " +
                "where bbpo.PUSH_STATE in ('待推送','推送失败') ";
        //bjww+=" or RETURN_VALUE not like  '%单号重复%' ";           //u8优化要求返回【单号重复】才能确保推送成功
        bjww+=" group by MOID ";
        List<Map<String, Object>> listbjww = queryJdbcTemplate.queryForList(bjww);
        for (int i = 0; i < listbjww.size(); i++) {
            String MOID = (String) listbjww.get(i).get("MOID");
            List Ids = new ArrayList();
            Ids.add(listbjww.get(i).get("MOID"));
            HashMap<String, Object> SampleMap = new LinkedHashMap<>();//各项目期号提取数
            SampleMap.put("Ids", Ids);
            SampleMap.put("ts", "手动模式");
            SampleMap.put("urlhtml", urlhtml);
            try {
                JSONObject delivery = bjwwdd.DELIVERY(new JSONObject(SampleMap));

                Map map = delivery;
                List apiData = (List) map.get("apiData");
                JSONObject bject = (JSONObject) apiData.get(0);
                Map map1 = bject;
                String RETURN_VALUE = (String) map1.get("apiData");
                String s1 = (new Date().toString()) + " || " + RETURN_VALUE;
                if (RETURN_VALUE.indexOf("u8key") > -1) {
                    int a = RETURN_VALUE.indexOf("u8key=\"") + 7;
                    int b = RETURN_VALUE.indexOf("\"></item>");
                    String u8key = RETURN_VALUE.substring(a, b);
                    String sqop = "UPDATE  BIO_BJ_PRODUCTION_ORDER set PUSH_STATE ='已推送',LIMS_TO_U8ID = '" + u8key + "',RETURN_VALUE='" + s1 + "' where MOID=?";
                    updateJdbcTemplate.update(sqop, MOID);
                } else {
                    if (RETURN_VALUE.indexOf("单号重复") > -1) {
                        int a = RETURN_VALUE.indexOf("u8key=\"") + 7;
                        int b = RETURN_VALUE.indexOf("\"></item>");
                        String u8key = RETURN_VALUE.substring(a, b);
                        String sqop = "UPDATE  BIO_BJ_PRODUCTION_ORDER set PUSH_STATE ='已推送',LIMS_TO_U8ID = '" + u8key + "',RETURN_VALUE='" + s1 + "' where MOID=?";
                        updateJdbcTemplate.update(sqop, MOID);
                    } else {
                    String sqop = "UPDATE  BIO_BJ_PRODUCTION_ORDER set PUSH_STATE ='推送失败',RETURN_VALUE='" + s1 + "' where MOID=?";
                    updateJdbcTemplate.update(sqop, MOID);}
                }
                Map<String, String> logMap = new  HashMap<>();
                logMap.put("ID",SysBasic.getUUID());
                logMap.put("API_TYPE","北京U8生产订单");
                logMap.put("PUSH_DATE",SysBasic.getNowTime());
                logMap.put("MOID",MOID);
                logMap.put("API_RS",RETURN_VALUE);
                logMap.put("PUSH_MAN","sys");
                logMap.put("PUSH_MODE","自动推送");
                SysBasic.insertDataByTableMap(updateJdbcTemplate,"BIO_U8API_LOG",logMap);
            } catch (Exception e) {
                System.out.println(e);
            }
            Thread.sleep(5000);
        }
        //青岛
        String qdww = "SELECT " +
                "MOID  " +
                "FROM BIO_QD_PRODUCTION_ORDER bbpo " +
                "where bbpo.PUSH_STATE  in ('待推送','推送失败')";
        //qdww+=" or RETURN_VALUE not like  '%单号重复%' ";           //u8优化要求返回【单号重复】才能确保推送成功
        qdww+=" group by MOID ";
        List<Map<String, Object>> listqdww = queryJdbcTemplate.queryForList(qdww);
        for (int i = 0; i < listqdww.size(); i++) {
            String MOID = (String) listqdww.get(i).get("MOID");
            List Ids = new ArrayList();
            Ids.add(listqdww.get(i).get("MOID"));
            HashMap<String, Object> SampleMap = new LinkedHashMap<>();//各项目期号提取数
            SampleMap.put("Ids", Ids);
            SampleMap.put("ts", "手动模式");
            SampleMap.put("urlhtml", urlhtml);
            try {
                JSONObject delivery = qdwwdd.DELIVERY(new JSONObject(SampleMap));
                Map map = delivery;
                List apiData = (List) map.get("apiData");
                JSONObject bject = (JSONObject) apiData.get(0);
                Map map1 = bject;
                String RETURN_VALUE = (String) map1.get("apiData");
                String s1 = (new Date().toString()) + " || " + RETURN_VALUE;
                if (RETURN_VALUE.indexOf("u8key") > -1) {
                    int a = RETURN_VALUE.indexOf("u8key=\"") + 7;
                    int b = RETURN_VALUE.indexOf("\"></item>");
                    String u8key = RETURN_VALUE.substring(a, b);
                    String sqop = "UPDATE  BIO_QD_PRODUCTION_ORDER set PUSH_STATE ='已推送',LIMS_TO_U8ID = '" + u8key + "',RETURN_VALUE='" + s1 + "' where MOID=?";
                    updateJdbcTemplate.update(sqop, MOID);
                } else {
                    if (RETURN_VALUE.indexOf("单号重复") > -1) {
                        int a = RETURN_VALUE.indexOf("单号[") + 2;
                        int b = RETURN_VALUE.indexOf("]行号[");
                        String u8key = RETURN_VALUE.substring(a, b);
                        String sqop = "UPDATE  BIO_QD_PRODUCTION_ORDER set PUSH_STATE ='已推送',LIMS_TO_U8ID = '" + u8key + "',RETURN_VALUE='" + s1 + "' where MOID=?";
                        updateJdbcTemplate.update(sqop, MOID);
                    } else {
                        String sqop = "UPDATE  BIO_QD_PRODUCTION_ORDER set PUSH_STATE ='推送失败',RETURN_VALUE='" + s1 + "' where MOID=?";
                        updateJdbcTemplate.update(sqop, MOID);
                    }
                }
                Map<String, String> logMap = new  HashMap<>();
                logMap.put("ID",SysBasic.getUUID());
                logMap.put("API_TYPE","青岛U8生产订单");
                logMap.put("PUSH_DATE",SysBasic.getNowTime());
                logMap.put("MOID",MOID);
                logMap.put("API_RS",RETURN_VALUE);
                logMap.put("PUSH_MAN","sys");
                logMap.put("PUSH_MODE","自动推送");
                SysBasic.insertDataByTableMap(updateJdbcTemplate,"BIO_U8API_LOG",logMap);
            } catch (Exception e) {
                System.out.println(e);
            }
            Thread.sleep(5000);
        }
        //委外订单
        qdww = "SELECT " +
                "*  "+
                "FROM BIO_WW_ORDER bbpo " +
                "where bbpo.PUSH_STATE  in ('待推送','推送失败')";
        //qdww+=" or RETURN_VALUE not like  '%单号重复%' ";           //u8优化要求返回【单号重复】才能确保推送成功
        List<Map<String, Object>> listwwdd = queryJdbcTemplate.queryForList(qdww);
        for (int i = 0; i < listwwdd.size(); i++) {
            String id = (String) listwwdd.get(i).get("ID");
            String code = (String) listwwdd.get(i).get("CODE");
            List Ids = new ArrayList();
            Ids.add(id);
            HashMap<String, Object> SampleMap = new LinkedHashMap<>();//各项目期号提取数
            SampleMap.put("Ids", Ids);
            SampleMap.put("ts", "手动模式");
            SampleMap.put("urlhtml", urlhtml);
            try {
                JSONObject delivery = wwddjk.DELIVERY(new JSONObject(SampleMap));
                //String RETURN_VALUE = (String) ((Map) (((List) delivery.get("rows")).get(0))).get("apiData");
                Map map = delivery;
                List apiData = (List) map.get("apiData");
                JSONObject bject = (JSONObject) apiData.get(0);
                Map map1 = bject;
                String RETURN_VALUE = (String) map1.get("apiData");
                String s1 = (new Date().toString()) + " || " + RETURN_VALUE;
                if (RETURN_VALUE.indexOf("u8key") > -1 ) {
                    int a = RETURN_VALUE.indexOf("u8key=\"") + 7;
                    int b = RETURN_VALUE.indexOf("\"></item>");
                    String u8key = RETURN_VALUE.substring(a, b);
                    String sqop = "UPDATE  BIO_WW_ORDER set PUSH_STATE ='已推送',LIMS_TO_U8ID = '" + u8key + "',RETURN_VALUE='" + s1 + "' where ID=?";
                    updateJdbcTemplate.update(sqop, id);
                } else {
                    if (RETURN_VALUE.indexOf("单据号重复") > -1) {
                        int a = RETURN_VALUE.indexOf("<item key=\"") + 11;
                        int b = RETURN_VALUE.indexOf("\" succeed");
                        String u8key = RETURN_VALUE.substring(a, b);
                        String sqop = "UPDATE  BIO_WW_ORDER set PUSH_STATE ='已推送',LIMS_TO_U8ID = '" + u8key + "',RETURN_VALUE='" + s1 + "' where ID=?";
                        updateJdbcTemplate.update(sqop, id);
                    } else {
                        String sqop = "UPDATE  BIO_WW_ORDER set PUSH_STATE ='推送失败',RETURN_VALUE='" + s1 + "' where ID=?";
                        updateJdbcTemplate.update(sqop, id);
                    }
                }
                Map<String, String> logMap = new  HashMap<>();
                logMap.put("ID",SysBasic.getUUID());
                logMap.put("API_TYPE","委外订单");
                logMap.put("PUSH_DATE",SysBasic.getNowTime());
                logMap.put("MOID",code);
                logMap.put("API_RS",RETURN_VALUE);
                logMap.put("PUSH_MAN","sys");
                logMap.put("PUSH_MODE","自动推送");
                SysBasic.insertDataByTableMap(updateJdbcTemplate,"BIO_U8API_LOG",logMap);
            } catch (Exception e) {
                System.out.println(e);
            }
            Thread.sleep(5000);

        }
        //材料出库-生产
        qdww = "SELECT " +
                "* " +
                "FROM BIO_OUT_SOURCING bbpo " +
                "where bbpo.PUSH_STATE  in ('待推送','推送失败')";
        //qdww+=" or RETURN_VALUE not like  '%单号重复%' ";           //u8优化要求返回【单号重复】才能确保推送成功
        List<Map<String, Object>> listclck = queryJdbcTemplate.queryForList(qdww);
        for (int i = 0; i < listclck.size(); i++) {
            String id = (String) listclck.get(i).get("ID");
            String tradeNo = (String) listclck.get(i).get("TRADE_NO");
            List Ids = new ArrayList();
            Ids.add(listclck.get(i).get("ID"));
            HashMap<String, Object> SampleMap = new LinkedHashMap<>();//各项目期号提取数
            SampleMap.put("Ids", Ids);
            SampleMap.put("ts", "手动模式");
            SampleMap.put("urlhtml", urlhtml);
            try {
                JSONObject delivery = scclck.DELIVERY(new JSONObject(SampleMap));
                // String RETURN_VALUE = (String) ((Map) (((List) delivery.get("rows")).get(0))).get("apiData");
                Map map = delivery;
                List apiData = (List) map.get("apiData");
                String RETURN_VALUE = (String) apiData.get(0);
                String s1 = (new Date().toString()) + " || " + RETURN_VALUE;
                int c = RETURN_VALUE.indexOf("state\":\"");
                int d = RETURN_VALUE.indexOf("code\":\"");
                String success = RETURN_VALUE.substring(c + 8, c + 15);
                if (RETURN_VALUE.indexOf("生成材料出库单成功") > -1) {
                    int a = RETURN_VALUE.indexOf("材料出库单据号：") + 8;
                    String u8key = RETURN_VALUE.substring(a, a + 11);
                    String sqop = "UPDATE  BIO_OUT_SOURCING set PUSH_STATE ='已推送',LIMS_TO_U8ID = '" + u8key + "',RETURN_VALUE='" + s1 + "' where ID=?";
                    updateJdbcTemplate.update(sqop, id);
                } else {
                    if (RETURN_VALUE.indexOf("已生成材料出库单") > -1) {
                        int a = RETURN_VALUE.indexOf("委外华开单号重复：") + 9;
                        String u8key = RETURN_VALUE.substring(a, a + 11);
                        String sqop = "UPDATE  BIO_OUT_SOURCING set PUSH_STATE ='已推送',LIMS_TO_U8ID = '" + u8key + "',RETURN_VALUE='" + s1 + "' where ID=?";
                        updateJdbcTemplate.update(sqop, id);
                    } else {
                        String sqop = "UPDATE  BIO_OUT_SOURCING set PUSH_STATE ='推送失败',RETURN_VALUE='" + s1 + "' where ID=?";
                        updateJdbcTemplate.update(sqop, id);
                    }
                }
                Map<String, String> logMap = new  HashMap<>();
                logMap.put("ID",SysBasic.getUUID());
                logMap.put("API_TYPE","材料出库-生产");
                logMap.put("PUSH_DATE",SysBasic.getNowTime());
                logMap.put("MOID",tradeNo);
                logMap.put("API_RS",RETURN_VALUE);
                logMap.put("PUSH_MAN","sys");
                logMap.put("PUSH_MODE","自动推送");
                SysBasic.insertDataByTableMap(updateJdbcTemplate,"BIO_U8API_LOG",logMap);
            } catch (Exception e) {
                System.out.println(e);
            }
            Thread.sleep(5000);
        }
        //研发出库
        qdww = "SELECT " +
                "* " +
                "FROM BIO_GOO_RDRECORD09 bbpo " +
                "where bbpo.PUSH_STATE  in ('待推送','推送失败')";
        //qdww+=" or RETURN_VALUE not like  '%单号重复%' ";           //u8优化要求返回【单号重复】才能确保推送成功
        List<Map<String, Object>> listyfcl = queryJdbcTemplate.queryForList(qdww);
        for (int i = 0; i < listyfcl.size(); i++) {
            String id = (String) listyfcl.get(i).get("ID");
            String outboundCode = (String) listyfcl.get(i).get("OUTBOUND_CODE");
            List Ids = new ArrayList();
            Ids.add(listyfcl.get(i).get("ID"));
            HashMap<String, Object> SampleMap = new LinkedHashMap<>();//各项目期号提取数
            SampleMap.put("Ids", Ids);
            SampleMap.put("ts", "手动模式");
            SampleMap.put("urlhtml", urlhtml);
            try {
                JSONObject delivery = yfck.Rd(new JSONObject(SampleMap));
                //  String RETURN_VALUE = (String) ((Map) (((List) delivery.get("rows")).get(0))).get("apiData");
                Map map = delivery;
                List apiData = (List) map.get("apiData");
                String RETURN_VALUE = (String) apiData.get(0);
                int c = RETURN_VALUE.indexOf("state\":\"");
                int d = RETURN_VALUE.indexOf("code\":\"");
                String success = RETURN_VALUE.substring(c + 8, c + 15);
                if (success == "success" || success.equals("success")) {
                    //int a = RETURN_VALUE.indexOf("state\":\"");
                    //int b = RETURN_VALUE.indexOf("\",\"data");
                    String u8key = RETURN_VALUE.substring(d + 7, d + 18);
                    String sqop = "UPDATE  BIO_GOO_RDRECORD09 set PUSH_STATE ='已推送',LIMS_TO_U8 = '" + u8key + "',RETURN_VALUE='" + RETURN_VALUE + "' where ID=?";
                    updateJdbcTemplate.update(sqop, id);
                } else {
                    String sqop = "UPDATE  BIO_GOO_RDRECORD09 set PUSH_STATE ='推送失败',RETURN_VALUE='" + RETURN_VALUE + "' where ID=?";
                    updateJdbcTemplate.update(sqop, id);
                }
                Map<String, String> logMap = new  HashMap<>();
                logMap.put("ID",SysBasic.getUUID());
                logMap.put("API_TYPE","材料出库-研发");
                logMap.put("PUSH_DATE",SysBasic.getNowTime());
                logMap.put("MOID",outboundCode);
                logMap.put("API_RS",RETURN_VALUE);
                logMap.put("PUSH_MAN","sys");
                logMap.put("PUSH_MODE","自动推送");
                SysBasic.insertDataByTableMap(updateJdbcTemplate,"BIO_U8API_LOG",logMap);
            } catch (Exception e) {
                System.out.println(e);
            }
            Thread.sleep(5000);
        }
        //采购入库
        qdww = "SELECT " +
                "* " +
                "FROM BIO_WW_PURCHASE_RECEIPT bbpo " +
                "where bbpo.PUSH_STATE  in ('待推送','推送失败')";
        //qdww+=" or RETURN_VALUE not like  '%单号重复%' ";           //u8优化要求返回【单号重复】才能确保推送成功
        List<Map<String, Object>> listwwcg = queryJdbcTemplate.queryForList(qdww);
        for (int i = 0; i < listwwcg.size(); i++) {
            String id = (String) listwwcg.get(i).get("ID");
            String billId = (String) listwwcg.get(i).get("BILL_ID");
            List Ids = new ArrayList();
            Ids.add(listwwcg.get(i).get("ID"));
            HashMap<String, Object> SampleMap = new LinkedHashMap<>();//各项目期号提取数
            SampleMap.put("Ids", Ids);
            SampleMap.put("ts", "手动模式");
            SampleMap.put("urlhtml", urlhtml);
            try {
                JSONObject delivery = wwcg.DELIVERY(new JSONObject(SampleMap));
                //String RETURN_VALUE = (String) ((Map) (((List) delivery.get("rows")).get(0))).get("apiData");
                Map map = delivery;
                List apiData = (List) map.get("apiData");
                JSONObject bject = (JSONObject) apiData.get(0);
                Map map1 = bject;
                String RETURN_VALUE = (String) map1.get("apiData");
                if (RETURN_VALUE.indexOf("u8key") > -1) {
                    int a = RETURN_VALUE.indexOf("u8key=\"");
                    int b = RETURN_VALUE.indexOf("\"></item>");
                    String u8key = RETURN_VALUE.substring(a, b);
                    String sqop = "UPDATE  BIO_WW_PURCHASE_RECEIPT set PUSH_STATE ='已推送',LIMS_TO_U8ID = '" + u8key + "',RETURN_VALUE='" + RETURN_VALUE + "' where ID=?";
                    updateJdbcTemplate.update(sqop, id);
                } else {
                    String sqop = "UPDATE  BIO_WW_PURCHASE_RECEIPT set PUSH_STATE ='推送失败',RETURN_VALUE='" + RETURN_VALUE + "' where ID=?";
                    updateJdbcTemplate.update(sqop, id);
                }
                Map<String, String> logMap = new  HashMap<>();
                logMap.put("ID",SysBasic.getUUID());
                logMap.put("API_TYPE","材料出库-研发");
                logMap.put("PUSH_DATE",SysBasic.getNowTime());
                logMap.put("MOID",billId);
                logMap.put("API_RS",RETURN_VALUE);
                logMap.put("PUSH_MAN","sys");
                logMap.put("PUSH_MODE","自动推送");
                SysBasic.insertDataByTableMap(updateJdbcTemplate,"BIO_U8API_LOG",logMap);
            } catch (Exception e) {
                System.out.println(e);
            }
            Thread.sleep(5000);
        }

    }
}
