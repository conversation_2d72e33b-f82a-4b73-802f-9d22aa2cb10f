package com.kinglims.berry.automation;

import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.utils.system.SysBasic;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@Slf4j
public class BMKU8DataQueryController extends QuartzJobBean {
    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;

    @Autowired
    private HttpCommonController httpCommonController;

    @SneakyThrows
    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        String sqlbj = "select  MOCODE " +
                "from BIO_BJ_PRODUCTION_ORDER  " +
                "where PUSH_STATE='已推送' " +
                "and U8_DATA_CHECK='N'  " +
                "GROUP BY MOCODE";
        String bjwts="";
       String qdwts="";
        List<Map<String, Object>> listbj = queryJdbcTemplate.queryForList(sqlbj);   // 查询北京生产订单已推送且未校验单号
        if (listbj.size() > 0) {
            for (int i = 0; i < listbj.size(); i++) {
                String mocode = SysBasic.toTranStringByObject(listbj.get(i).get("MOCODE"));
                String u8sql = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><ufinterface sender=\"020\" receiver=\"u8\" roottag=\"SQLEXE\" proc=\"query\" codeexchanged=\"n\" ><sql value=\"select MoCode,CreateDate  from mom_ddh where MoCode ='" + mocode + "' \"/></ufinterface>";
                JSONObject date = new JSONObject();
                log.info(u8sql);
                date.put("url", "http://bj01ufi02.bmk.local/U8EAI/import.asp");
                date.put("outContentType", "xml");
                date.put("outCharset", "utf-8");
                date.put("inCharset", "utf-8");
                date.put("bodyParams", u8sql);
                JSONObject jsonObject = httpCommonController.apiCommonPost(date);
                String apiData = SysBasic.toTranStringByObject(jsonObject.get("apiData"));
                if(apiData.indexOf("MoCode")>-1){                                                            //判断u8返回信息
                    String sqlu8="UPDATE BIO_BJ_PRODUCTION_ORDER SET U8_DATA_CHECK='Y' WHERE MOCODE= ? ";
                    updateJdbcTemplate.update(sqlu8,mocode);
                }else {
                    String sqlu8="UPDATE BIO_BJ_PRODUCTION_ORDER SET PUSH_STATE='待推送' WHERE MOCODE= ? ";
                    updateJdbcTemplate.update(sqlu8,mocode);
                    bjwts=bjwts+"-"+mocode;
                }
            }
        }
        log.info("北京生产未推送订单号："+bjwts);
        String sqlqd = "select  MOCODE " +
                "from BIO_QD_PRODUCTION_ORDER  " +
                "where PUSH_STATE='已推送' " +
                "and U8_DATA_CHECK='N'  " +
                "GROUP BY MOCODE";
        List<Map<String, Object>> listqd = queryJdbcTemplate.queryForList(sqlqd);   // 查询青岛生产订单已推送且未校验单号
        if (listqd.size() > 0) {
            for (int i = 0; i < listqd.size(); i++) {
                String mocode = SysBasic.toTranStringByObject(listqd.get(i).get("MOCODE"));
                String u8sql = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><ufinterface sender=\"023\" receiver=\"u8\" roottag=\"SQLEXE\" proc=\"query\" codeexchanged=\"n\" ><sql value=\"select MoCode,CreateDate  from mom_ddh where MoCode ='" + mocode + "' \"/></ufinterface>";
                JSONObject date = new JSONObject();
                log.info(u8sql);
                date.put("url", "http://bj01ufi02.bmk.local/U8EAI/import.asp");
                date.put("outContentType", "xml");
                date.put("outCharset", "utf-8");
                date.put("inCharset", "utf-8");
                date.put("bodyParams", u8sql);
                JSONObject jsonObject = httpCommonController.apiCommonPost(date);
                String apiData = SysBasic.toTranStringByObject(jsonObject.get("apiData"));
                if(apiData.indexOf("MoCode")>-1){                                                            //判断u8返回信息
                    String sqlu8="UPDATE BIO_QD_PRODUCTION_ORDER SET U8_DATA_CHECK='Y' WHERE MOCODE= ? ";
                    updateJdbcTemplate.update(sqlu8,mocode);
                }else {
                    String sqlu8="UPDATE BIO_QD_PRODUCTION_ORDER SET PUSH_STATE='待推送' WHERE MOCODE= ? ";
                    updateJdbcTemplate.update(sqlu8,mocode);
                    qdwts=qdwts+"-"+mocode;
                }
            }
        }

        log.info("青岛生产未推送订单号："+qdwts);

    }
}
