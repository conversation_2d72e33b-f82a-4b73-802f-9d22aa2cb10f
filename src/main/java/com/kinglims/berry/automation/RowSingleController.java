package com.kinglims.berry.automation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kinglims.framework.common.http.HttpCommonController;
import com.kinglims.framework.utils.response.CurrResponseResolve;
import com.kinglims.framework.utils.response.ResponseMessageParameter;
import com.kinglims.framework.utils.system.SysBasic;
import jdk.nashorn.internal.parser.JSONParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.sl.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@RestController
@RequestMapping("/berry/automation/rowsingle")
@Slf4j
public class RowSingleController {
    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;

    @Autowired
    private ResponseMessageParameter responseMessageParameter;


    @RequestMapping("/rowsingle")
    public JSONObject rowSingle(@RequestBody JSONObject data) throws Exception {
        //请求连接
        String urlStr = SysBasic.toTranStringByObject(data.get("url"));
        String bodyParamsStr = null;

        if (data.get("PEVariable") instanceof Map) {//对象模式
            JSONObject objectJSON = data.getJSONObject("PEVariable");
            bodyParamsStr = objectJSON.toJSONString();
        } else if (data.get("PEVariable") instanceof List) {//列表模式
            JSONArray listJSON = data.getJSONArray("PEVariable");
            bodyParamsStr = listJSON.toJSONString();
        } else {//其他参数类型, 按字符串模式
            bodyParamsStr = SysBasic.toTranStringByObject(data.getString("PEVariable"));
        }

        URL url = null;
        HttpURLConnection httpConn = null;

        OutputStreamWriter out = null;
        InputStreamReader in = null;

        url = new URL(urlStr);
        httpConn = (HttpURLConnection) url.openConnection();
        httpConn.setDoOutput(true);
        httpConn.setDoInput(true);
        httpConn.setRequestMethod("POST");
        httpConn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        httpConn.setConnectTimeout(60 * 1000);//一分钟
        httpConn.setReadTimeout(60 * 1000);
        httpConn.connect();

        out = new OutputStreamWriter(httpConn.getOutputStream(), "UTF-8");
        out.write(bodyParamsStr);
        out.flush();

        //读取服务器端返回的内容
        if (httpConn.getResponseCode() != 200) {
            in = new InputStreamReader(httpConn.getErrorStream(), "UTF-8");
        } else {
            in = new InputStreamReader(httpConn.getInputStream(), "UTF-8");
        }
        BufferedReader br = new BufferedReader(in);
        StringBuffer buffer = new StringBuffer();
        String line = null;
        while ((line = br.readLine()) != null) {
            buffer.append(line);
        }

        System.out.println(buffer);
        Object apiData = null;
        //单JSON对象
        if (apiData == null) {
            try {
                JSONObject apiDataJsonObject = JSONObject.parseObject(buffer.toString());
                apiData = apiDataJsonObject;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //列表JSON对象
        if (apiData == null) {
            try {
                JSONArray apiDataJsonArray = JSONArray.parseArray(buffer.toString());
                apiData = apiDataJsonArray;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //普通文本
        if (apiData == null) {
            apiData = buffer.toString();
        }
        in.close();
        out.close();

        return new CurrResponseResolve(1).put("apiData", apiData).getData();

    }

    //微生物
    @RequestMapping("/peMCDCollection")
    public JSONObject peMCDCollection(@RequestBody JSONObject data) throws Exception {
        String exID = SysBasic.toTranStringByObject(data.get("EXE_TQQC_ID")); //ID
        List TaskNos = (List) data.get("TaskNos");
        String Way = SysBasic.toTranStringByObject(data.get("Way"));
        String region = SysBasic.toTranStringByObject(data.get("REGION")); //区分MCD2与MCD3
        Map<String, Object> peMap = SysBasic.toJsonObjectByListMap((Map<String, Object>) data.get("PSLMap"));

        int code = 0;
        String apiData = "采集失败";

        String sql = " select \n" +
                " * \n" +
                "from BIO_PE_RESULT_MX  bprm ,BIO_PE_RESULT bpr \n" +
                " where  bpr.id=bprm.BIO_PE_RESULT_ID  \n" +
                " and ( bprm.FILENAME LIKE ('%WellTable%') or  bprm.FILENAME LIKE ('%wellTable%') ) \n" +
                " and bpr.BARCODE=?" +
                "  ORDER BY bpr.TIMESTAMP desc  ";
        List<Map<String, Object>> list = queryJdbcTemplate.queryForList(sql, exID);
        for (int i = 0; i < 1; i++) {
            String fileContent = (String) list.get(i).get("FILECONTENT");
            if (fileContent != null) {
                String[] sample = fileContent.split("\\r\\n");
                String[] header = sample[0].split(",", -1);
                // Map<String, Object> peMap = PEShineLimsRna();
                Map<Integer, Object> limsMap = new LinkedHashMap<Integer, Object>();
                for (int z = 0; z < header.length; z++) {
                    limsMap.put(z, peMap.get(header[z]));
                }
                for (int j = 1; j < sample.length; j++) {
                    String[] attribute = sample[j].split("\\,", -1);
                    Map<String, Object> bmkMap = new LinkedHashMap<String, Object>();
                    for (int n = 0; n < attribute.length; n++) {
                        bmkMap.put("ID", SysBasic.getUUID());
                        bmkMap.put("NODE_TYPE", list.get(i).get("TYPE"));
                        bmkMap.put("FILE_TYPE", "WellTable");
                        if (attribute[n] != null && attribute[n].length() > 0) {
                            if ((String) limsMap.get(n) != null) {
                                bmkMap.put((String) limsMap.get(n), attribute[n]);
                            }
                        }
                    }
                    String pe_sample_name = (String) bmkMap.get("PE_USERCOMMENT");
                    String sql1 = "   select  bdrq.ID,bttm.AMPLIFY_REGIONAL,bdrq.PLATE_CODE,bdrq.TQ_SY_VOL,btt.TASK_TYPE, " +
                            " bdrq.PLATE_WELL, bttm.ID as BTTMID,  bdrq.NUMBER_TESTS  " +
                            "  from BIO_TQ_TASK_MX bttm,BIO_DNA_RNA_QC bdrq ,BIO_TQ_TASK btt , EXE_TQQC_SHEET ets " +
                            " WHERE   bttm.ID=bdrq.TASK_TQ_ID and btt.id = bttm.TASK_ID " +
                            "and  bdrq.EXE_TQQC2_ID = ets .id   and  bdrq.SAMPLE_GENNO =?  and ets.EX_DH_NO=? ";

                    try {
                        List<Map<String, Object>> list1 = queryJdbcTemplate.queryForList(sql1, pe_sample_name,exID);
                        String regional = (String) list1.get(0).get("AMPLIFY_REGIONAL");
                        Map<String, Object> bmkMap2 = new LinkedHashMap<String, Object>();
                        String s = "/data/app/biomarker/temp/" + (String) list1.get(0).get("PLATE_CODE") + "/";
                        String s1 = (String) bmkMap.get("PE_WELL_LABEL") + ".png";

                        bmkMap2.put("ID", list1.get(0).get("ID"));    //id
                        bmkMap2.put("SAMPLE_LABCHIP_CODE", pe_sample_name);       //核酸编号
                        bmkMap2.put("SAMPLE_LABCHIP_PATH", s);       //路径
                        bmkMap2.put("SAMPLE_LABCHIP_FILE", s1);       //文件名


                        String sql2 = "SELECT * from BIO_PE_LIMS_SCAR where LIMS_SCAR = ? ";

                        List<Map<String, Object>> list2 = queryJdbcTemplate.queryForList(sql2, regional);
                        String str_conc = (String) list2.get(0).get("PE_CONC");
                        String str_size = (String) list2.get(0).get("PE_SIZE");


                        double a_upper = SysBasic.toTranDoubleByObject(list2.get(0).get("A_UPPER"));
                        double a_lower = SysBasic.toTranDoubleByObject(list2.get(0).get("A_LOWER"));
                        double b_upper = SysBasic.toTranDoubleByObject(list2.get(0).get("B_UPPER"));
                        double b_lower = SysBasic.toTranDoubleByObject(list2.get(0).get("B_LOWER"));
                        double c_upper = SysBasic.toTranDoubleByObject(list2.get(0).get("C_UPPER"));
                        double c_lower = SysBasic.toTranDoubleByObject(list2.get(0).get("C_LOWER"));
                        double d_upper = SysBasic.toTranDoubleByObject(list2.get(0).get("D_UPPER"));
                        double d_lower = SysBasic.toTranDoubleByObject(list2.get(0).get("D_LOWER"));

                        double pe_conc = Double.valueOf((String) bmkMap.get(str_conc)).doubleValue();
                        String conc = String.format("%.2f", pe_conc);
                        double pe_size = Double.valueOf((String) bmkMap.get(str_size)).doubleValue();
                        String size = String.format("%.2f", pe_size);
                        bmkMap2.put("SAMPLE_LABCHIP_CONCENTRATION", conc);       //浓度
                        bmkMap2.put("SAMPLE_LABCHIP_FRAGMENT", size);          //片段

                        String DJC_NG_CAUSE = ""; //不合格原因

                        double b1 = Double.valueOf((String) bmkMap2.get("SAMPLE_LABCHIP_CONCENTRATION"));
                        String djc = "D";
                        if (b1 < a_upper && b1 >= a_lower) {
                            djc = "A";
                        }
                        ;
                        if (b1 < b_upper && b1 >= b_lower) {
                            djc = "B";
                        }
                        ;
                        if (b1 < c_upper && b1 >= c_lower) {
                            djc = "C";
                        }
                        ;
                        if (b1 < d_upper && b1 >= d_lower) {
                            djc = "D";
                        }
                        ;
                        if (djc == "D") {
                            DJC_NG_CAUSE = "扩增失败";
                        }

                        String DJC_JC_STATUS = "不通过";  //检测状态
                        String RL_STATUS = "已排";  //提取排单状态
                        String TASK_SM_STATUS = "检测中";  //样本状态
                        String RL_STATUS2 = "已排";  //检测排单状态
                        String TQ_SY_VOL = (String) list1.get(0).get("TQ_SY_VOL");  //组织剩余量
                        String TASK_TYPE = (String) list1.get(0).get("TASK_TYPE");  //任务单类型
                        int NUMBER_TESTS = SysBasic.toTranIntegerByObject(list1.get(0).get("NUMBER_TESTS")) + 1;  //检测次数
                        String[] strArray = pe_sample_name.split("-");
                        String aa = strArray[strArray.length - 1];
                        Integer bb = SysBasic.toTranIntegerByObject(aa);
                        String PE_RECHECK_STATUS = null;

                        //  if (TASK_TYPE.equals("检测") || djc.equals("A") || djc.equals("B") || pe_sample_name.endsWith("-02") || TQ_SY_VOL == "0") {
//                        if ( djc.equals("A") || djc.equals("B") || (  !TASK_TYPE.equals("检测")  && bb>=2 )|| ( !TASK_TYPE.equals("检测")  && TQ_SY_VOL == "0")) {
//                            DJC_JC_STATUS = "通过";
//
//                        }
                        if (djc.equals("A") || djc.equals("B")) {
                            DJC_JC_STATUS = "通过";
                            TASK_SM_STATUS = "已完成";
                        } else {
                            if (TASK_TYPE.equals("检测")) {
                                if (NUMBER_TESTS >= 2) {
                                    DJC_JC_STATUS = "通过";
                                    TASK_SM_STATUS = "已完成";
                                } else {
                                    RL_STATUS2 = "待排";
                                }
                            } else {
                                if (TQ_SY_VOL == "0") {
                                    DJC_JC_STATUS = "通过";
                                    TASK_SM_STATUS = "已完成";
                                } else {
                                    if (bb >= 2) {
                                        if (NUMBER_TESTS >= 2) {
                                            DJC_JC_STATUS = "通过";
                                            TASK_SM_STATUS = "已完成";
                                        } else {
                                            PE_RECHECK_STATUS= "待处理";
                                            RL_STATUS2 = "待排";
                                        }
                                    } else {
                                        if (NUMBER_TESTS >= 2) {
                                            TASK_SM_STATUS = "待提取";
                                            RL_STATUS = "待排";
                                            RL_STATUS2 = "待排";
                                        } else {
                                            PE_RECHECK_STATUS= "待处理";
                                            RL_STATUS2 = "待排";
                                        }
                                    }
                                }
                            }
                        }


                        Map<String, Object> BTTMXMAP = new LinkedHashMap<String, Object>();
                        BTTMXMAP.put("ID", list1.get(0).get("BTTMID"));
                        BTTMXMAP.put("RL_STATUS", RL_STATUS);
                        BTTMXMAP.put("TASK_SM_STATUS", TASK_SM_STATUS);
                        BTTMXMAP.put("RL_STATUS2", RL_STATUS2);


/*
                        if (regional.indexOf("真菌ITS1") != -1) {
                            double pe_conc = Double.valueOf((String) bmkMap.get("PE_REGION_360_560_CONC")).doubleValue();
                            String conc = String.format("%.2f", pe_conc);
                            double pe_size = Double.valueOf((String) bmkMap.get("PE_REGION_360_560_SIZE_BP")).doubleValue();
                            String size = String.format("%.2f", pe_size);
                            bmkMap2.put("SAMPLE_LABCHIP_CONCENTRATION", conc);       //浓度
                            bmkMap2.put("SAMPLE_LABCHIP_FRAGMENT", size);          //片段
                        }
                        if (regional.indexOf("V3+V4") != -1) {
                            double pe_conc = Double.valueOf((String) bmkMap.get("PE_EP650_CONC")).doubleValue();
                            String conc = String.format("%.2f", pe_conc);
                            double pe_size = Double.valueOf((String) bmkMap.get("PE_EP650_SIZE")).doubleValue();
                            String size = String.format("%.2f", pe_size);
                            bmkMap2.put("SAMPLE_LABCHIP_CONCENTRATION", conc);       //浓度
                            bmkMap2.put("SAMPLE_LABCHIP_FRAGMENT", size);          //片段
                        }
                        if (regional.indexOf("ITS全长") != -1) {
                            double pe_conc = Double.valueOf((String) bmkMap.get("PE_REGION_585_715_CONC")).doubleValue();
                            String conc = String.format("%.2f", pe_conc);
                            double pe_size = Double.valueOf((String) bmkMap.get("PE_REGION_585_715_SIZE_BP")).doubleValue();
                            String size = String.format("%.2f", pe_size);
                            bmkMap2.put("SAMPLE_LABCHIP_CONCENTRATION", conc);       //浓度
                            bmkMap2.put("SAMPLE_LABCHIP_FRAGMENT", size);          //片段
                        }
                        if (regional.indexOf("16S 全长") != -1) {
                            double pe_conc = Double.valueOf((String) bmkMap.get("PE_EP1550_CONC")).doubleValue();
                            String conc = String.format("%.2f", pe_conc);
                            double pe_size = Double.valueOf((String) bmkMap.get("PE_EP1550_SIZE")).doubleValue();
                            String size = String.format("%.2f", pe_size);
                            bmkMap2.put("SAMPLE_LABCHIP_CONCENTRATION", conc);       //浓度
                            bmkMap2.put("SAMPLE_LABCHIP_FRAGMENT", size);          //片段
                        }

                        double b1 = Double.valueOf((String) bmkMap2.get("SAMPLE_LABCHIP_CONCENTRATION"));
                        String djc = "A";
                        if (region.equals("MCD3")) {
                            if (b1 < 15) djc = "C";
                        }
                        if (b1 < 5) djc = "D";

 */

                        bmkMap2.put("PE_RECHECK_STATUS",PE_RECHECK_STATUS);
                        bmkMap2.put("NUMBER_TESTS",NUMBER_TESTS);
                        bmkMap2.put("PE_EP1550_SIZE", bmkMap.get("PE_EP1550_SIZE"));
                        bmkMap2.put("PE_EP1550_CONC", bmkMap.get("PE_EP1550_CONC"));
                        bmkMap2.put("PE_REGION_360_560_SIZE_BP", bmkMap.get("PE_REGION_360_560_SIZE_BP"));
                        bmkMap2.put("PE_REGION_360_560_CONC", bmkMap.get("PE_REGION_360_560_CONC"));
                        bmkMap2.put("PE_REGION_585_715_SIZE_BP", bmkMap.get("PE_REGION_585_715_SIZE_BP"));
                        bmkMap2.put("PE_REGION_585_715_CONC", bmkMap.get("PE_REGION_585_715_CONC"));
                        bmkMap2.put("PE_REGION_450_650_SIZE_BP", bmkMap.get("PE_REGION_450_650_SIZE_BP"));
                        bmkMap2.put("PE_REGION_450_650_CONC", bmkMap.get("PE_REGION_450_650_CONC"));
                        bmkMap2.put("PE_EP650_SIZE", bmkMap.get("PE_EP650_SIZE"));
                        bmkMap2.put("PE_EP650_CONC", bmkMap.get("PE_EP650_CONC"));
                        bmkMap2.put("DJC_RESULT", djc);           //检测结果
                        bmkMap2.put("DJC_JC_STATUS", DJC_JC_STATUS);           //检测状态
                        bmkMap2.put("DJC_NG_CAUSE", DJC_NG_CAUSE);           //不合格原因

                        bmkMap2.put("DJC_JT_DLMD", bmkMap2.get("SAMPLE_LABCHIP_CONCENTRATION"));     //胶图/Qubit定量浓度
                        //  bmkMap2.put("PCR_PLAT_NO", bmkMap.get("PE_USERCOMMENT"));     //扩增板号
                        bmkMap2.put("PCR_PLAT_NO", exID + "-P");     //扩增板号
                        bmkMap2.put("PCR_PLAT_CELL", bmkMap.get("PE_WELL_LABEL"));     //扩增孔位
                        if (list1.get(0).get("ID") == null) {
                            System.out.println("1");
                        }
                        if (list1.get(0).get("ID").equals("")) {
                            System.out.println("2");
                        }

                        // int a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_PE_DATA_ACQUISITION", bmkMap);
                        if (list1.size() > 0) {
                            SysBasic.updateDataByTableMap(updateJdbcTemplate, "BIO_DNA_RNA_QC", bmkMap2);
                            SysBasic.updateDataByTableMap(updateJdbcTemplate, "BIO_TQ_TASK_MX", BTTMXMAP);
                            code = 1;
                            apiData = "采集成功";
                        }
                    } catch (Exception e) {
                    }
                }
            }
        }
        if (code == 1) {
            String QCStr = "UPDATE  BIO_PE_RESULT set PE_GATHER_STATUS='已采集'  where BARCODE=?  ";
            updateJdbcTemplate.update(QCStr, exID);
        } else {
            String QCStr = "UPDATE  BIO_PE_RESULT set PE_GATHER_STATUS='采集失败'  where BARCODE=?  ";
            updateJdbcTemplate.update(QCStr, exID);
        }

        return new CurrResponseResolve(code).put("apiData", apiData).getData();
    }
/*
    //片段
    @RequestMapping("/peQCCollection")
    public JSONObject peQCCollection(@RequestBody JSONObject data) throws Exception {
        String exID = SysBasic.toTranStringByObject(data.get("EXE_TQQC_ID")); //ID
        List TaskNos = (List) data.get("TaskNos");
        String Way = SysBasic.toTranStringByObject(data.get("Way"));
        Map<String, Object> peMap = SysBasic.toJsonObjectByListMap((Map<String, Object>) data.get("PSLMap"));

        String sql = " select \n" +
                "*\n" +
                "from BIO_PE_RESULT_MX  bprm ,BIO_PE_RESULT bpr \n" +
                " where  bpr.id=bprm.BIO_PE_RESULT_ID  \n" +
                " and ( bprm.FILENAME LIKE ('%WellTable%') or  bprm.FILENAME LIKE ('%wellTable%') ) \n" +
                " and bpr.BARCODE=?" +
                "  ORDER BY bpr.TIMESTAMP desc  ";
        String sql2 = " select \n" +
                " *\n" +
                " from BIO_PE_RESULT_MX  bprm ,BIO_PE_RESULT bpr \n" +
                " where  bpr.id=bprm.BIO_PE_RESULT_ID  \n" +
                " and ( bprm.FILENAME LIKE ('%Nivo%') or  bprm.FILENAME LIKE ('%nivo%') ) \n" +
                " and bpr.BARCODE=? \n" +
                " ORDER BY bpr.TIMESTAMP desc  ";
        List<Map<String, Object>> list = queryJdbcTemplate.queryForList(sql, exID);
        List<Map<String, Object>> list2 = queryJdbcTemplate.queryForList(sql2, exID);
        for (int i = 0; i < 1; i++) {
            String fileContent = (String) list.get(i).get("FILECONTENT");
            String fileContent2 = (String) list2.get(i).get("FILECONTENT");
            JSONObject jsonObject = (JSONObject) JSON.parse(fileContent2);
            JSONArray rstData = (JSONArray) jsonObject.get("rstData");
            Map<String, Object> wcMap = new HashMap<String, Object>();
            for (int j = 0; j < rstData.size(); j++) {
                JSONObject jdata = (JSONObject) rstData.get(j);
                wcMap.put((String) jdata.get("Well"), jdata.get("Conc"));
            }
            JSONObject parse = (JSONObject) JSON.parse("");
            if (fileContent != null) {
                String[] sample = fileContent.split("\\r\\n");
                String[] header = sample[0].split(",", -1);
                // Map<String, Object> peMap = PEShineLimsRna();
                Map<Integer, Object> limsMap = new LinkedHashMap<Integer, Object>();
                for (int z = 0; z < header.length; z++) {
                    limsMap.put(z, peMap.get(header[z]));
                }
                for (int j = 1; j < sample.length; j++) {
                    String[] attribute = sample[j].split("\\,", -1);
                    Map<String, Object> bmkMap = new LinkedHashMap<String, Object>();
                    for (int n = 0; n < attribute.length; n++) {
                        bmkMap.put("ID", SysBasic.getUUID());
                        bmkMap.put("NODE_TYPE", list.get(i).get("TYPE"));
                        bmkMap.put("FILE_TYPE", "WellTable");
                        if (attribute[n] != null && attribute[n].length() > 0) {
                            if ((String) limsMap.get(n) != null) {
                                bmkMap.put((String) limsMap.get(n), attribute[n]);
                            }
                        }
                    }
                    Object pe_sample_name = bmkMap.get("PE_SAMPLE_NAME");
                    String sql1 = "SELECT\n" +
                            "T.ID, \n" +           //文库或
                            "T.LIBRARY_TYPE,\n" +
                            "T.YESORNO  \n" +
                            "FROM(\n" +
                            "SELECT\n" +
                            "blqi.ID,\n" +
                            "'否' AS YESORNO,\n" +
                            "bli.LIBRARY_TYPE, \n" +
                            "bli.LIBRARY_CODE \n" +
                            "FROM BIO_LIB_INFO bli,\n" +
                            "BIO_LIB_QC_INFO blqi\n" +
                            "WHERE bli.ID=blqi.TASK_JK_ID\n" +
                            "UNION ALL\n" +
                            "SELECT\n" +
                            "brtwlt.ID,\n" +
                            "'是' AS YESORNO,\n" +
                            "brtwlt.LIBRARY_TYPE_EN AS LIBRARY_TYPE,\n" +
                            "brtwlt.LIBRARY_CODE\n" +
                            "FROM BIO_RD_TASK_WL_LIBQC_TZ brtwlt\n" +
                            "WHERE 1=1\n" +
                            ")T\n" +
                            "where  T.LIBRARY_CODE = ? \n" +
                            "ORDER BY T.LIBRARY_CODE";
                    try {
                        List<Map<String, Object>> list1 = queryJdbcTemplate.queryForList(sql1, pe_sample_name);
                        String libraryType = (String) list1.get(0).get("LIBRARY_TYPE");  //文库类型
                        Map<String, Object> bmkMap2 = new LinkedHashMap<String, Object>();

                        bmkMap2.put("ID", list1.get(0).get("ID"));    //id

                        Object l2100Result = ""; //片段文库检测结论

                        if (libraryType.indexOf("Trans、Lnc-Str、Reseq-M、Hi-c、Denovo、Trans-Str、10x-snRNA-seq、10x-scRNA-seq、10x-siRNA-seq、10x-snRNA-seq") != -1) {
                            double pe_conc = Double.valueOf((String) bmkMap.get("PE_REGION_300_800_CONC")).doubleValue();
                            String conc = String.format("%.2f", pe_conc);
                            double pe_size = Double.valueOf((String) bmkMap.get("PE_REGION_300_800_SIZE_BP")).doubleValue();
                            String size = String.format("%.2f", pe_size);
                            bmkMap2.put("TASKZJ_L2100_MACNG", conc);       //仪器检测浓度ng/ul
                            bmkMap2.put("TASKZJ_L2100_SIZE", size);          //文库片段大小片段
                            if (pe_size >= 430 && pe_size <= 480) {
                                l2100Result = "A";
                            }
                            if (pe_size >= 380 && pe_size < 430) {
                                l2100Result = "B";
                            }
                            if (pe_size > 480 && pe_size <= 530) {
                                l2100Result = "C";
                            }
                            if ((pe_size >= 350 && pe_size < 380) || (pe_size > 530 && pe_size <= 560)) {
                                l2100Result = "D";
                            }
                            if (pe_size > 560 || pe_size < 350) {
                                l2100Result = "E";
                            }

                        }
                        if (libraryType.indexOf("Lnc-Str(exosome)、CircRNA") != -1) {
                            double pe_conc = Double.valueOf((String) bmkMap.get("PE_REGION_250_800_CONC")).doubleValue();
                            String conc = String.format("%.2f", pe_conc);
                            double pe_size = Double.valueOf((String) bmkMap.get("PE_REGION_250_800_SIZE_BP")).doubleValue();
                            String size = String.format("%.2f", pe_size);
                            bmkMap2.put("TASKZJ_L2100_MACNG", conc);       //仪器检测浓度ng/ul
                            bmkMap2.put("TASKZJ_L2100_SIZE", size);          //文库片段大小片段
                            if (pe_size >= 430 && pe_size <= 480) {
                                l2100Result = "A";
                            }
                            if (pe_size >= 380 && pe_size < 430) {
                                l2100Result = "B";
                            }
                            if (pe_size > 480 && pe_size <= 530) {
                                l2100Result = "C";
                            }
                            if ((pe_size >= 350 && pe_size < 380) || (pe_size > 530 && pe_size <= 560)) {
                                l2100Result = "D";
                            }
                            if (pe_size > 560 || pe_size < 350) {
                                l2100Result = "E";
                            }
                        }
                        if (libraryType.indexOf("ATAC-seq") != -1) {
                            double pe_conc = Double.valueOf((String) bmkMap.get("PE_REGION_150_800_CONC")).doubleValue();
                            String conc = String.format("%.2f", pe_conc);
                            double pe_size = Double.valueOf((String) bmkMap.get("PE_REGION_150_800_SIZE_BP")).doubleValue();
                            String size = String.format("%.2f", pe_size);
                            bmkMap2.put("TASKZJ_L2100_MACNG", conc);       //仪器检测浓度ng/ul
                            bmkMap2.put("TASKZJ_L2100_SIZE", size);          //文库片段大小片段
                            if (pe_size >= 430 && pe_size <= 480) {
                                l2100Result = "A";
                            }
                            if (pe_size >= 380 && pe_size < 430) {
                                l2100Result = "B";
                            }
                            if ((pe_size >= 350 && pe_size < 380) || (pe_size >= 530 && pe_size <= 560)) {
                                l2100Result = "C";
                            }
                            if (pe_size > 560 || pe_size < 350) {
                                l2100Result = "D";
                            }
                        }
                        if (libraryType.indexOf("SRNA、SRNA(exosome)") != -1) {
                            double pe_conc = Double.valueOf((String) bmkMap.get("PE_REGION_130_165_CONC")).doubleValue();
                            String conc = String.format("%.2f", pe_conc);
                            double pe_size = Double.valueOf((String) bmkMap.get("PE_REGION_130_165_SIZE_BP")).doubleValue();
                            String size = String.format("%.2f", pe_size);
                            bmkMap2.put("TASKZJ_L2100_MACNG", conc);       //仪器检测浓度ng/ul
                            bmkMap2.put("TASKZJ_L2100_SIZE", size);          //文库片段大小片段
                            if (pe_size >= 135 && pe_size <= 150) {
                                l2100Result = "A";
                            }
                            if (pe_size >= 130 && pe_size < 135) {
                                l2100Result = "B";
                            }
                            if (pe_size > 150 && pe_size <= 155) {
                                l2100Result = "B";
                            }
                            if (pe_size > 155 || pe_size < 130) {
                                l2100Result = "D";
                            }
                        }
                        if (libraryType.indexOf("10x-TCR/BCR-seq、CUT&Tag") != -1) {
                            double pe_conc = Double.valueOf((String) bmkMap.get("PE_REGION_300_1000_CONC")).doubleValue();
                            String conc = String.format("%.2f", pe_conc);
                            double pe_size = Double.valueOf((String) bmkMap.get("PE_REGION_300_1000_SIZE_BP")).doubleValue();
                            String size = String.format("%.2f", pe_size);
                            bmkMap2.put("TASKZJ_L2100_MACNG", conc);       //仪器检测浓度ng/ul
                            bmkMap2.put("TASKZJ_L2100_SIZE", size);          //文库片段大小片段
                            if (pe_size >= 500 && pe_size <= 800) {
                                l2100Result = "A";
                            }
                            if (pe_size > 800 && pe_size < 1000) {
                                l2100Result = "B";
                            }
                            if (pe_size >= 400 && pe_size < 500) {
                                l2100Result = "B";
                            }
                            if (pe_size > 1000 || pe_size < 400) {
                                l2100Result = "D";
                            }
                        }
                        String peWellLabel = (String) bmkMap.get("PE_WELL_LABEL");     //孔位
                        String format = String.format("%.2f", peWellLabel);
                        bmkMap2.put("TASKZJ_QUBIT_ND", format);          //文库Qubit浓度(ng/μl)
                        String qcResult = "";
                        String l2100Unresult = "";
                        if (l2100Result.equals("A")) {
                            qcResult = "合格";
                            l2100Unresult = "A:片段合格";
                        }
                        if (l2100Result.equals("B")) {
                            qcResult = "合格";
                            l2100Unresult = "B:片段偏小，可风险上机";
                        }
                        if (l2100Result.equals("C")) {
                            qcResult = "不合格";
                            l2100Unresult = "C:片段偏大，可风险上机";
                        }
                        if (l2100Result.equals("D")) {
                            qcResult = "不合格";
                            l2100Unresult = "D:片段大小不符合要求，需要处理后上机";
                        }
                        if (l2100Result.equals("E")) {
                            qcResult = "不合格";
                            l2100Unresult = "E：不合格，重新建库";
                        }
                        String quResult = "";
                        String quUnresult = "";

                        double qu = Double.valueOf(format);
                        if (qu >= 0.5) {
                            quResult = "合格";
                            quUnresult = "A:浓度合格";
                        }
                        if (qu > 0.5 && qu < 0.2) {
                            quResult = "合格";
                            quUnresult = "B:浓度不合格，可风险上机";
                        }
                        if (qu >= 0.2) {
                            quResult = "不合格";
                            quUnresult = "C: 浓度不合格，不建议上机";
                        }
                        String zjResult = "不合格";
                        String zjUnresult = "";
                        if (qcResult.equals("合格") && quResult.equals("合格")) {
                            zjResult = "合格";
                        }
                        zjUnresult = "片段：" + l2100Unresult + " ; " + "浓度：" + quUnresult;

                        bmkMap2.put("TASKZJ_L2100_RESULT", qcResult);
                        bmkMap2.put("TASKZJ_L2100_UNRESULT", l2100Unresult);
                        bmkMap2.put("TASKZJ_QUBIT_RESULT", quResult);
                        bmkMap2.put("TASKZJ_QUBIT_UNRESULT", quUnresult);
                        bmkMap2.put("TASKZJ_ZJ_RESULT", zjResult);
                        bmkMap2.put("TASKZJ_ZJ_UNREST", zjUnresult);

                        // int a = SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_PE_DATA_ACQUISITION", bmkMap);
                        if (list1.size() > 0) {
                            SysBasic.updateDataByTableMap(updateJdbcTemplate, "BIO_LIB_QC_INFO", bmkMap2);
                        }
                    } catch (Exception e) {
                    }

                }
            }
        }
        return new CurrResponseResolve(1).put("apiData", "").getData();
    }*/

    @RequestMapping("/listIndex")
    public JSONObject divideIndex(@RequestBody JSONObject data) throws Exception {
        int code = 0;
        //文库类型
        String INDEX_TYPE = SysBasic.toTranStringByObject(data.get("INDEX_TYPE"));
        //index个数
        String INDEX_NUMBER = SysBasic.toTranStringByObject(data.get("INDEX_NUMBER"));

        String type = "MCD";
        if (INDEX_TYPE != "MCD") {
            type = "常规";
        }
        if (!SysBasic.isNumeric(INDEX_NUMBER)) {
            code = -1;
            String message = "index个数【INDEX_NUMBER】请输入正整数！";
            return new CurrResponseResolve(code).put(responseMessageParameter).put("message", message).getData();
        }
        String sqlIndexQuadrant = "select * from BIO_PE_QUADRANT where TYPE= ? ";   //查询上一次index使用的是哪一象限
        List<Map<String, Object>> listIndexQuadrant = queryJdbcTemplate.queryForList(sqlIndexQuadrant, type);
        String quadrant = (String) listIndexQuadrant.get(0).get("QUADRANT");
        try {
            int INDEXNUMBER = SysBasic.toTranIntegerByObject(data.get("INDEX_NUMBER"));
            String sqlcode = "select * from  BIO_LIB_BOARD_INDEX  " +
                    "where BOARD_STATUS = '正常' AND INDEX_TYPE = ?  AND INDEX_QUADRANT != ? " +
                    "ORDER BY SERIAL_NUMBER ASC";
            List<Map<String, Object>> listIndex = queryJdbcTemplate.queryForList(sqlcode, INDEX_TYPE, quadrant);
            if (listIndex.size() > 0) {
                int serialNumber = SysBasic.toTranIntegerByObject(listIndex.get(0).get("SERIAL_NUMBER"));   //使用次数
                int maxUsage = SysBasic.toTranIntegerByObject(listIndex.get(0).get("MAX_USAGE"));            //最大使用次数
                String indexQuadrant = SysBasic.toTranStringByObject(listIndex.get(0).get("INDEX_QUADRANT"));           //本次象限
                if (maxUsage <= serialNumber) {
                    code = -1;
                    String message = "类型为【" + INDEX_TYPE + "】的index板使用次数已达最大次数！";
                    return new CurrResponseResolve(code).put(responseMessageParameter).put("message", message).getData();
                }
                List<Map<JSONObject, List>> returnIndex = new ArrayList<>();
                int no = 0;
                int i = 1;
                while (true) {
                    if (no == listIndex.size()) {
                        no = 0;
                    }

                    Map<String, Object> indexMap = listIndex.get(no);
                    Map<String, Object> updateMap = new HashMap<>();
                    Map<String, Object> IndexQuadrantMap = new HashMap<>();
                    String ID = SysBasic.toTranStringByObject(indexMap.get("ID"));
                    int SERIAL_NUMBER = SysBasic.toTranIntegerByObject(indexMap.get("SERIAL_NUMBER"));
                    updateMap.put("ID", ID);
                    updateMap.put("SERIAL_NUMBER", SERIAL_NUMBER + 1);
                    listIndex.get(no).put("SERIAL_NUMBER", SERIAL_NUMBER + 1);
                    IndexQuadrantMap.put("ID", listIndexQuadrant.get(0).get("ID"));
                    IndexQuadrantMap.put("QUADRANT", indexQuadrant);

                    int v = SysBasic.updateDataByTableMap(updateJdbcTemplate, "BIO_PE_QUADRANT", IndexQuadrantMap);
                    int u = SysBasic.updateDataByTableMap(updateJdbcTemplate, "BIO_LIB_BOARD_INDEX", updateMap);
                    if (u > 0) {
                        code = 1;
                    } else {
                        code = -1;
                        SysBasic.rollBack();
                        return new CurrResponseResolve(code).put(responseMessageParameter).put("message", "index修改失败").getData();
                    }
                    indexMap.put("NO", i);
                    JSONObject jsonObj = new JSONObject(indexMap);
                    LinkedHashMap<JSONObject, List> LinkedHashMap = new LinkedHashMap<>();
                    String sql = "SELECT * FROM BIO_LIB_BOARD_INDEX_MX blbim inner join BIO_LIB_INDEX bli ON blbim.INDEX_ID=bli.ID " +
                            "WHERE blbim.BOARD_ID = ? ORDER BY bli.SEQ_I7_NAME,bli.SEQ_I5_NAME";
                    List<Map<String, Object>> list = queryJdbcTemplate.queryForList(sql, indexMap.get("ID"));
                    LinkedHashMap.put(jsonObj, list);
                    returnIndex.add(LinkedHashMap);
                    no++;
                    if (i == INDEXNUMBER) {
                        break;
                    }
                    i++;
                }
                //更新index
                code = 1;
                return new CurrResponseResolve(code).put(responseMessageParameter).put("INDEX", returnIndex).getData();

            } else {
                code = -1;
                String message = "文库类型【" + INDEX_TYPE + "】未在index维护库中找到！或象限分配不足";
                return new CurrResponseResolve(code).put(responseMessageParameter).put("message", message).getData();
            }

        } catch (Exception e) {
            e.printStackTrace();

            String message = e.getMessage();
            return new CurrResponseResolve(-1).put(responseMessageParameter).put("message", message).getData();

        }

    }

    @RequestMapping("/autodoLane")
    public JSONObject autodoLane(@RequestBody JSONObject data) throws Exception {


        String username = SysBasic.toTranStringByObject(data.get("username")); //人
        Date time = new Date();

        Object[] arrIds = null;
        if (data.get("arrIds") != null) {
            if (data.get("arrIds") instanceof ArrayList) {
                arrIds = SysBasic.toTranObjectsByList((ArrayList) data.get("arrIds"));
            }
        }


        Map<String, Object> configure = (Map<String, Object>) data.get("configure");

        String sqlcode = "SELECT " +
                "T.ID,  " +
                "T.TASKID,  " +
                "T.CUSTOMER_VIP,   " +
                "T.LIBRARY_TYPE_EN,  " +
                "T.TASK_LS_TYPE_LB,  " +
                "T.YFA,   " +
                "T.PROJECT_SUBNO,   " +
                "T.PROJECT_NO,  " +
                "T.PROJECT_NAME,  " +
                "T.TASK_LS_NO,  " +
                "T.SAMPLE_CODE,  " +
                "T.DATA_LIBCODE,  " +
                "T.LIBRARY_CODE,  " +
                "T.SAMPLE_NAME,  " +
                "T.BIO_CODE,  " +
                "T.RD_DEPT,  " +
                "T.INDEX_NAME_,  " +
                "T.SEQ_I5_NAME_,   " +
                "T.SEQ_I7_NAME_,   " +
                "T.DJC_FI_DATE, " +
                "T.LIB_MAN,   " +
                "T.TEST_CODE,   " +
                "T.AMPLIFY_REGIONAL,  " +
                "T.PRIMER_CODE,   " +
                "T.LANENO,   " +
                "T.DJC_JT_DLMD,  " +
                "T.PCR_BOARD_CODE,   " +
                "T.PCR_PLAT_CELL,  " +
                "T.SJ_ARRANGEMENT,  " +
                "T.DATA_SUM,  " +
                "T.DATA_UNIT,  " +
                "T.TASK_LS_REMARKS,  " +
                "T.LIBRARY_NAME,  " +
                "T.THE_DATA_SUM,  " +
                "T.PRODUCT_TYPE,   " +
                "T.TASK_LS_LIBREQUEST,  " +
                "T.TASK_LS_CDATE " +
                "FROM( " +
                "SELECT " +
                "btl.ID, " +
                "'否' AS YFA, " +
                "btl.DATA_LIBCODE, " +
                "bt.ID AS TASKID, " +
                "bt.CUSTOMER_VIP, " +
                "btl.LIBRARY_TYPE_EN, " +
                "bt.TASK_LS_TYPE_LB, " +
                "bt.PROJECT_SUBNO, " +
                "'' AS PROJECT_NO, " +
                "'' AS PROJECT_NAME, " +
                "bt.TASK_LS_NO, " +
                "btl.SAMPLE_CODE, " +
                "btl.LIBRARY_CODE, " +
                "btl.SAMPLE_NAME, " +
                "btl.BIO_CODE, " +
                "null as RD_DEPT, " +
                "btl.INDEX_NAME_, " +
                "btl.SEQ_I5_NAME_, " +
                "btl.SEQ_I7_NAME_, " +
                "btl.DJC_FI_DATE, " +
                "btl.LIB_MAN, " +
                "btl.TEST_CODE, " +
                "btl.AMPLIFY_REGIONAL, " +
                "btl.PRIMER_CODE, " +
                "btl.LANENO, " +
                "btl.DJC_JT_DLMD, " +
                "btl.PCR_BOARD_CODE, " +
                "btl.PCR_PLAT_CELL, " +
                "btl.SJ_ARRANGEMENT, " +
                "btl.DATA_SUM, " +
                "btl.DATA_UNIT, " +
                "bt.TASK_LS_REMARKS, " +
                "btl.LIBRARY_NAME, " +
                "btl.THE_DATA_SUM, " +
                "btl.PRODUCT_TYPE, " +
                "bt.TASK_LS_LIBREQUEST, " +
                "bt.TASK_LS_CDATE " +
                "FROM BIO_TASK_LIB bt " +
                "inner join BIO_TASK_LIBMX btl ON bt.ID=TASK_LS_ID " +
                "UNION ALL " +
                "SELECT " +
                "brtp.ID, " +
                "'是' AS YFA, " +
                "null as DATA_LIBCODE, " +
                "brtp.ID AS TASKID, " +
                "brtp.CUSTOMER_VIP, " +
                "brtp.LIBRARY_TYPE_EN, " +
                "brtp.TASK_LS_TYPE_LB, " +
                "brtp.PROJECT_SUBNO, " +
                "brtp.PROJECT_NO, " +
                "brtp.PROJECT_NAME, " +
                "brtp.TASK_LS_NO, " +
                "brtp.SAMPLE_CODE, " +
                "brtp.LIBRARY_CODE, " +
                "brtp.SAMPLE_NAME, " +
                "brtp.BIO_CODE, " +
                "brtp.RD_DEPT, " +
                "brtp.INDEX_NAME_, " +
                "brtp.SEQ_I5_NAME_, " +
                "brtp.SEQ_I7_NAME_, " +
                "brtp.DJC_FI_DATE, " +
                "brtp.LIB_MAN, " +
                "brtp.TEST_CODE, " +
                "brtp.AMPLIFY_REGIONAL, " +
                "brtp.PRIMER_CODE, " +
                "brtp.LANENO, " +
                "brtp.DJC_JT_DLMD, " +
                "brtp.PCR_BOARD_CODE, " +
                "brtp.PCR_PLAT_CELL, " +
                "brtp.SJ_ARRANGEMENT, " +
                "brtp.DATA_SUM, " +
                "brtp.DATA_UNIT, " +
                "brtp.TASK_LS_REMARKS, " +
                "brtp.LIBRARY_NAME, " +
                "brtp.THE_DATA_SUM, " +
                "brtp.PRODUCT_TYPE, " +
                "brtp.TASK_LS_LIBREQUEST, " +
                "null as TASK_LS_CDATE " +
                "FROM BIO_RD_TASK_POOL brtp " +
                ")T where id in (" + SysBasic.getQuestionMarkBySzie(arrIds.length) + ") " +
                "ORDER BY T.TASK_LS_CDATE ";
        List<Map<String, Object>> listMx = queryJdbcTemplate.queryForList(sqlcode, arrIds);

        HashMap<String, Map<String, Object>> projects = new LinkedHashMap<>();//各项目期号

        if (listMx.size() <= 0) {
            return new CurrResponseResolve(2).put(responseMessageParameter).put("msg", "样品无法满足排lan需求").getData();
        }

        for (Map<String, Object> mapyy : listMx) {
            String subNo = (String) mapyy.get("PROJECT_SUBNO"); //期号
            BigDecimal dataSum = (BigDecimal) mapyy.get("DATA_SUM"); //合同数据量
            BigDecimal sNum = (BigDecimal) mapyy.get("SJ_ARRANGEMENT"); //安排数据量
            double v = SysBasic.toTranDoubleByObject(dataSum);
            if (dataSum != null) {

                double coefficient = 0;   //安排数据量计算系数
                if (v == 0.05) {
                    coefficient = 2.5;
                }
                if (v == 0.04) {
                    coefficient = 2.5;
                }
                if (v == 0.1) {
                    coefficient = 2.3;
                }
                if (v == 0.01) {
                    coefficient = 2.5;
                }
                if (v == 0.3) {
                    coefficient = 2;
                }
                BigDecimal bcoefficient = new BigDecimal(coefficient);
                sNum = dataSum.multiply(bcoefficient).setScale(3, BigDecimal.ROUND_DOWN);
                mapyy.put("SJ_ARRANGEMENT", sNum);
            }
            if (projects.containsKey(subNo)) {
                ((List<Map<String, Object>>) projects.get(subNo).get("list")).add(mapyy);
                BigDecimal a = (BigDecimal) projects.get(subNo).get("sNum");
                BigDecimal b = a.add(sNum);
                projects.get(subNo).put("sNum", b);
            } else {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("sNum", sNum);
                List<Map<String, Object>> list = new LinkedList<>();
                list.add(mapyy);
                map.put("list", list);
                projects.put(subNo, map);
            }
        }

        HashMap<String, Object> Pretreatment = new LinkedHashMap<>();
        List<Map<String, Object>> sampleLists = new LinkedList<>();
        List<String> indexLists = new LinkedList<>();
        Pretreatment.put("datas", 0); //总数据量
        Pretreatment.put("sampleLists", sampleLists); //总样品数
        Pretreatment.put("indexLists", indexLists); //总index

        double maxNum = SysBasic.toTranDoubleByObject(configure.get("MAX_NUM")); //每条LANE最大样品数
        double minNum = SysBasic.toTranDoubleByObject(configure.get("MIN_NUM")); //每条LANE最小样品数
        double maxData = SysBasic.toTranDoubleByObject(configure.get("MAX_DATA")); //每条LANE最大安排数据量
        double minData = SysBasic.toTranDoubleByObject(configure.get("MIN_DATA")); //每条LANE最大样品小数据量

        for (String key : projects.keySet()) {
            double datas = SysBasic.toTranDoubleByObject(Pretreatment.get("datas"));//总数据量
            sampleLists = ((List<Map<String, Object>>) Pretreatment.get("sampleLists"));//总样品数
            indexLists = ((List<String>) Pretreatment.get("indexLists"));//总index
            int sampleListsSize = sampleLists.size();
            double sNum = SysBasic.toTranDoubleByObject(projects.get(key).get("sNum"));  //一期安排数据量
            List<Map<String, Object>> projectList = (List<Map<String, Object>>) projects.get(key).get("list");
            int projectListSize = projectList.size();//一期样品数据量

            List<Map<String, Object>> sampleLists2 = new LinkedList<>();
            List<String> indexLists2 = new LinkedList<>();
            int a = 1;
            if ((datas + sNum) < maxData && (sampleListsSize + projectListSize) < maxNum) {
                for (int j = 0; j < projectList.size(); j++) {
                    Map mapXM = projectList.get(j);
                    String indexName = (String) mapXM.get("INDEX_NAME_");
                    if (!(indexLists.contains(indexName)) && !(indexLists2.contains(indexName))) {
                        indexLists2.add(indexName);
                        sampleLists2.add(mapXM);
                    } else {
                        a = 2;
                        break;
                    }
                }
                if (a == 1) {
                    sampleLists.addAll(sampleLists2); //总样品数
                    indexLists.addAll(indexLists2); //总index
                    Pretreatment.put("datas", datas + sNum); //总数据量
                    Pretreatment.put("sampleLists", sampleLists); //总样品数
                    Pretreatment.put("indexLists", indexLists); //总index
                }
            }
        }

        double datas = SysBasic.toTranDoubleByObject(Pretreatment.get("datas"));//总数据量
        sampleLists = ((List<Map<String, Object>>) Pretreatment.get("sampleLists"));//总样品数
        int sampleListsSize = sampleLists.size();
        if (datas < maxData && datas > minData && sampleListsSize < maxNum && sampleListsSize > minNum) {

            String sqlLane = " SELECT      " +
                    " a.LANE_NO ," +
                    " a.SYS_INSERTTIME " +
                    " from  BIO_LANE_INFO a, " +
                    " (select  max(LANE_NO) as LANE_NO  from  BIO_LANE_INFO  where LANE_NO like 'R%') b  " +
                    " where a.LANE_NO =b.LANE_NO ";
            List<Map<String, Object>> list = queryJdbcTemplate.queryForList(sqlLane);

            Date useTime = SysBasic.toTranDateByValidAndFormat(list.get(0).get("SYS_INSERTTIME"));
            Date date = new Date();
            int year = date.getYear();
            int month = date.getMonth();
            int day = date.getDay();
            int day1 = useTime.getDay();
            int month1 = useTime.getMonth();
            int year1 = useTime.getYear();
            String str = (String) list.get(0).get("LANE_NO");
            String laneNo;
            String s = StringUtils.substringBetween(str, "R", "-");
            int a = Integer.parseInt(s) + 1;
            if (year == year1 & month1 == month & day == day1) {
                String s1 = str.substring(1 + str.indexOf("-"));
                int b = Integer.parseInt(s1) + 1;
                laneNo = "R" + a + "-" + b;
            } else {
                laneNo = "R" + a + "-1";
            }


            List ids = new LinkedList();
            List lanenos = new LinkedList();
            List taskIDs = new LinkedList();
            List<Map<String, Object>> indexS = new LinkedList();
            for (int i = 0; i < sampleLists.size(); i++) {
                ids.add(sampleLists.get(i).get("ID"));
                lanenos.add(laneNo);
                taskIDs.add(sampleLists.get(i).get("TASKID"));

                Map<String, Object> map = new HashMap<String, Object>();
                map.put("YFA", sampleLists.get(i).get("YFA"));
                map.put("SJ_ARRANGEMENT", sampleLists.get(i).get("SJ_ARRANGEMENT"));
                map.put("BIO_CODE", sampleLists.get(i).get("BIO_CODE"));
                map.put("LIB_LIBRARY_CODE", sampleLists.get(i).get("LIBRARY_CODE"));
                map.put("INDEX_NAME", sampleLists.get(i).get("INDEX_NAME_"));
                map.put("SEQ_I7_NAME", sampleLists.get(i).get("SEQ_I7_NAME_"));
                map.put("SEQ_I5_NAME", sampleLists.get(i).get("SEQ_I5_NAME_"));
                map.put("TASK_ID", sampleLists.get(i).get("TASKID"));
                map.put("LANE_MX_CUSTOMER_TYPE", sampleLists.get(i).get("CUSTOMER_VIP"));
                map.put("LIBRARY_TYPE", sampleLists.get(i).get("LIBRARY_TYPE_EN"));
                map.put("LIBRARY_CODE", sampleLists.get(i).get("BIO_CODE"));
                indexS.add(map);
            }
            List laneS = new LinkedList();
            List laneIds = new LinkedList();
            for (int i = 0; i < lanenos.size(); i++) {
                if (!(laneS.contains(lanenos.get(i)))) {
                    laneIds.add(SysBasic.getUUID());
                    laneS.add(lanenos.get(i));
                }
            }
            List addToLane = new LinkedList();
            List addToLib = new LinkedList();
            List objectupmx = new LinkedList();
            List objectupmxyf = new LinkedList();
            List thetaskids = new LinkedList();
            List objectMx = new LinkedList();


            for (int i = 0; i < laneS.size(); i++) {

                int libnumber = 0;

                for (int j = 0; j < lanenos.size(); j++) {
                    if (laneS.get(i).equals(lanenos.get(i))) {
                        String libid = SysBasic.getUUID();
                        Map<String, Object> libMap = new HashMap<>();
                        libMap.put("ID", libid);
                        libMap.put("TASK_LIB_MX_ID", ids.get(j));//联联任务ID
                        libMap.put("LANE_ID", laneIds.get(i));//关联LANE
                        libMap.put("INDEX_NAME", indexS.get(j).get("INDEX_NAME"));//index
                        libMap.put("SEQ_I7_NAME", indexS.get(j).get("SEQ_I7_NAME"));//i7编号
                        libMap.put("SEQ_I5_NAME", indexS.get(j).get("SEQ_I5_NAME"));//i5编号
                        libMap.put("SEQ_I7_1", indexS.get(j).get("SEQ_I7_1"));//I7端
                        libMap.put("SEQ_I5_1", indexS.get(j).get("SEQ_I5_1"));//I5序列-V1.0
                        libMap.put("SEQ_I5_2", indexS.get(j).get("SEQ_I5_2"));//I5序列-V1.5
                        libMap.put("SEQ_ODBY", indexS.get(j).get("SEQ_ODBY"));//分组
                        libMap.put("SEQ_CELL", indexS.get(j).get("SEQ_CELL"));//对应孔位
                        libMap.put("LIBRARY_CODE", indexS.get(j).get("LIB_LIBRARY_CODE"));//文库编号
                        libMap.put("SYS_MAN", username);//实验员
                        libMap.put("SYS_INSERTTIME", time);//开始日期
                        addToLib.add(libMap);


                        Map<String, Object> mxMap = new HashMap<>();
                        mxMap.put("ID", SysBasic.getUUID());
                        mxMap.put("TASK_ID", indexS.get(j).get("TASK_ID"));
                        mxMap.put("LANE_ID", laneIds.get(i));
                        mxMap.put("LANE_MX_CUSTOMER_TYPE", indexS.get(j).get("LANE_MX_CUSTOMER_TYPE"));//项目等级
                        mxMap.put("LIBRARY_TYPE", indexS.get(j).get("LIBRARY_TYPE"));//文库类型
                        mxMap.put("INDEX_NAME", indexS.get(j).get("INDEX_NAME"));//index名称
                        mxMap.put("LIBRARY_CODE", indexS.get(j).get("LIBRARY_CODE"));
                        mxMap.put("ISMCD", "是");
                        mxMap.put("SEQ_ID", ids.get(j));
                        mxMap.put("LIB_ID", libid);
                        mxMap.put("SEQ_LIBID", libid);//排单池的文库ID,有可能是混库ID
                        objectMx.add(mxMap);

                        if (indexS.get(j).get("YFA").equals("是")) {
                            Map<String, Object> mxyfMap = new HashMap<>();
                            mxyfMap.put("ID", ids.get(j));
                            mxyfMap.put("MCD_PASS", "是");
                            mxyfMap.put("LANENO", laneS.get(i));
                            mxyfMap.put("SJ_ARRANGEMENT", indexS.get(j).get("SJ_ARRANGEMENT"));
                            objectupmxyf.add(mxyfMap);
                        } else {
                            Map<String, Object> upmxMap = new HashMap<>();
                            upmxMap.put("ID", ids.get(j));
                            upmxMap.put("MCD_PASS", "是");
                            upmxMap.put("LANENO", laneS.get(i));
                            upmxMap.put("SJ_ARRANGEMENT", indexS.get(j).get("SJ_ARRANGEMENT"));
                            objectupmx.add(upmxMap);

                            //主单ID排重

                            if (!(thetaskids.contains(taskIDs.get(j)))) {
                                Map<String, Object> taskMap = new HashMap<>();
                                taskMap.put("ID", taskIDs.get(j));
                                taskMap.put("TASK_LS_STATUS", "建库中");
                                thetaskids.add(taskMap);
                            }
                        }
                        libnumber++;
                    }
                }
                Map<String, Object> laneMap = new HashMap<>();
                laneMap.put("ID", laneIds.get(i));
                laneMap.put("LANE_NO", laneS.get(i));
                laneMap.put("LANE_SM_NUM", libnumber);
                laneMap.put("ISMCD", "是");
                laneMap.put("LANE_SEQ_PLAT", "NGS");
                laneMap.put("LANE_TYPE", "MCD混样建库预排Lane");
                laneMap.put("SYS_MAN", username);
                laneMap.put("SYS_INSERTTIME", time);
                addToLane.add(laneMap);

            }
            for (int i = 0; i < addToLane.size(); i++) {
                SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_LANE_INFO", (Map<String, Object>) addToLane.get(i));
            }

            for (int i = 0; i < addToLib.size(); i++) {
                SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_LIB_INFO", (Map<String, Object>) addToLib.get(i));
            }

            if (objectMx.size() > 0) {
                for (int i = 0; i < objectMx.size(); i++) {
                    SysBasic.insertDataByTableMap(updateJdbcTemplate, "BIO_LANE_MX", (Map<String, Object>) objectMx.get(i));
                }
            }
            if (thetaskids.size() > 0) {
                for (int i = 0; i < thetaskids.size(); i++) {
                    SysBasic.updateDataByTableMap(updateJdbcTemplate, "BIO_TASK_LIB", (Map<String, Object>) thetaskids.get(i));
                }
            }
            if (objectupmx.size() > 0) {
                for (int i = 0; i < objectupmx.size(); i++) {
                    SysBasic.updateDataByTableMap(updateJdbcTemplate, "BIO_TASK_LIBMX", (Map<String, Object>) objectupmx.get(i));
                }
            }
            if (objectupmxyf.size() > 0) {
                for (int i = 0; i < objectupmxyf.size(); i++) {
                    SysBasic.updateDataByTableMap(updateJdbcTemplate, "BIO_RD_TASK_POOL", (Map<String, Object>) objectupmxyf.get(i));
                }
            }
            return new CurrResponseResolve(1).put(responseMessageParameter).put("msg", "排lan成功").getData();

        }
        return new CurrResponseResolve(2).put(responseMessageParameter).put("msg", "样品无法满足排lan需求").getData();
    }

    public Map PEShineLimsRna() {
        Map<String, Object> PSLMap = new LinkedHashMap<String, Object>();
        PSLMap.put("PlateName", "PE_PLATE_NAME");
        PSLMap.put("WellLabel", "PE_WELL_LABEL");
        PSLMap.put("SampleName", "PE_SAMPLE_NAME");
        PSLMap.put("Type", "PE_TYPE");
        PSLMap.put("Size[BP]", "PE_REGION_SIZE_BP");
        PSLMap.put("Region[0-150] Conc. (ng/ul)", "PE_REGION_0_150_CONC");
        PSLMap.put("Region[150-300] Conc. (ng/ul)", "PE_REGION_150_300_CONC");
        PSLMap.put("Region[0-150] % Purity", "PE_REGION_0_150_PURITY");
        PSLMap.put("Region[150-300] % Purity", "PE_REGION_150_300_PURITY");
        PSLMap.put("Height", "PE_HEIGHT");
        PSLMap.put("Result", "PE_RESULT");
        PSLMap.put("Conc. (ng/ul)", "PE_PURITY");
        PSLMap.put("% Purity", "PE_CONC");
        PSLMap.put("Region[300-2500] Size [BP]", "PE_REGION_300_2500_SIZE_BP");
        PSLMap.put("Region[300-800] Size [BP]", "PE_REGION_300_800_SIZE_BP");
        PSLMap.put("Region[130-165] Size [BP]", "PE_REGION_130_165_SIZE_BP");
        PSLMap.put("Region[150-800] Size [BP]", "PE_REGION_150_800_SIZE_BP");
        PSLMap.put("Region[250-800] Size [BP]", "PE_REGION_250_800_SIZE_BP");
        PSLMap.put("Region[300-2500] Conc. (ng/ul)", "PE_REGION_300_2500_CONC");
        PSLMap.put("Region[300-2500] % Purity", "PE_REGION_300_2500_PURITY");
        PSLMap.put("Region[300-800] Conc. (ng/ul)", "PE_REGION_300_800_CONC");
        PSLMap.put("Region[300-800] % Purity", "PE_REGION_300_800_PURITY");
        PSLMap.put("Region[800-1000] Conc. (ng/ul)", "PE_REGION_800_1000_CONC");
        PSLMap.put("Region[800-1000] % Purity", "PE_REGION_800_1000_PURITY");
        PSLMap.put("Region[150-250] Conc. (ng/ul)", "PE_REGION_150_250_CONC");
        PSLMap.put("Region[150-250] % Purity", "PE_REGION_150_250_PURITY");
        PSLMap.put("Region[250-800] Conc. (ng/ul)", "PE_REGION_250_800_CONC");
        PSLMap.put("Region[250-800] % Purity", "PE_REGION_250_800_PURITY");
        PSLMap.put("Region[150-900] Conc. (ng/ul)", "PE_REGION_150_900_CONC");
        PSLMap.put("Region[150-900] % Purity", "PE_REGION_150_900_PURITY");
        PSLMap.put("Region[0-165] Conc. (ng/ul)", "PE_REGION_0_165_CONC");
        PSLMap.put("Region[0-165]] % Purity", "PE_REGION_0_165_PURITY");
        PSLMap.put("Region[165-900] Conc. (ng/ul)", "PE_REGION_165_900_CONC");
        PSLMap.put("Region[165-900] % Purity", "PE_REGION_165_900_PURITY");

        return PSLMap;
    }
}
