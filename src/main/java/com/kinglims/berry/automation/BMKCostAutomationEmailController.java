package com.kinglims.berry.automation;


import com.alibaba.fastjson.JSONObject;
import com.kinglims.berry.rdtask.*;
import com.kinglims.framework.utils.system.SysBasic;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.*;


public class BMKCostAutomationEmailController extends QuartzJobBean {
    @Autowired
    private JdbcTemplate queryJdbcTemplate;

    @Autowired
    private JdbcTemplate updateJdbcTemplate;


    @Autowired
    private BMKProductionResearchImport BmkRI;


    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        //北京
        String bjww = " SELECT \n" +
                " sop.SOP_REVIEW_MAN,sop.SYS_MAN_JL,sop.EX_DH_NO\n" +
                "FROM BIO_BJ_PRODUCTION_ORDER bbpo \n" +
                "inner join BIO_BZ_MATERIEL_SOP_JL sop on bbpo.EX_DH_NO=sop.EX_DH_NO\n" +
                " where bbpo.PUSH_STATE in ('推送失败')and sop.COLLECTION_FLAG='已归集'\n" +
                " GROUP BY sop.SOP_REVIEW_MAN,sop.SYS_MAN_JL,sop.EX_DH_NO";
        List<Map<String, Object>> listbjww = queryJdbcTemplate.queryForList(bjww);
        String str = "";
        if (listbjww.size() > 0) {

            for (int i = 0; i < listbjww.size(); i++) {
                Map<String, Object> map = listbjww.get(i);
                String sopReviewMan = (String) map.get("SOP_REVIEW_MAN");
                String sysManJl = (String) map.get("SYS_MAN_JL");
                String exNo = (String) map.get("EX_DH_NO");
                str = "北京生产订单【" + exNo + "】推送失败";

                String bjwwMan = "select * from BIO_COST_EMAIL where  C_MAN=? and C_USE='Y'";
                List<Map<String, Object>> listbjwwMan = queryJdbcTemplate.queryForList(bjwwMan, sopReviewMan);
                if (listbjwwMan.size() > 0) {

                    HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                    if (!sopReviewMan.equals(sysManJl)) {
                        String bjwwManJl = "select * from BIO_COST_EMAIL where  C_MAN=? and C_USE='Y'";
                        List<Map<String, Object>> listbjwwManJl = queryJdbcTemplate.queryForList(bjwwManJl, sysManJl);
                        String EMAIL_RECEIVER_COPY_TO = SysBasic.toTranStringByObject((String) listbjwwManJl.get(0).get("C_EMAIL"));//抄送人
                        SampleMap.put("EMAIL_RECEIVER_COPY_TO", EMAIL_RECEIVER_COPY_TO);
                    }
                    String EMAIL_TITLE = SysBasic.toTranStringByObject("成本推送失败邮件");//标题
                    String EMAIL_RECEIVER = SysBasic.toTranStringByObject((String) listbjwwMan.get(0).get("C_EMAIL"));//接收人
                    String EMAIL_CONTENT = SysBasic.toTranStringByObject(str);//内容

                    SampleMap.put("EMAIL_TITLE", EMAIL_TITLE);
                    SampleMap.put("EMAIL_RECEIVER", EMAIL_RECEIVER);
                    SampleMap.put("EMAIL_CONTENT", EMAIL_CONTENT);
                    BmkRI.automationEmail(new JSONObject(SampleMap));

                } else {
                    String EMAIL_TITLE = SysBasic.toTranStringByObject("成本推送失败邮件");//标题
                    String EMAIL_RECEIVER = SysBasic.toTranStringByObject("<EMAIL>");//接收人
                    String EMAIL_CONTENT = SysBasic.toTranStringByObject(str);//内容
                    HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                    SampleMap.put("EMAIL_TITLE", EMAIL_TITLE);
                    SampleMap.put("EMAIL_RECEIVER", EMAIL_RECEIVER);
                    SampleMap.put("EMAIL_CONTENT", EMAIL_CONTENT);
                    BmkRI.automationEmail(new JSONObject(SampleMap));
                }
            }



        }
        //青岛
        String qdww = "SELECT  sop.SOP_REVIEW_MAN,sop.SYS_MAN_JL,sop.EX_DH_NO\n" +
                "FROM BIO_QD_PRODUCTION_ORDER bbpo \n" +
                "inner join BIO_BZ_MATERIEL_SOP_JL sop on bbpo.EX_DH_NO=sop.EX_DH_NO\n" +
                "where bbpo.PUSH_STATE  in ('推送失败') and sop.COLLECTION_FLAG='已归集'\n" +
                "GROUP BY sop.SOP_REVIEW_MAN,sop.SYS_MAN_JL,sop.EX_DH_NO";
        List<Map<String, Object>> listqdww = queryJdbcTemplate.queryForList(qdww);
        if (listqdww.size() > 0) {

            for (int i = 0; i < listqdww.size(); i++) {
                Map<String, Object> map = listqdww.get(i);
                String sopReviewMan = (String) map.get("SOP_REVIEW_MAN");
                String sysManJl = (String) map.get("SYS_MAN_JL");
                String exNo = (String) map.get("EX_DH_NO");
                str = "青岛U8生产订单接口【" + exNo + "】推送失败";

                String bjwwMan = "select * from BIO_COST_EMAIL where  C_MAN=? and C_USE='Y'";
                List<Map<String, Object>> listbjwwMan = queryJdbcTemplate.queryForList(bjwwMan, sopReviewMan);
                if (listbjwwMan.size() > 0) {

                    HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                    if (!sopReviewMan.equals(sysManJl)) {
                        String bjwwManJl = "select * from BIO_COST_EMAIL where  C_MAN=? and C_USE='Y'";
                        List<Map<String, Object>> listbjwwManJl = queryJdbcTemplate.queryForList(bjwwManJl, sysManJl);
                        String EMAIL_RECEIVER_COPY_TO = SysBasic.toTranStringByObject((String) listbjwwManJl.get(0).get("C_EMAIL"));//抄送人
                        SampleMap.put("EMAIL_RECEIVER_COPY_TO", EMAIL_RECEIVER_COPY_TO);
                    }
                    String EMAIL_TITLE = SysBasic.toTranStringByObject("成本推送失败邮件");//标题
                    String EMAIL_RECEIVER = SysBasic.toTranStringByObject((String) listbjwwMan.get(0).get("C_EMAIL"));//接收人
                    String EMAIL_CONTENT = SysBasic.toTranStringByObject(str);//内容

                    SampleMap.put("EMAIL_TITLE", EMAIL_TITLE);
                    SampleMap.put("EMAIL_RECEIVER", EMAIL_RECEIVER);
                    SampleMap.put("EMAIL_CONTENT", EMAIL_CONTENT);
                    BmkRI.automationEmail(new JSONObject(SampleMap));

                } else {
                    String EMAIL_TITLE = SysBasic.toTranStringByObject("成本推送失败邮件");//标题
                    String EMAIL_RECEIVER = SysBasic.toTranStringByObject("<EMAIL>");//接收人
                    String EMAIL_CONTENT = SysBasic.toTranStringByObject(str);//内容
                    HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                    SampleMap.put("EMAIL_TITLE", EMAIL_TITLE);
                    SampleMap.put("EMAIL_RECEIVER", EMAIL_RECEIVER);
                    SampleMap.put("EMAIL_CONTENT", EMAIL_CONTENT);
                    BmkRI.automationEmail(new JSONObject(SampleMap));
                }
            }
        }


        //委外订单
        qdww = " SELECT \n" +
                " sop.SOP_REVIEW_MAN,sop.SYS_MAN_JL,sop.EX_DH_NO\n" +
                " FROM BIO_WW_ORDER bbpo \n" +
                "inner join BIO_BZ_MATERIEL_SOP_JL sop on bbpo.EX_DH_NO=sop.EX_DH_NO\n" +
                " where bbpo.PUSH_STATE in ('推送失败')and sop.COLLECTION_FLAG='已归集'\n" +
                " GROUP BY sop.SOP_REVIEW_MAN,sop.SYS_MAN_JL,sop.EX_DH_NO";
        List<Map<String, Object>> listwwdd = queryJdbcTemplate.queryForList(qdww);
        str = "";
        if (listwwdd.size() > 0) {
            for (int i = 0; i < listwwdd.size(); i++) {
                Map<String, Object> map = listwwdd.get(i);
                String sopReviewMan = (String) map.get("SOP_REVIEW_MAN");
                String sysManJl = (String) map.get("SYS_MAN_JL");
                String exNo = (String) map.get("EX_DH_NO");
                str = "委外订单接口【" + exNo + "】推送失败";

                String bjwwMan = "select * from BIO_COST_EMAIL where  C_MAN=? and C_USE='Y'";
                List<Map<String, Object>> listbjwwMan = queryJdbcTemplate.queryForList(bjwwMan, sopReviewMan);
                if (listbjwwMan.size() > 0) {
                    HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                    if (!sopReviewMan.equals(sysManJl)) {
                        String bjwwManJl = "select * from BIO_COST_EMAIL where  C_MAN=? and C_USE='Y'";
                        List<Map<String, Object>> listbjwwManJl = queryJdbcTemplate.queryForList(bjwwManJl, sysManJl);
                        String EMAIL_RECEIVER_COPY_TO = SysBasic.toTranStringByObject((String) listbjwwManJl.get(0).get("C_EMAIL"));//抄送人
                        SampleMap.put("EMAIL_RECEIVER_COPY_TO", EMAIL_RECEIVER_COPY_TO);
                    }
                    String EMAIL_TITLE = SysBasic.toTranStringByObject("成本推送失败邮件");//标题
                    String EMAIL_RECEIVER = SysBasic.toTranStringByObject((String) listbjwwMan.get(0).get("C_EMAIL"));//接收人
                    String EMAIL_CONTENT = SysBasic.toTranStringByObject(str);//内容

                    SampleMap.put("EMAIL_TITLE", EMAIL_TITLE);
                    SampleMap.put("EMAIL_RECEIVER", EMAIL_RECEIVER);
                    SampleMap.put("EMAIL_CONTENT", EMAIL_CONTENT);
                    BmkRI.automationEmail(new JSONObject(SampleMap));

                } else {
                    String EMAIL_TITLE = SysBasic.toTranStringByObject("成本推送失败邮件");//标题
                    String EMAIL_RECEIVER = SysBasic.toTranStringByObject("<EMAIL>");//接收人
                    String EMAIL_CONTENT = SysBasic.toTranStringByObject(str);//内容
                    HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                    SampleMap.put("EMAIL_TITLE", EMAIL_TITLE);
                    SampleMap.put("EMAIL_RECEIVER", EMAIL_RECEIVER);
                    SampleMap.put("EMAIL_CONTENT", EMAIL_CONTENT);
                    BmkRI.automationEmail(new JSONObject(SampleMap));
                }
            }
        }


        //材料出库-生产
        qdww = " SELECT \n" +
                " sop.SOP_REVIEW_MAN,sop.SYS_MAN_JL,sop.EX_DH_NO\n" +
                " FROM BIO_OUT_SOURCING bbpo \n" +
                "inner join BIO_BZ_MATERIEL_SOP_JL sop on bbpo.EX_DH_NO=sop.EX_DH_NO\n" +
                " where bbpo.PUSH_STATE in ('推送失败')and sop.COLLECTION_FLAG='已归集'\n" +
                " GROUP BY sop.SOP_REVIEW_MAN,sop.SYS_MAN_JL,sop.EX_DH_NO";
        List<Map<String, Object>> listclck = queryJdbcTemplate.queryForList(qdww);
        str = "";
        if (listclck.size() > 0) {
            for (int i = 0; i < listclck.size(); i++) {
                Map<String, Object> map = listclck.get(i);
                String sopReviewMan = (String) map.get("SOP_REVIEW_MAN");
                String sysManJl = (String) map.get("SYS_MAN_JL");
                String exNo = (String) map.get("EX_DH_NO");
                str = "材料出库单-生产【" + exNo + "】推送失败";

                String bjwwMan = "select * from BIO_COST_EMAIL where  C_MAN=? and C_USE='Y'";
                List<Map<String, Object>> listbjwwMan = queryJdbcTemplate.queryForList(bjwwMan, sopReviewMan);
                if (listbjwwMan.size() > 0) {
                    HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                    if (!sopReviewMan.equals(sysManJl)) {
                        String bjwwManJl = "select * from BIO_COST_EMAIL where  C_MAN=? and C_USE='Y'";
                        List<Map<String, Object>> listbjwwManJl = queryJdbcTemplate.queryForList(bjwwManJl, sysManJl);
                        String EMAIL_RECEIVER_COPY_TO = SysBasic.toTranStringByObject((String) listbjwwManJl.get(0).get("C_EMAIL"));//抄送人
                        SampleMap.put("EMAIL_RECEIVER_COPY_TO", EMAIL_RECEIVER_COPY_TO);
                    }
                    String EMAIL_TITLE = SysBasic.toTranStringByObject("成本推送失败邮件");//标题
                    String EMAIL_RECEIVER = SysBasic.toTranStringByObject((String) listbjwwMan.get(0).get("C_EMAIL"));//接收人
                    String EMAIL_CONTENT = SysBasic.toTranStringByObject(str);//内容

                    SampleMap.put("EMAIL_TITLE", EMAIL_TITLE);
                    SampleMap.put("EMAIL_RECEIVER", EMAIL_RECEIVER);
                    SampleMap.put("EMAIL_CONTENT", EMAIL_CONTENT);
                    BmkRI.automationEmail(new JSONObject(SampleMap));

                } else {
                    String EMAIL_TITLE = SysBasic.toTranStringByObject("成本推送失败邮件");//标题
                    String EMAIL_RECEIVER = SysBasic.toTranStringByObject("<EMAIL>");//接收人
                    String EMAIL_CONTENT = SysBasic.toTranStringByObject(str);//内容
                    HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                    SampleMap.put("EMAIL_TITLE", EMAIL_TITLE);
                    SampleMap.put("EMAIL_RECEIVER", EMAIL_RECEIVER);
                    SampleMap.put("EMAIL_CONTENT", EMAIL_CONTENT);
                    BmkRI.automationEmail(new JSONObject(SampleMap));
                }
            }
        }
        //研发出库
        qdww = "SELECT\n" +
                "*\n" +
                "FROM BIO_GOO_RDRECORD09 bbpo\n" +
                "where bbpo.PUSH_STATE  in ('推送失败')";
        List<Map<String, Object>> listyfcl = queryJdbcTemplate.queryForList(qdww);
        str = "";
        if (listyfcl.size() > 0) {
            str = "材料出库单-生产";
            for (int i = 0; i < listyfcl.size(); i++) {
                Map<String, Object> map = listclck.get(i);

                String exNo = (String) listyfcl.get(i).get("EX_DH_NO");
                String sopReviewMan = (String) listyfcl.get(i).get("DEFINE12");
                str = "材料出库单-研发【" + exNo + "】推送失败";
                String bjwwMan = "select * from BIO_COST_EMAIL where  C_MAN=? and C_USE='Y'";
                List<Map<String, Object>> listbjwwMan = queryJdbcTemplate.queryForList(bjwwMan, sopReviewMan);
                if (listbjwwMan.size() > 0) {
                    HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                    String EMAIL_TITLE = SysBasic.toTranStringByObject("成本推送失败邮件");//标题
                    String EMAIL_RECEIVER = SysBasic.toTranStringByObject((String) listbjwwMan.get(0).get("C_EMAIL"));//接收人
                    String EMAIL_CONTENT = SysBasic.toTranStringByObject(str);//内容
                    SampleMap.put("EMAIL_TITLE", EMAIL_TITLE);
                    SampleMap.put("EMAIL_RECEIVER", EMAIL_RECEIVER);
                    SampleMap.put("EMAIL_CONTENT", EMAIL_CONTENT);
                    BmkRI.automationEmail(new JSONObject(SampleMap));

                } else {
                    String EMAIL_TITLE = SysBasic.toTranStringByObject("成本推送失败邮件");//标题
                    String EMAIL_RECEIVER = SysBasic.toTranStringByObject("<EMAIL>");//接收人
                    String EMAIL_CONTENT = SysBasic.toTranStringByObject(str);//内容
                    HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                    SampleMap.put("EMAIL_TITLE", EMAIL_TITLE);
                    SampleMap.put("EMAIL_RECEIVER", EMAIL_RECEIVER);
                    SampleMap.put("EMAIL_CONTENT", EMAIL_CONTENT);
                    BmkRI.automationEmail(new JSONObject(SampleMap));
                }
            }

        }
        //采购入库
        qdww = "SELECT\n" +
                "*\n" +
                "FROM BIO_WW_PURCHASE_RECEIPT bbpo\n" +
                "where bbpo.PUSH_STATE  in ( '推送失败')";
        List<Map<String, Object>> listwwcg = queryJdbcTemplate.queryForList(qdww);
        str = "";
        if (listwwcg.size() > 0) {
            str = "材料出库单-生产";
            for (int i = 0; i < listwwcg.size(); i++) {
                Map<String, Object> map = listclck.get(i);

                String exNo = (String) listyfcl.get(i).get("EX_DH_NO");

                str = "委外采购入库单【" + exNo + "】推送失败";

                    String EMAIL_TITLE = SysBasic.toTranStringByObject("成本推送失败邮件");//标题
                    String EMAIL_RECEIVER = SysBasic.toTranStringByObject("<EMAIL>");//接收人
                    String EMAIL_CONTENT = SysBasic.toTranStringByObject(str);//内容
                    HashMap<String, Object> SampleMap = new LinkedHashMap<>();
                    SampleMap.put("EMAIL_TITLE", EMAIL_TITLE);
                    SampleMap.put("EMAIL_RECEIVER", EMAIL_RECEIVER);
                    SampleMap.put("EMAIL_CONTENT", EMAIL_CONTENT);
                    BmkRI.automationEmail(new JSONObject(SampleMap));

            }

        }
    }
}
