package com.kinglims.berry.collect.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kinglims.berry.collect.ResultSet;
import com.kinglims.berry.collect.dao.ApiDao;

@Service
public class ApiService {
    
    @Autowired
    private ApiDao apiDao;

    public ResultSet getAllOnconiCQInfo(String id) {
        List<Map<String,Object>> list = apiDao.getAllOnconiCQInfo(id);
        Map<String,Map<String,Object>> map = new HashMap<String,Map<String,Object>>();
        ResultSet rs = new ResultSet();
        for(Map<String,Object> m : list) {
            String flowCell = m.get("FLOWCELL").toString();
            Map<String,Object> mmap = map.get(flowCell);
            if(mmap == null) {
                mmap = new HashMap<String,Object>();
                mmap.put("LANE_LIBRARY_NAME", new ArrayList<Map<String,Object>>());
                mmap.put("LANEINFO", new ArrayList<Map<String,Object>>());
                mmap.put("MAIL", "");
                mmap.put("PROJECTINFO", new ArrayList<Map<String,Object>>());
                map.put(flowCell, mmap);
            }
            
            List<Map<String,Object>> llnList = (List<Map<String, Object>>) mmap.get("LANE_LIBRARY_NAME");
            Map<String,Object> mmmp = new HashMap<String,Object>();
            mmmp.put("LANE", m.get("LLN_LANE"));
            mmmp.put("LANE_BUSINESS_TYPE", m.get("LLN_LANE_BUSINESS_TYPE"));
            mmmp.put("LANE_ID", m.get("LLN_LANE_ID"));
            mmmp.put("LIBRARY_NAME", m.get("LLN_LIBRARY_NAME"));
            llnList.add(mmmp);
            
            List<Map<String,Object>> liList = (List<Map<String, Object>>) mmap.get("LANEINFO");
            mmmp = new HashMap<String,Object>();
            mmmp.put("CQ_DATE", m.get("LANEINFO_CQ_DATE"));
            mmmp.put("FLOWCELL", m.get("LANEINFO_FLOWCELL"));
            mmmp.put("INDEXCONTENT", m.get("LANEINFO_INDEXCONTENT"));
            mmmp.put("LANE", m.get("LANEINFO_LANE"));
            mmmp.put("LANE_ID", m.get("LANEINFO_LANE_ID"));
            mmmp.put("LIB_NAME", m.get("LANEINFO_LIB_NAME"));
            mmmp.put("LIB_TYPE", m.get("LANEINFO_LIB_TYPE"));
            mmmp.put("LIBRARY_NAME", m.get("LANEINFO_LIBRARY_NAME"));
            mmmp.put("LIMSLINE", m.get("LANEINFO_LIMSLINE"));
            mmmp.put("NO", m.get("LANEINFO_NO"));
            mmmp.put("POOL_LIB_DATA", m.get("LANEINFO_POOL_LIB_DATA"));
            mmmp.put("PROJ_TYPE", m.get("LANEINFO_PROJ_TYPE"));
            mmmp.put("PROJECT_CODE", m.get("LANEINFO_PROJECT_CODE"));
            mmmp.put("SAMPLE", m.get("LANEINFO_SAMPLE"));
            mmmp.put("SMPL_CODE", m.get("LANEINFO_SMPL_CODE"));
            liList.add(mmmp);
            
            String dbMail = m.get("MAIL") == null ? "" : (String)m.get("MAIL");
            String mapMail = mmap.get("MAIL") == null ? "" : (String)mmap.get("MAIL");
            if(StringUtils.isNotBlank(dbMail)) {
                if(StringUtils.isNotBlank(mapMail)) {
                    mapMail = String.join(",",mapMail, dbMail);
                }else {
                    mapMail = dbMail;
                }
            }
            mmap.put("MAIL", mapMail);
            
            List<Map<String,Object>> prjList = (List<Map<String, Object>>) mmap.get("PROJECTINFO");
            mmmp = new HashMap<String,Object>();
            mmmp.put("MAIL", m.get("PROJECTINFO_MAIL"));
            mmmp.put("PROJ_TYPE", m.get("PROJECTINFO_PROJ_TYPE"));
            mmmp.put("PROJECT_CODE", m.get("PROJECTINFO_PROJECT_CODE"));
            mmmp.put("PROJECT_NAME", m.get("PROJECTINFO_PROJECT_NAME"));
            mmmp.put("REF", m.get("PROJECTINFO_REF"));
            mmmp.put("USER", m.get("PROJECTINFO_USER"));
            prjList.add(mmmp);
            
        }
        rs.setDATA(map);
        return rs;
    }
    
    public ResultSet addNIPTLaneInfo(Map<String,String> data) {
        ResultSet rs = new ResultSet();
        apiDao.addNIPTLaneInfo(data);
        rs.setDATA("");
        return rs;
    }
    
    
    public ResultSet addOnconiCQData(Map<String,String> data) {
        ResultSet rs = new ResultSet();
        apiDao.addOnconiCQData(data);
        rs.setDATA("");
        return rs;
    }
    
    public ResultSet getTGSQCTask() {
        ResultSet rs = new ResultSet();
        List<Map<String,Object>> list = apiDao.getTGSQCTask("NotQC");
        rs.setDATA(list);
        return rs;
    }
    
    public ResultSet modifyTGSTaskStatus(List<Map<String,String>> data) {
        ResultSet rs = new ResultSet();
        apiDao.modifyTGSTaskStatus(data);
        rs.setDATA("");
        return rs;
    }
    
    
    public ResultSet writeTGSQCResult(Map<String,String> data) {
        ResultSet rs = new ResultSet();
        apiDao.writeTGSQCResult(data);
        rs.setDATA("");
        return rs;
    }
    
    public ResultSet getFlowcellsData(String flowcell) {
        ResultSet rs = new ResultSet();
        List<Map<String,Object>> list = apiDao.getFlowcellsData(flowcell);
        rs.setDATA(list);
        return rs;
    }
    
    public ResultSet saveWESData(Map<String,String> data) {
        ResultSet rs = new ResultSet();
        apiDao.saveWESData(data);
        rs.setDATA("");
        return rs;
    }
    
}
