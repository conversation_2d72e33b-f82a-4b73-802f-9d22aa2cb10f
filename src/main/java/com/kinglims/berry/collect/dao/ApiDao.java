package com.kinglims.berry.collect.dao;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service
public class ApiDao {
    
    @Autowired
    private JdbcTemplate jt;
    
    //API_AllOnconiCQInfo
    public List<Map<String,Object>>  getAllOnconiCQInfo(String id) {
        List<Map<String,Object>> list =  jt.queryForList("select * from API_ALLONCONICQINFO where id = ?",id);
        return list;
    }
    
    public void addNIPTLaneInfo(Map<String,String> data) {
        jt.update("INSERT INTO  API_NIPTLANEINFO (CLEAN_BASES, HTML_FILE, RAW_READS, CLEAN_Q30, INFOENDDATE, MACHINE_NUMBER,"
                + " CLUSTERS, LANE_NUMBER, DQ, FLOWCELL, RAW_Q30, LIB_NAME, MACHINE_DATE, CLEAN_READS, ID, PF, MACHINE_TYPE,"
                + " CLEAN, UNDETERMINED, LENGTH, LANE_BUSINESS_TYPE)"
                + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                data.get("CLEAN_BASES"),
                data.get("HTML_FILE"),
                data.get("RAW_READS"),
                data.get("CLEAN_Q30"),
                data.get("INFOENDDATE"),
                data.get("MACHINE_NUMBER"),
                data.get("CLUSTERS"),
                data.get("LANE_NUMBER"),
                data.get("DQ"),
                data.get("FLOWCELL"),
                data.get("RAW_Q30"),
                data.get("LIB_NAME"),
                data.get("MACHINE_DATE"),
                data.get("CLEAN_READS"),
                data.get("ID"),
                data.get("PF"),
                data.get("MACHINE_TYPE"),
                data.get("CLEAN"),
                data.get("UNDETERMINED"),
                data.get("LENGTH"),
                data.get("LANE_BUSINESS_TYPE")
                );
    }
    
    public void addOnconiCQData(Map<String,String> data) {
        
        jt.update("INSERT INTO  API_ONCONICQDATA (ALIGN_PE, RRNA, MAPPED, RAWGCDIFF, CLEAN_BASES, CLEANGCDIFF, "
                + "HTML_FILE, RAW_READS, GC_PERCENT, INFOENDDATE, MACHINE_NUMBER, RAWQ30, REFERENCE, DOWNTIME,"
                + " Q30_PERCENT, HIGHQUALITY, MACHINE_DATE, FIGURE_FILE, RAW_BASES, MACHINE_TYPE, READ_LENGTH,"
                + " PAIRED, SOLEXA_DATA_DQ, HIGHQUALITY31, POOL_LIB_DATA, ERROR_PERCENT_REGEUST, CHIP_NUMBER,"
                + " RAWQ20, DUPLICATION, ADAPTER_RATIO, Q20_PERCENT, CLEAN_READS, INSTER_SIZE_SD, BUSINESS_TYPE,"
                + " LANE, CLEAN_RATIO, INSTER_SIZE, INDEX_NUM) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                data.get("ALIGN_PE"),
                data.get("RRNA"),
                data.get("MAPPED"),
                data.get("RAWGCDIFF"),
                data.get("CLEAN_BASES"),
                data.get("CLEANGCDIFF"),
                data.get("HTML_FILE"),
                data.get("RAW_READS"),
                data.get("GC_PERCENT"),
                data.get("INFOENDDATE"),
                data.get("MACHINE_NUMBER"),
                data.get("RAWQ30"),
                data.get("REFERENCE"),
                data.get("DOWNTIME"),
                data.get("Q30_PERCENT"),
                data.get("HIGHQUALITY"),
                data.get("MACHINE_DATE"),
                data.get("FIGURE_FILE"),
                data.get("RAW_BASES"),
                data.get("MACHINE_TYPE"),
                data.get("READ_LENGTH"),
                data.get("PAIRED"),
                data.get("SOLEXA_DATA_DQ"),
                data.get("HIGHQUALITY31"),
                data.get("POOL_LIB_DATA"),
                data.get("ERROR_PERCENT_REGEUST"),
                data.get("CHIP_NUMBER"),
                data.get("RAWQ20"),
                data.get("DUPLICATION"),
                data.get("ADAPTER_RATIO"),
                data.get("Q20_PERCENT"),
                data.get("CLEAN_READS"),
                data.get("INSTER_SIZE_SD"),
                data.get("BUSINESS_TYPE"),
                data.get("LANE"),
                data.get("CLEAN_RATIO"),
                data.get("INSTER_SIZE"),
                data.get("INDEX_NUM")
                );
    }
    
    public List<Map<String,Object>> getTGSQCTask(String QCTASKSTATUS) {
        List<Map<String,Object>> list =  jt.queryForList("select * from API_TGSQCTASK where QCTASKSTATUS = ?",QCTASKSTATUS);
        return list;
    }
    
    
    public void modifyTGSTaskStatus(List<Map<String,String>> data) {
        jt.batchUpdate("UPDATE  API_TGSTASKSTATUS SET QCTASKSTATUS = ? WHERE RUNNAME = ? AND WELLID = ? AND EXPERIMENTID = ?",
        new BatchPreparedStatementSetter() {
            
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setString(1, data.get(i).get("QCTASKSTATUS"));
                ps.setString(2, data.get(i).get("RUNNAME"));
                ps.setString(3, data.get(i).get("WELLID"));
                ps.setString(4, data.get(i).get("EXPERIMENTID"));
            }
            
            @Override
            public int getBatchSize() {
                return data.size();
            }
        });
    }
    
    
    public void writeTGSQCResult(Map<String,String> data) {
        jt.update("INSERT INTO  API_TGSQCRESULT (UMY, CREATOR, FIGURE_FILE, SUBREADSN50, EXPERIMENTID, "
                + "P2, MEANPOLYMERASEREADLENGTH, RUNNAME, LASTUPDATOR, P1, P0, LIBRARY, SUBREADSMEANLEN, "
                + "SUBREADSBASES, POLYMERASEREADN50, MAPPINGRATE, SUBREADSNUMBER, HTML_FILE, DATALOSSRATIO, "
                + "TOTALBASES, INSERTSIZE, POLYMERASEREADS, WELLID, THAN10KSUBREADS)"
                + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                data.get("UMY"),
                data.get("CREATOR"),
                data.get("FIGURE_FILE"),
                data.get("SUBREADSN50"),
                data.get("EXPERIMENTID"),
                data.get("P2"),
                data.get("MEANPOLYMERASEREADLENGTH"),
                data.get("RUNNAME"),
                data.get("LASTUPDATOR"),
                data.get("P1"),
                data.get("P0"),
                data.get("LIBRARY"),
                data.get("SUBREADSMEANLEN"),
                data.get("SUBREADSBASES"),
                data.get("POLYMERASEREADN50"),
                data.get("MAPPINGRATE"),
                data.get("SUBREADSNUMBER"),
                data.get("HTML_FILE"),
                data.get("DATALOSSRATIO"),
                data.get("TOTALBASES"),
                data.get("INSERTSIZE"),
                data.get("POLYMERASEREADS"),
                data.get("WELLID"),
                data.get("THAN10KSUBREADS")
                );
    }
    
    
    public List<Map<String,Object>>  getFlowcellsData(String flowcell) {
        List<Map<String,Object>> list =  jt.queryForList("select * from API_FLOWCELLSDATA where FLOWCELLS = ?",flowcell);
        return list;
    }
    
    public void saveWESData(Map<String,String> data) {
        jt.update("INSERT INTO  API_WESDATA(FLOWCELL_SAMPLE, QC_STATUS, CLEAN_BASES, CLEANQ30, CLEAN_GC, "
                + "AVG_SEQDEPTHON_TARGET, IQR, FRACTION_COVEREDT_4X, PCT_TARGET_BASES_20X, DUPLICATE_READS, "
                + "AVG_INSERT_SIZE, FEB_ON_TARGE, CHECK_SEX, CHECK_TRIO, TOTAL_SNP, TI_TV, HET, MT_MEAN_DEPTH, "
                + "MT_20X, FEB_ON_TARGET) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                data.get("FLOWCELL_SAMPLE"),
                data.get("QC_STATUS"),
                data.get("CLEAN_BASES"),
                data.get("CLEANQ30"),
                data.get("CLEAN_GC"),
                data.get("AVG_SEQDEPTHON_TARGET"),
                data.get("IQR"),
                data.get("FRACTION_COVEREDT_4X"),
                data.get("PCT_TARGET_BASES_20X"),
                data.get("DUPLICATE_READS"),
                data.get("AVG_INSERT_SIZE"),
                data.get("FEB_ON_TARGE"),
                data.get("CHECK_SEX"),
                data.get("CHECK_TRIO"),
                data.get("TOTAL_SNP"),
                data.get("TI_TV"),
                data.get("HET"),
                data.get("MT_MEAN_DEPTH"),
                data.get("MT_20X"),
                data.get("FEB_ON_TARGET")
                );
    }
}
