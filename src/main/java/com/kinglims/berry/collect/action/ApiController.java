package com.kinglims.berry.collect.action;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kinglims.berry.collect.ResultSet;
import com.kinglims.berry.collect.service.ApiService;

@Controller
public class ApiController {

    @Autowired
    private ApiService apisvr;

    @Autowired
    private ObjectMapper om;

    // {"FLOWCELL": "AHWWMHDSXX"}
    @PostMapping(value = "/berry/Jrest/niptRestService/getAllOnconiCQInfo")
    @ResponseBody
    public ResultSet getAllOnconiCQInfo(@RequestBody String param) {
        System.out.println("requestbody --" + param);
        JsonNode jn;
        ResultSet rs = null;
        try {
            jn = om.readTree(param);
            rs = apisvr.getAllOnconiCQInfo(jn.get("FLOWCELL").asText());
            rs.setMESSAGE("查询成功！");
            rs.setSTATUS("SUCCESS");
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return rs;
    }

    @PostMapping(value = "/berry/Jrest/niptRestService/getNIPTLaneInfo")
    @ResponseBody
    public ResultSet getNIPTLaneInfo(@RequestBody String param) {
        System.out.println("requestbody --" + param);
        ResultSet rs = null;
        try {
            JsonNode jn = om.readTree(param);
            Map<String, String> map = new HashMap<String, String>();
            Iterator<String> iter = jn.fieldNames();
            while (iter.hasNext()) {
                String name = iter.next();
                JsonNode j = jn.get(name);
                String value = j.asText();
                map.put(name, value);
            }
            rs = apisvr.addNIPTLaneInfo(map);
            rs.setMESSAGE("查询成功！");
            rs.setSTATUS("SUCCESS");
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return rs;
    }

    @PostMapping(value = "/berry/Jrest/niptRestService/getOnconiCQData")
    @ResponseBody
    public ResultSet getOnconiCQData(@RequestBody String param) {
        System.out.println("requestbody --" + param);
        ResultSet rs = null;
        try {
            JsonNode jn = om.readTree(param);
            Map<String, String> map = new HashMap<String, String>();
            Iterator<String> iter = jn.fieldNames();
            while (iter.hasNext()) {
                String name = iter.next();
                JsonNode j = jn.get(name);
                String value = j.asText();
                map.put(name, value);
            }
            rs = apisvr.addOnconiCQData(map);
            rs.setMESSAGE("查询成功！");
            rs.setSTATUS("SUCCESS");
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return rs;
    }

    // QCTASKSTATUS is NotQC
    @PostMapping(value = "/berry/Jrest/TSRestService/getTGSQCTask")
    @ResponseBody
    public ResultSet getTGSQCTask(@RequestBody String param) {
        System.out.println("requestbody --" + param);
        ResultSet rs = null;
        rs = apisvr.getTGSQCTask();
        rs.setMESSAGE("查询成功！");
        rs.setSTATUS("SUCCESS");
        return rs;
    }

    @PostMapping(value = "/berry/Jrest/TSRestService/modifyTGSTaskStatus")
    @ResponseBody
    public ResultSet modifyTGSTaskStatus(@RequestBody String param) {
        System.out.println("requestbody --" + param);
        ResultSet rs = null;
        try {
            // "QCTASKSTATUS":"1",
            // "RUNNAME":"2",
            // "WELLID":"3",
            // "EXPERIMENTID":"4"
            JsonNode jn = om.readTree(param);
            Iterator<JsonNode> iter = jn.iterator();
            List<Map<String, String>> list = new ArrayList<Map<String, String>>();
            while (iter.hasNext()) {
                JsonNode j = iter.next();
                Iterator<String> fields = j.fieldNames();
                Map<String, String> map = new HashMap<String, String>();
                while (fields.hasNext()) {
                    String name = fields.next();
                    JsonNode jj = j.get(name);
                    String value = jj.asText();
                    map.put(name, value);
                }
                list.add(map);
            }
            rs = apisvr.modifyTGSTaskStatus(list);
            rs.setMESSAGE("查询成功！");
            rs.setSTATUS("SUCCESS");
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return rs;
    }

    @PostMapping(value = "/berry/Jrest/TSRestService/writeTGSQCResult")
    @ResponseBody
    public ResultSet writeTGSQCResult(@RequestBody String param) {
        System.out.println("requestbody --" + param);
        ResultSet rs = null;
        try {
            JsonNode jn = om.readTree(param);
            Map<String, String> map = new HashMap<String, String>();
            Iterator<String> iter = jn.fieldNames();
            while (iter.hasNext()) {
                String name = iter.next();
                JsonNode j = jn.get(name);
                String value = j.asText();
                map.put(name, value);
            }
            rs = apisvr.writeTGSQCResult(map);
            rs.setMESSAGE("查询成功！");
            rs.setSTATUS("SUCCESS");
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return rs;
    }

    @PostMapping(value = "/berry/Jrest/TsWesLimsService/getFlowcellsData")
    @ResponseBody
    public ResultSet getFlowcellsData(@RequestBody String param) {
        System.out.println("requestbody --" + param);
        String flowcell = "";
        if (StringUtils.isNotBlank(param)) {
            if (StringUtils.contains(param, "/")) {
                flowcell = param.substring(0, param.indexOf("/"));
            } else {
                flowcell = param;
            }
        }
        ResultSet rs = null;
        rs = apisvr.getFlowcellsData(flowcell);
        rs.setMESSAGE("查询成功！");
        rs.setSTATUS("SUCCESS");

        return rs;
    }

    @PostMapping(value = "/berry/Jrest/TsWesLimsService/saveWESData")
    @ResponseBody
    public ResultSet saveWESData(@RequestBody String param) {
        System.out.println("requestbody --" + param);
        ResultSet rs = null;
        try {
            JsonNode jn = om.readTree(param);
            Map<String, String> map = new HashMap<String, String>();
            Iterator<String> iter = jn.fieldNames();
            while (iter.hasNext()) {
                String name = iter.next();
                JsonNode j = jn.get(name);
                if (j.isArray()) {
                    String value = "";
                    for (JsonNode jjn : j) {
                        value = String.join(",", value, jjn.asText());
                    }
                    value = value.replaceFirst(",", "");
                    map.put(name, value);
                } else {
                    String value = j.asText();
                    map.put(name, value);
                }
            }
            rs = apisvr.saveWESData(map);
            rs.setMESSAGE("查询成功！");
            rs.setSTATUS("SUCCESS");
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return rs;
    }
}
